const { getESLintConfig } = require('@iceworks/spec');

module.exports = getESLintConfig('react-ts', {
  extends: ['eslint-config-ali/typescript/react', 'eslint-config-prettier', 'plugin:prettier/recommended'],
  rules: {
    eqeqeq: 'off',
    'no-console': 'off',
    'no-debugger': 'off',
    'no-param-reassign': 'off',
    'no-nested-ternary': 'off',
    'react-hooks/exhaustive-deps': 'off',
    'react/prop-types': 'off',
    'react/no-danger': 'off',
    'react/no-array-index-key': 'off',
    'react-hooks/rules-of-hooks': 'off',
    '@typescript-eslint/restrict-plus-operands': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@iceworks/best-practices/recommend-functional-component': 'off',
    '@iceworks/best-practices/recommend-polyfill': 'off',
    '@iceworks/best-practices/no-http-url': 'off',
    '@typescript-eslint/no-invalid-void-type': 'off',
    '@typescript-eslint/no-require-imports': 'off',
    "endOfLine": "auto",
    // complexity: ['error', 10],
  },
});
