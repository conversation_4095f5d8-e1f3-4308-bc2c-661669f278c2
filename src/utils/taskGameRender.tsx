import React from 'react';
import dayjs from 'dayjs';

// 任务类型1
export const TASK_TYPE = {
  1: '关注店铺',
  2: '浏览店铺',
  3: '浏览商品',
  4: '浏览会场/直播',
  5: '关注商品',
  6: '预约商品',
  7: '加购商品',
  8: '购买商品',
  9: '分享商品',
  10: '分享店铺',
  11: '分享会场',
  12: '分享活动',
  13: '会员开卡',
  14: '每日签到',
  15: '邀请助力',
  16: '每日组队',
  17: '活动提醒',
  18: '领取优惠券',
  20: '初始赠送',
  21: '积分兑换',
  22: '购买指定收货时间商品',
  23: '商品下单',
  24: '每日赠送',
};
// 渲染任务内容
// eslint-disable-next-line complexity
export const renderTaskContent = (val, index, row): any => {
  switch (row.taskType) {
    case 1:
      return <div>{`成功关注店铺`}</div>;
    case 2:
      return <div>{`成功浏览${row.perOperateCount}次店铺`}</div>;
    case 3:
      return <div>{`成功浏览${row.perOperateCount}件商品`}</div>;
    case 4:
      return <div>{`成功浏览${row.perOperateCount}会场/直播`}</div>;
    case 5:
      return (
        <div>{`${row.optWay === 1 ? `成功关注${row.perOperateCount}件商品(逐件)` : '成功关注全部商品(一键)'}`}</div>
      );
    case 6:
      return <div>{`成功预约${row.perOperateCount}件商品`}</div>;
    case 7:
      return (
        <div>{`${row.optWay === 1 ? `成功加购${row.perOperateCount}件商品(逐件)` : '成功加购全部商品(一键)'}`}</div>
      );
    case 8:
      return (
        <div>{`${
          row.buyType === 1 ? `成功购买${row.perOperateCount}件全店商品` : `成功购买${row.perOperateCount}件指定商品`
        }`}</div>
      );
    case 9:
      return <div>{`成功分享${row.perOperateCount}次商品`}</div>;
    case 10:
      return <div>{`成功分享${row.perOperateCount}次店铺`}</div>;
    case 12:
      return <div>{`成功分享${row.perOperateCount}次活动`}</div>;
    case 13:
      return <div>{`成功开卡`}</div>;
    case 14:
      return <div>{`成功签到`}</div>;
    case 15:
      return <div>{`成功邀请${row.perOperateCount}位好友助力`}</div>;
    default:
      return <div>-</div>;
  }
};
// 渲染任务限制
// eslint-disable-next-line complexity
export const renderTaskLength = (val, index, row): any => {
  switch (row.taskType) {
    case 1:
      return <div>{`${row.taskTimeLimitWay === 1 ? '活动期间' : '指定时间内'}最多完成1次`}</div>;
    case 2:
      return <div>{`${row.taskTimeLimitWay === 1 ? '每日' : '指定时间内每日'}最多完成${row.taskLength || 1}次`}</div>;
    case 3:
      return <div>{`${row.taskTimeLimitWay === 1 ? '每日' : '指定时间内每日'}最多完成${row.taskLength || 1}次`}</div>;
    case 4:
      return <div>{`${row.taskTimeLimitWay === 1 ? '每日' : '指定时间内每日'}最多完成${row.taskLength || 1}次`}</div>;
    case 5:
    case 6:
      return <div>{`${row.taskTimeLimitWay === 1 ? '活动期间' : '指定时间内'}最多完成${row.taskLength || 1}次`}</div>;
    case 7:
      return <div>{`${row.taskTimeLimitWay === 1 ? '活动期间' : '指定时间内'}最多完成${row.taskLength || 1}次`}</div>;
    case 13:
      return <div>{`${row.taskTimeLimitWay === 1 ? '活动期间' : '指定时间内'}最多完成1次`}</div>;
    case 14:
      return <div>{`${row.taskTimeLimitWay === 1 ? '每日' : '指定时间内每日'}最多完成1次`}</div>;
    case 15:
      return <div>{`${row.taskTimeLimitWay === 1 ? '活动期间' : '指定时间内'}不限制`}</div>;
    case 9:
    case 10:
    case 12:
      return (
        <div>{`${row.taskUpperLimitWay === 1 ? '活动期间内' : '活动期间内每日'}最多完成${
          row.taskLength ? row.taskLength : 1
        }次`}</div>
      );
    case 8:
      return <div>{`活动期间不限制`}</div>;
    default:
      return <div>-</div>;
  }
};
// 渲染任务奖励
// eslint-disable-next-line complexity
export const renderTaskGift = (val, index, row): any => {
  switch (row.taskType) {
    case 5:
    case 7:
      return <div>{`${row.optWay === 1 ? row.perLotteryCount : row.lotteryCount}次游戏机会`}</div>;
    case 1:
    case 2:
    case 3:
    case 4:
    case 6:
    case 8:
    case 9:
    case 10:
    case 12:
    case 13:
    case 14:
    case 15:
      return <div>{`${row.perLotteryCount}次游戏机会`}</div>;
    default:
      return <div>-</div>;
  }
};
export const renderTaskDate = (val, index, row): any => {
  switch (row.taskType) {
    case 5:
    case 7:
    case 1:
    case 2:
    case 3:
    case 4:
    case 6:
    case 8:
    case 9:
    case 10:
    case 12:
    case 13:
    case 14:
    case 15:
      return (
        <div>{`${
          row.taskTimeLimitWay === 1
            ? '--'
            : `${dayjs(row.taskStartTime).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(row.taskEndTime).format(
                'YYYY-MM-DD HH:mm:ss',
              )}`
        }`}</div>
      );
    default:
      return <div>-</div>;
  }
};
