/// <reference lib="dom" />

import constant from './constant';
import { isJSONFormat } from './index';
import { isInIcestark } from '@ice/stark-app';
import { event } from '@ice/stark-data';
import { APP_MODE, history } from 'ice';

/**
 * 没有授权到登录页 from参数 告诉登录组件结束登录之后需要后退回页面
 */
export const noAuthHandler = () => {
  if (isInIcestark()) {
    const from = window.location.pathname + window.location.search;
    if (localStorage.getItem(constant.LZ_SSO_PRD) === 'cjhy') {
      if (APP_MODE === 'prod') {
        window.parent.location.href = `https://jdcjhy.dianpusoft.cn/cjhyIndex`;
      } else {
        window.parent.location.href = `https://cjhyb-test.dianpusoft.cn/cjhyIndex`;
      }
    } else if (localStorage.getItem(constant.LZ_SSO_PRD) === 'cjwx') {
      window.location.href = `https://${window.location.hostname}/new-interact/`;
    } else if (localStorage.getItem(constant.LZ_SSO_PRD) === 'cjzd') {
      if (APP_MODE === 'prod') {
        window.location.href = `https://${window.location.hostname}/cjzd-new/`;
      } else {
        window.location.href = `http://${window.location.hostname}/cjzd-new/`;
      }
    } else {
      event.emit(constant.LZ_EVENT_SSO_TOKEN_EXPIRE, encodeURIComponent(from));
    }
    console.log(`sub app interaction emit event ${constant.LZ_EVENT_SSO_TOKEN_EXPIRE}`);
  } else {
    console.log(constant.LZ_AUTH_ERROR_MESSAGE);
    history?.replace(constant.LZ_LOGIN_PAGE_URL);
  }
};

export const getUser = () => {
  const lzCrmBizUserStr = localStorage.getItem(constant.LZ_CRM_BIZ_USER);
  if (lzCrmBizUserStr && isJSONFormat(lzCrmBizUserStr)) {
    return JSON.parse(lzCrmBizUserStr);
  }
  return noAuthHandler();
};

/**
 * 获取当前登录用户名
 */
export const getUserName = () => {
  const user = getUser();
  if (user) {
    return encodeURIComponent(user.account);
  }
  return '';
};

/**
 * 获取当前登录店铺
 */
export const getShop = () => {
  // 获取当前登录店铺
  const lzCurrentShopStr = localStorage.getItem(constant.LZ_CURRENT_SHOP);
  if (lzCurrentShopStr && isJSONFormat(lzCurrentShopStr)) {
    return JSON.parse(lzCurrentShopStr);
  }
  return noAuthHandler();
};

/**
 * 获取当前登录店铺id
 */
export const getShopId = () => {
  const shop = getShop();
  if (shop) {
    return shop.shopId;
  }
  return null;
};

/**
 * 获取当前登录店铺名称
 */
export const getShopName = () => {
  const shop = getShop();
  if (shop) {
    return shop.shopName;
  }
  return null;
};
/**
 * 获取当前版本
 */
export const getVersionByData = () => {
  // 旧版 1 平台专业版 2 数据旗舰版
  // 新版 在localStorage LZ_CURRENT_SHOP 5 平台专业版 6 数据旗舰版
  return (
    JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') as string)?.versionNo == '6' ||
    JSON.parse(sessionStorage.getItem('shopInfoYB') as string)?.version == '2'
  );
};
/**
 * 获取店铺的超级营销过期时间
 */
export const getMarketExpireTime = (): number | void => {
  // 获取当前登录店铺
  const lzCurrentShopStr = localStorage.getItem(constant.LZ_CURRENT_SHOP);
  if (lzCurrentShopStr && isJSONFormat(lzCurrentShopStr)) {
    console.log('lzCurrentShopStr', isJSONFormat(lzCurrentShopStr));
    const shop = JSON.parse(lzCurrentShopStr);
    console.log('shop', shop);
    const { currentShop } = shop;
    const { marketEndTime } = currentShop;
    console.log('marketEndTime', marketEndTime);
    return marketEndTime;
  }
  return noAuthHandler();
};
