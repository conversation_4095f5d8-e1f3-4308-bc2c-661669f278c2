import {
  Activity39002ActivityDataRequest,
  Activity39002CreateOrUpdateRequest,
  Activity39002CreateOrUpdateResponse,
  Activity39002UpdateOrderRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity39002UserExchangeLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 三元定制-积分兑换
 * @summary 创建活动
 * @request POST:/39002/createActivity
 */
export const createActivity = (
  request: Activity39002CreateOrUpdateRequest,
): Promise<Activity39002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/39002/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 三元定制积分兑换数据
 * @summary 上传人群包
 * @request POST:/39002/data/exchange/uploadPin
 */
export const dataExchangeUploadPin = (
  activity39002ActivityDataRequest: Activity39002ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/39002/data/exchange/uploadPin',
    method: 'post',
    data: activity39002ActivityDataRequest,
  });
};

/**
 * @tags 三元定制积分兑换数据
 * @summary 中奖记录
 * @request POST:/39002/data/exchangeLog
 */
export const dataExchangeLog = (
  activity39002ActivityDataRequest: Activity39002ActivityDataRequest,
): Promise<IPageActivity39002UserExchangeLogResponse> => {
  return httpRequest({
    url: '/39002/data/exchangeLog',
    method: 'post',
    data: activity39002ActivityDataRequest,
  });
};

/**
 * @tags 三元定制积分兑换数据
 * @summary 中奖记录导出
 * @request POST:/39002/data/exchangeLog/export
 */
export const dataExchangeLogExport = (
  activity39002ActivityDataRequest: Activity39002ActivityDataRequest,
): Promise<void> => {
  return httpRequest({
    url: '/39002/data/exchangeLog/export',
    method: 'post',
    data: activity39002ActivityDataRequest,
  });
};

/**
 * @tags 三元定制积分兑换数据
 * @summary 更新订单状态
 * @request POST:/39002/data/updataOrder
 */
export const dataUpdataOrder = (request: Activity39002UpdateOrderRequest): Promise<void> => {
  return httpRequest({
    url: '/39002/data/updataOrder',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 三元定制-积分兑换
 * @summary 查询活动信息
 * @request POST:/39002/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/39002/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 三元定制-积分兑换
 * @summary 修改活动
 * @request POST:/39002/updateActivity
 */
export const updateActivity = (
  request: Activity39002CreateOrUpdateRequest,
): Promise<Activity39002CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/39002/updateActivity',
    method: 'post',
    data: request,
  });
};
