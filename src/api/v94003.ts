import {
  Activity94003CreateOrUpdateRequest,
  Activity94003CreateOrUpdateResponse,
  Activity94003OrderDataRequest,
  Activity94003UserDetailRequest,
  Activity94003UserDetailResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity94003OrderDataResponse,
  IPageBdActivity94003TokenLog,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags  会员锁权礼
 * @summary 创建活动
 * @request POST:/94003/createActivity
 */
export const createActivity = (
  request: Activity94003CreateOrUpdateRequest,
): Promise<Activity94003CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/94003/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员锁权礼数据
 * @summary 订单记录
 * @request POST:/94003/data/orderRecord
 */
export const dataOrderRecord = (
  request: Activity94003OrderDataRequest,
): Promise<IPageActivity94003OrderDataResponse> => {
  return httpRequest({
    url: '/94003/data/orderRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员锁权礼数据
 * @summary 导出记录
 * @request POST:/94003/data/orderRecord/export
 */
export const dataOrderRecordExport = (request: Activity94003OrderDataRequest): Promise<void> => {
  return httpRequest({
    url: '/94003/data/orderRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员锁权礼数据
 * @summary 令牌记录
 * @request POST:/94003/data/tokenLogRecord
 */
export const dataTokenLogRecord = (request: Activity94003OrderDataRequest): Promise<IPageBdActivity94003TokenLog> => {
  return httpRequest({
    url: '/94003/data/tokenLogRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员锁权礼数据
 * @summary 导出记录
 * @request POST:/94003/data/tokenLogRecord/export
 */
export const dataTokenLogRecordExport = (request: Activity94003OrderDataRequest): Promise<void> => {
  return httpRequest({
    url: '/94003/data/tokenLogRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员锁权礼数据
 * @summary 导出记录（旧）
 * @request POST:/94003/data/tokenLogRecordOld/export
 */
export const dataTokenLogRecordOldExport = (request: Activity94003OrderDataRequest): Promise<void> => {
  return httpRequest({
    url: '/94003/data/tokenLogRecordOld/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员锁权礼数据
 * @summary 用户记录详情
 * @request POST:/94003/data/userDetailRecord
 */
export const dataUserDetailRecord = (
  activity94003UserDetailRequest: Activity94003UserDetailRequest,
): Promise<Activity94003UserDetailResponse> => {
  return httpRequest({
    url: '/94003/data/userDetailRecord',
    method: 'post',
    data: activity94003UserDetailRequest,
  });
};

/**
 * @tags  会员锁权礼
 * @summary 查询活动信息
 * @request POST:/94003/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/94003/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags  会员锁权礼
 * @summary 查询活动参与人数信息
 * @request POST:/94003/getPrizeUserInfo
 */
export const getPrizeUserInfo = (request: BaseGetActivityRequest): Promise<string> => {
  return httpRequest({
    url: '/94003/getPrizeUserInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags  会员锁权礼
 * @summary 查询活动总期数
 * @request POST:/94003/num
 */
export const num = (request: BaseGetActivityRequest): Promise<string> => {
  return httpRequest({
    url: '/94003/num',
    method: 'post',
    data: request,
  });
};

/**
 * @tags  会员锁权礼
 * @summary 修改活动
 * @request POST:/94003/updateActivity
 */
export const updateActivity = (
  request: Activity94003CreateOrUpdateRequest,
): Promise<Activity94003CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/94003/updateActivity',
    method: 'post',
    data: request,
  });
};
