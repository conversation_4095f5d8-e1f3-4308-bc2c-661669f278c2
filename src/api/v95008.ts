import {
  Activity95008CreateOrUpdateRequest,
  Activity95008CreateOrUpdateResponse,
  Activity95008DataRequest,
  IPageBdActivity95008DataResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 集卡有礼
 * @summary 创建活动
 * @request POST:/95008/createActivity
 */
export const createActivity = (
  request: Activity95008CreateOrUpdateRequest,
): Promise<Activity95008CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/95008/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集卡有礼数据
 * @summary 获奖记录
 * @request POST:/95008/data/applyList
 */
export const dataApplyList = (request: Activity95008DataRequest): Promise<IPageBdActivity95008DataResponse> => {
  return httpRequest({
    url: '/95008/data/applyList',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集卡有礼数据
 * @summary 导出记录
 * @request POST:/95008/data/applyList/export
 */
export const dataApplyListExport = (request: Activity95008DataRequest): Promise<void> => {
  return httpRequest({
    url: '/95008/data/applyList/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 集卡有礼
 * @summary 修改活动
 * @request POST:/95008/updateActivity
 */
export const updateActivity = (
  request: Activity95008CreateOrUpdateRequest,
): Promise<Activity95008CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/95008/updateActivity',
    method: 'post',
    data: request,
  });
};
