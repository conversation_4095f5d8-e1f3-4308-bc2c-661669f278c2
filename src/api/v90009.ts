import {
  Activity90009CreateOrUpdateRequest,
  Activity90009CreateOrUpdateResponse,
  Activity90009OrderRequest,
  Activity90009OrderResponse,
  Activity90009PartakeRecordPageRequest,
  Activity90009SkuImportResponse,
  Activity90009UserPrizeRecordPageRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity90009PartakeRecordPageResponse,
  IPageActivity90009UserPrizeRecordPageResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 美素佳儿老客入会礼
 * @summary 创建活动
 * @request POST:/90009/createActivity
 */
export const createActivity = (
  request: Activity90009CreateOrUpdateRequest,
): Promise<Activity90009CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90009/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 订单查询
 * @request POST:/90009/data/getOrder
 */
export const dataGetOrder = (request: Activity90009OrderRequest): Promise<Activity90009OrderResponse[]> => {
  return httpRequest({
    url: '/90009/data/getOrder',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 参与记录
 * @request POST:/90009/data/partakeLog
 */
export const dataPartakeLog = (
  request: Activity90009PartakeRecordPageRequest,
): Promise<IPageActivity90009PartakeRecordPageResponse> => {
  return httpRequest({
    url: '/90009/data/partakeLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 参与记录导出
 * @request POST:/90009/data/partakeLog/export
 */
export const dataPartakeLogExport = (request: Activity90009PartakeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90009/data/partakeLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 参与记录人群包
 * @request POST:/90009/data/partakeLog/uploadPin
 */
export const dataPartakeLogUploadPin = (request: Activity90009PartakeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90009/data/partakeLog/uploadPin',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 中奖记录
 * @request POST:/90009/data/winningLog
 */
export const dataWinningLog = (
  request: Activity90009UserPrizeRecordPageRequest,
): Promise<IPageActivity90009UserPrizeRecordPageResponse> => {
  return httpRequest({
    url: '/90009/data/winningLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 中奖记录导出
 * @request POST:/90009/data/winningLog/export
 */
export const dataWinningLogExport = (request: Activity90009UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90009/data/winningLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 复购有礼数据
 * @summary 中奖记录人群包
 * @request POST:/90009/data/winningLog/uploadPin
 */
export const dataWinningLogUploadPin = (request: Activity90009UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90009/data/winningLog/uploadPin',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 美素佳儿老客入会礼
 * @summary 查询活动信息
 * @request POST:/90009/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/90009/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 美素佳儿老客入会礼
 * @summary 导入系列信息excel
 * @request POST:/90009/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<Activity90009SkuImportResponse[]> => {
  return httpRequest({
    url: '/90009/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 美素佳儿老客入会礼
 * @summary 下载模板
 * @request POST:/90009/seriesTemplate/export
 */
export const seriesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/90009/seriesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 美素佳儿老客入会礼
 * @summary 修改活动
 * @request POST:/90009/updateActivity
 */
export const updateActivity = (
  request: Activity90009CreateOrUpdateRequest,
): Promise<Activity90009CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90009/updateActivity',
    method: 'post',
    data: request,
  });
};
