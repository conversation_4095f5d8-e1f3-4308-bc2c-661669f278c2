import {
  IPageZphyBdUserGiftRecordBizResponse,
  ZphyBdGlobalUserGiftRecordBizRequest,
  ZphyFuluRequest,
  ZphyFuluResponse,
  ZphyGiftInfoBizRequest,
  ZphyGiftInfoResponse,
  ZphyJsonRequest,
  ZphyJsonResponse,
  ZphyMemberLevelBizRequest,
  ZphyMemberLevelResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 智铺会员页面装修
 * @summary 获取飞鹤福禄奖品列表
 * @request POST:/zphy/getFuluList
 */
export const getFuluList = (fuluRequest: ZphyFuluRequest): Promise<ZphyFuluResponse[]> => {
  return httpRequest({
    url: '/zphy/getFuluList',
    method: 'post',
    data: fuluRequest,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 获取智铺会员页面装修奖品集合
 * @request POST:/zphy/getZphyGiftInfoList
 */
export const getZphyGiftInfoList = (): Promise<ZphyGiftInfoResponse[]> => {
  return httpRequest({
    url: '/zphy/getZphyGiftInfoList',
    method: 'post',
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 获取智铺会员页面装修json
 * @request POST:/zphy/getZphyJson
 */
export const getZphyJson = (request: ZphyJsonRequest): Promise<ZphyJsonResponse> => {
  return httpRequest({
    url: '/zphy/getZphyJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 获取会员等级
 * @request POST:/zphy/getZphyMemberLevel
 */
export const getZphyMemberLevel = (): Promise<ZphyMemberLevelResponse[]> => {
  return httpRequest({
    url: '/zphy/getZphyMemberLevel',
    method: 'post',
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 获取智铺会员页面装修json
 * @request POST:/zphy/getZphySmartCardJson
 */
export const getZphySmartCardJson = (request: ZphyJsonRequest): Promise<ZphyJsonResponse> => {
  return httpRequest({
    url: '/zphy/getZphySmartCardJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 获取智铺会员奖品兑换记录
 * @request POST:/zphy/gift/getGiftRecordList
 */
export const giftGetGiftRecordList = (
  globalUserGiftRecord: ZphyBdGlobalUserGiftRecordBizRequest,
): Promise<IPageZphyBdUserGiftRecordBizResponse> => {
  return httpRequest({
    url: '/zphy/gift/getGiftRecordList',
    method: 'post',
    data: globalUserGiftRecord,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 智铺会员奖品兑换记录导出
 * @request POST:/zphy/gift/giftRecord/export
 */
export const giftGiftRecordExport = (globalUserGiftRecord: ZphyBdGlobalUserGiftRecordBizRequest): Promise<void> => {
  return httpRequest({
    url: '/zphy/gift/giftRecord/export',
    method: 'post',
    data: globalUserGiftRecord,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 获取智铺会员pop店奖品兑换记录
 * @request POST:/zphy/pop/gift/getGiftRecordList
 */
export const popGiftGetGiftRecordList = (
  globalUserGiftRecord: ZphyBdGlobalUserGiftRecordBizRequest,
): Promise<IPageZphyBdUserGiftRecordBizResponse> => {
  return httpRequest({
    url: '/zphy/pop/gift/getGiftRecordList',
    method: 'post',
    data: globalUserGiftRecord,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 智铺会员pop店奖品兑换记录导出
 * @request POST:/zphy/pop/gift/giftRecord/export
 */
export const popGiftGiftRecordExport = (globalUserGiftRecord: ZphyBdGlobalUserGiftRecordBizRequest): Promise<void> => {
  return httpRequest({
    url: '/zphy/pop/gift/giftRecord/export',
    method: 'post',
    data: globalUserGiftRecord,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 获取飞鹤福禄奖品列表
 * @request POST:/zphy/pop/json/getFuluList
 */
export const popJsonGetFuluList = (fuluRequest: ZphyFuluRequest): Promise<ZphyFuluResponse[]> => {
  return httpRequest({
    url: '/zphy/pop/json/getFuluList',
    method: 'post',
    data: fuluRequest,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 获取智铺会员页面装修奖品集合
 * @request POST:/zphy/pop/json/getZphyGiftInfoList
 */
export const popJsonGetZphyGiftInfoList = (): Promise<ZphyGiftInfoResponse[]> => {
  return httpRequest({
    url: '/zphy/pop/json/getZphyGiftInfoList',
    method: 'post',
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 获取智铺会员页面装修json
 * @request POST:/zphy/pop/json/getZphyJson
 */
export const popJsonGetZphyJson = (request: ZphyJsonRequest): Promise<ZphyJsonResponse> => {
  return httpRequest({
    url: '/zphy/pop/json/getZphyJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 获取会员等级
 * @request POST:/zphy/pop/json/getZphyMemberLevel
 */
export const popJsonGetZphyMemberLevel = (): Promise<ZphyMemberLevelResponse[]> => {
  return httpRequest({
    url: '/zphy/pop/json/getZphyMemberLevel',
    method: 'post',
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 获取智铺会员页面装修json
 * @request POST:/zphy/pop/json/getZphySmartCardJson
 */
export const popJsonGetZphySmartCardJson = (request: ZphyJsonRequest): Promise<ZphyJsonResponse> => {
  return httpRequest({
    url: '/zphy/pop/json/getZphySmartCardJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 更新智铺会员页面装修奖品
 * @request POST:/zphy/pop/json/saveZphyGiftInfoList
 */
export const popJsonSaveZphyGiftInfoList = (zphyGiftInfoBizRequest: ZphyGiftInfoBizRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/pop/json/saveZphyGiftInfoList',
    method: 'post',
    data: zphyGiftInfoBizRequest,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 更新智铺会员页面装修json——smartCard
 * @request POST:/zphy/pop/json/saveZphyJson
 */
export const popJsonSaveZphyJson = (request: ZphyJsonRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/pop/json/saveZphyJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 更新会员等级
 * @request POST:/zphy/pop/json/saveZphyMemberLevel
 */
export const popJsonSaveZphyMemberLevel = (memberLevelRequest: ZphyMemberLevelBizRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/pop/json/saveZphyMemberLevel',
    method: 'post',
    data: memberLevelRequest,
  });
};

/**
 * @tags 智铺会员pop店页面装修
 * @summary 更新智铺会员页面装修json——smartCard
 * @request POST:/zphy/pop/json/saveZphySmartCardJson
 */
export const popJsonSaveZphySmartCardJson = (request: ZphyJsonRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/pop/json/saveZphySmartCardJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 更新智铺会员页面装修奖品
 * @request POST:/zphy/saveZphyGiftInfoList
 */
export const saveZphyGiftInfoList = (zphyGiftInfoBizRequest: ZphyGiftInfoBizRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/saveZphyGiftInfoList',
    method: 'post',
    data: zphyGiftInfoBizRequest,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 更新智铺会员页面装修json——smartCard
 * @request POST:/zphy/saveZphyJson
 */
export const saveZphyJson = (request: ZphyJsonRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/saveZphyJson',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 更新会员等级
 * @request POST:/zphy/saveZphyMemberLevel
 */
export const saveZphyMemberLevel = (memberLevelRequest: ZphyMemberLevelBizRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/saveZphyMemberLevel',
    method: 'post',
    data: memberLevelRequest,
  });
};

/**
 * @tags 智铺会员页面装修
 * @summary 更新智铺会员页面装修json——smartCard
 * @request POST:/zphy/saveZphySmartCardJson
 */
export const saveZphySmartCardJson = (request: ZphyJsonRequest): Promise<boolean> => {
  return httpRequest({
    url: '/zphy/saveZphySmartCardJson',
    method: 'post',
    data: request,
  });
};
