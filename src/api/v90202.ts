import {
  Activity90202CreateOrUpdateRequest,
  Activity90202CreateOrUpdateResponse,
  Activity90202SeriesImportResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 伊利定制-集罐有礼
 * @summary 创建活动
 * @request POST:/90202/createActivity
 */
export const createActivity = (
  request: Activity90202CreateOrUpdateRequest,
): Promise<Activity90202CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90202/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 导入系列信息excel
 * @request POST:/90202/importSeriesExcel
 */
export const importSeriesExcel = (file: any): Promise<Activity90202SeriesImportResponse[]> => {
  return httpRequest({
    url: '/90202/importSeriesExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 下载模板
 * @request POST:/90202/seriesTemplate/export
 */
export const seriesTemplateExport = (): Promise<void> => {
  return httpRequest({
    url: '/90202/seriesTemplate/export',
    method: 'post',
  });
};

/**
 * @tags 伊利定制-集罐有礼
 * @summary 修改活动
 * @request POST:/90202/updateActivity
 */
export const updateActivity = (
  request: Activity90202CreateOrUpdateRequest,
): Promise<Activity90202CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90202/updateActivity',
    method: 'post',
    data: request,
  });
};
