import {
  Activity90031PrizeRemainRequest,
  Activity90090CreateOrUpdateRequest,
  Activity90090CreateOrUpdateResponse,
  Activity90090OrderRequest,
  Activity90090OrderResponse,
  Activity90090PartakeRecordPageRequest,
  Activity90090UserPrizeRecordPageRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  CollectingReportRequest,
  Get90090RequestParams,
  IPageActivity90090PartakeRecordPageResponse,
  IPageActivity90090UserPrizeRecordPageResponse,
  IPageCollectingReportDTO,
  TransmissionReportDTO,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 伊利复购有礼
 * @summary 创建活动
 * @request POST:/90090/createActivity
 */
export const createActivity = (
  request: Activity90090CreateOrUpdateRequest,
): Promise<Activity90090CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90090/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 报表导出
 * @request POST:/90090/data/data/export
 */
export const dataDataExport = (collectingTankRequest: CollectingReportRequest): Promise<void> => {
  return httpRequest({
    url: '/90090/data/data/export',
    method: 'post',
    data: collectingTankRequest,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 订单查询
 * @request POST:/90090/data/getOrder
 */
export const dataGetOrder = (request: Activity90090OrderRequest): Promise<Activity90090OrderResponse[]> => {
  return httpRequest({
    url: '/90090/data/getOrder',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 参与记录
 * @request POST:/90090/data/partakeLog
 */
export const dataPartakeLog = (
  request: Activity90090PartakeRecordPageRequest,
): Promise<IPageActivity90090PartakeRecordPageResponse> => {
  return httpRequest({
    url: '/90090/data/partakeLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 参与记录导出
 * @request POST:/90090/data/partakeLog/export
 */
export const dataPartakeLogExport = (request: Activity90090PartakeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90090/data/partakeLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 参与记录人群包
 * @request POST:/90090/data/partakeLog/uploadPin
 */
export const dataPartakeLogUploadPin = (request: Activity90090PartakeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90090/data/partakeLog/uploadPin',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 传输数据查询
 * @request GET:/90090/data/reportList
 */
export const dataReportList = (query: Get90090RequestParams): Promise<TransmissionReportDTO> => {
  return httpRequest({
    url: '/90090/data/reportList',
    method: 'get',
    params: query,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 报表查询
 * @request POST:/90090/data/reportPage
 */
export const dataReportPage = (request: CollectingReportRequest): Promise<IPageCollectingReportDTO> => {
  return httpRequest({
    url: '/90090/data/reportPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 中奖记录
 * @request POST:/90090/data/winningLog
 */
export const dataWinningLog = (
  request: Activity90090UserPrizeRecordPageRequest,
): Promise<IPageActivity90090UserPrizeRecordPageResponse> => {
  return httpRequest({
    url: '/90090/data/winningLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 中奖记录导出
 * @request POST:/90090/data/winningLog/export
 */
export const dataWinningLogExport = (request: Activity90090UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90090/data/winningLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼数据
 * @summary 中奖记录人群包
 * @request POST:/90090/data/winningLog/uploadPin
 */
export const dataWinningLogUploadPin = (request: Activity90090UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/90090/data/winningLog/uploadPin',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼
 * @summary 查询活动信息
 * @request POST:/90090/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/90090/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼
 * @summary 获取奖品剩余数量
 * @request POST:/90090/getPrizeRemain
 */
export const getPrizeRemain = (request: Activity90031PrizeRemainRequest): Promise<string> => {
  return httpRequest({
    url: '/90090/getPrizeRemain',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 伊利复购有礼
 * @summary 修改活动
 * @request POST:/90090/updateActivity
 */
export const updateActivity = (
  request: Activity90090CreateOrUpdateRequest,
): Promise<Activity90090CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90090/updateActivity',
    method: 'post',
    data: request,
  });
};
