import {
  Dz1912668770678595586DataRequest,
  Dz1912668770678595586DataResponse,
  Dz1912668770678595586ExamineDeleteRequest,
  Dz1912668770678595586ExamineQueryRequest,
  Dz1912668770678595586ExamineResponse,
  Dz1912668770678595586ExamineWorkRequest,
  Dz1912668770678595586RankPublishRequest,
  Dz1912668770678595586RankWorkModifyRequest,
  ExportReportUsingPost3Params,
  IPageDz1912668770678595586ExamineWorksResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags VIVO-新品上市二期看板
 * @summary 获取数据
 * @request POST:/vivoNewArrival/data/getDataPage
 */
export const dataGetDataPage = (
  request: Dz1912668770678595586DataRequest,
): Promise<Dz1912668770678595586DataResponse> => {
  return httpRequest({
    url: '/vivoNewArrival/data/getDataPage',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 导出数据
 * @request POST:/vivoNewArrival/data/report/export
 */
export const dataReportExport = (query: ExportReportUsingPost3Params): Promise<void> => {
  return httpRequest({
    url: '/vivoNewArrival/data/report/export',
    method: 'post',
    params: query,
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 添加到待发布
 * @request POST:/vivoNewArrival/examine/addPublish
 */
export const examineAddPublish = (request: Dz1912668770678595586ExamineWorkRequest): Promise<void> => {
  return httpRequest({
    url: '/vivoNewArrival/examine/addPublish',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 移除待发布作品
 * @request POST:/vivoNewArrival/examine/delete
 */
export const examineDelete = (request: Dz1912668770678595586ExamineDeleteRequest): Promise<void> => {
  return httpRequest({
    url: '/vivoNewArrival/examine/delete',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 发布待审核作品上线
 * @request POST:/vivoNewArrival/examine/publish
 */
export const examinePublish = (request: Dz1912668770678595586RankPublishRequest): Promise<void> => {
  return httpRequest({
    url: '/vivoNewArrival/examine/publish',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 待发布作品列表
 * @request POST:/vivoNewArrival/examine/rankWorksPage
 */
export const examineRankWorksPage = (): Promise<Dz1912668770678595586ExamineResponse[]> => {
  return httpRequest({
    url: '/vivoNewArrival/examine/rankWorksPage',
    method: 'post',
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 确认排名
 * @request POST:/vivoNewArrival/examine/verifyRank
 */
export const examineVerifyRank = (request: Dz1912668770678595586RankWorkModifyRequest): Promise<void> => {
  return httpRequest({
    url: '/vivoNewArrival/examine/verifyRank',
    method: 'post',
    data: request,
  });
};

/**
 * @tags VIVO-新品上市二期看板
 * @summary 作品列表
 * @request POST:/vivoNewArrival/examine/worksPage
 */
export const examineWorksPage = (
  request: Dz1912668770678595586ExamineQueryRequest,
): Promise<IPageDz1912668770678595586ExamineWorksResponse> => {
  return httpRequest({
    url: '/vivoNewArrival/examine/worksPage',
    method: 'post',
    data: request,
  });
};
