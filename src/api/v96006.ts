import {
  Activity96006CreateOrUpdateRequest,
  Activity96006CreateOrUpdateResponse,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 雅诗兰黛直播间加赠礼
 * @summary 创建活动
 * @request POST:/96006/createActivity
 */
export const createActivity = (
  request: Activity96006CreateOrUpdateRequest,
): Promise<Activity96006CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96006/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雅诗兰黛直播间加赠礼
 * @summary 查询活动信息
 * @request POST:/96006/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/96006/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雅诗兰黛直播间加赠礼
 * @summary 查询活动参与人数信息
 * @request POST:/96006/getPrizeUserInfo
 */
export const getPrizeUserInfo = (request: BaseGetActivityRequest): Promise<string> => {
  return httpRequest({
    url: '/96006/getPrizeUserInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 雅诗兰黛直播间加赠礼
 * @summary 修改活动
 * @request POST:/96006/updateActivity
 */
export const updateActivity = (
  request: Activity96006CreateOrUpdateRequest,
): Promise<Activity96006CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/96006/updateActivity',
    method: 'post',
    data: request,
  });
};
