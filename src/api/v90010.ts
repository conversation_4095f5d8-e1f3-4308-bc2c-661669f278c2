import {
  Activity90001MemberIdKeyAndLevelVo,
  Activity90010AddressResponse,
  Activity90010CreateOrUpdateRequest,
  Activity90010CreateOrUpdateResponse,
  Activity90010OrderLogRequest,
  Activity90010UserWinningLogRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  BdActivity90010Address,
  IPageActivity90010OrderLogResponse,
  IPageActivity90010UserWinningLogResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags cpb生日礼
 * @summary 创建活动
 * @request POST:/90010/createActivity
 */
export const createActivity = (
  request: Activity90010CreateOrUpdateRequest,
): Promise<Activity90010CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90010/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags cpb生日礼
 * @summary 地址详情
 * @request POST:/90010/data/addressDetail
 */
export const dataAddressDetail = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<Activity90010AddressResponse> => {
  return httpRequest({
    url: '/90010/data/addressDetail',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 查看收件地址
 * @request POST:/90010/data/getAddress
 */
export const dataGetAddress = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<BdActivity90010Address> => {
  return httpRequest({
    url: '/90010/data/getAddress',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 订单记录
 * @request POST:/90010/data/orderLog
 */
export const dataOrderLog = (
  activity90010OrderLogRequest: Activity90010OrderLogRequest,
): Promise<IPageActivity90010OrderLogResponse> => {
  return httpRequest({
    url: '/90010/data/orderLog',
    method: 'post',
    data: activity90010OrderLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 订单记录导出
 * @request POST:/90010/data/orderLog/export
 */
export const dataOrderLogExport = (activity30003ActivityDataRequest: Activity90010OrderLogRequest): Promise<void> => {
  return httpRequest({
    url: '/90010/data/orderLog/export',
    method: 'post',
    data: activity30003ActivityDataRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 上传人群包
 * @request POST:/90010/data/orderLog/uploadPin
 */
export const dataOrderLogUploadPin = (activity90010OrderLogRequest: Activity90010OrderLogRequest): Promise<void> => {
  return httpRequest({
    url: '/90010/data/orderLog/uploadPin',
    method: 'post',
    data: activity90010OrderLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 上传人群包
 * @request POST:/90010/data/winning/uploadPin
 */
export const dataWinningUploadPin = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90010/data/winning/uploadPin',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 奖品领取记录
 * @request POST:/90010/data/winningLog
 */
export const dataWinningLog = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<IPageActivity90010UserWinningLogResponse> => {
  return httpRequest({
    url: '/90010/data/winningLog',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 中奖记录导出
 * @request POST:/90010/data/winningLog/export
 */
export const dataWinningLogExport = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90010/data/winningLog/export',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 汇总奖品领取记录
 * @request POST:/90010/data/winningLogAll
 */
export const dataWinningLogAll = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<IPageActivity90010UserWinningLogResponse> => {
  return httpRequest({
    url: '/90010/data/winningLogAll',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 汇总记录导出
 * @request POST:/90010/data/winningLogAll/export
 */
export const dataWinningLogAllExport = (
  activity90010UserWinningLogRequest: Activity90010UserWinningLogRequest,
): Promise<void> => {
  return httpRequest({
    url: '/90010/data/winningLogAll/export',
    method: 'post',
    data: activity90010UserWinningLogRequest,
  });
};

/**
 * @tags cpb生日礼
 * @summary 查询活动信息
 * @request POST:/90010/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/90010/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags cpb生日礼
 * @summary 查询memberId
 * @request POST:/90010/getMemberId
 */
export const getMemberId = (
  activity90001MemberIdKeyAndLevelVo: Activity90001MemberIdKeyAndLevelVo,
): Promise<string[]> => {
  return httpRequest({
    url: '/90010/getMemberId',
    method: 'post',
    data: activity90001MemberIdKeyAndLevelVo,
  });
};

/**
 * @tags cpb生日礼
 * @summary 导入memberId
 * @request POST:/90010/importMemberIdExcel
 */
export const importMemberIdExcel = (file: any): Promise<string> => {
  return httpRequest({
    url: '/90010/importMemberIdExcel',
    method: 'post',
    data: file,
  });
};

/**
 * @tags cpb生日礼
 * @summary 修改活动
 * @request POST:/90010/updateActivity
 */
export const updateActivity = (
  request: Activity90010CreateOrUpdateRequest,
): Promise<Activity90010CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/90010/updateActivity',
    method: 'post',
    data: request,
  });
};
