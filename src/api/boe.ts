import { BoeQueationActivityDataVo, IPageBoeQueationActivityDataDto } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags BOE答题互动活动数据报表
 * @summary 会员兑换数据导出
 * @request POST:/boe/question/1797471746559229954/data/getRecordList/export
 */
export const question1797471746559229954DataGetRecordListExport = (
  boeQueationActivityDataVo: BoeQueationActivityDataVo,
): Promise<void> => {
  return httpRequest({
    url: '/boe/question/1797471746559229954/data/getRecordList/export',
    method: 'post',
    data: boeQueationActivityDataVo,
  });
};

/**
 * @tags BOE答题互动活动数据报表
 * @summary 查询活动报表数据
 * @request POST:/boe/question/1797471746559229954/data/getRecordPage
 */
export const question1797471746559229954DataGetRecordPage = (
  boeQueationActivityDataVo: BoeQueationActivityDataVo,
): Promise<IPageBoeQueationActivityDataDto> => {
  return httpRequest({
    url: '/boe/question/1797471746559229954/data/getRecordPage',
    method: 'post',
    data: boeQueationActivityDataVo,
  });
};
