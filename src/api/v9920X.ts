import { Activity99201QuerySkinRequest, IPageObject } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 会员升级礼包
 * @summary 获取皮肤
 * @request POST:/9920X/getBackgrounds
 */
export const getBackgrounds = (request: Activity99201QuerySkinRequest): Promise<IPageObject> => {
  return httpRequest({
    url: '/9920X/getBackgrounds',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 获取皮肤
 * @request POST:/9920X/getSkins
 */
export const getSkins = (request: Activity99201QuerySkinRequest): Promise<IPageObject> => {
  return httpRequest({
    url: '/9920X/getSkins',
    method: 'post',
    data: request,
  });
};
