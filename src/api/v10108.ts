import {
  Activity10108CreateOrUpdateRequest,
  Activity10108CreateOrUpdateResponse,
  Activity10108RecordRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity10108RecordResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 蔓迪锁权
 * @summary 创建活动
 * @request POST:/10108/createActivity
 */
export const createActivity = (
  request: Activity10108CreateOrUpdateRequest,
): Promise<Activity10108CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10108/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 前N名付尾款有礼
 * @summary 参与记录
 * @request POST:/10108/data/joinRecord
 */
export const dataJoinRecord = (request: Activity10108RecordRequest): Promise<IPageActivity10108RecordResponse> => {
  return httpRequest({
    url: '/10108/data/joinRecord',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 前N名付尾款有礼
 * @summary 导出记录
 * @request POST:/10108/data/joinRecord/export
 */
export const dataJoinRecordExport = (request: Activity10108RecordRequest): Promise<void> => {
  return httpRequest({
    url: '/10108/data/joinRecord/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 前N名付尾款有礼
 * @summary 获取奖励列表
 * @request POST:/10108/data/position
 */
export const dataPosition = (request: Activity10108RecordRequest): Promise<number[]> => {
  return httpRequest({
    url: '/10108/data/position',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 蔓迪锁权
 * @summary 查询活动信息
 * @request POST:/10108/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/10108/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 蔓迪锁权
 * @summary 修改活动
 * @request POST:/10108/updateActivity
 */
export const updateActivity = (
  request: Activity10108CreateOrUpdateRequest,
): Promise<Activity10108CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/10108/updateActivity',
    method: 'post',
    data: request,
  });
};
