/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-05 15:37
 * Description:
 */
import React, { useReducer } from 'react';
// @ts-ignore
import styles from '../../index.module.scss';
import LzTipPanel from '@/components/LzTipPanel';
import { Field, Form, Radio, NumberPicker, Button, Divider, DatePicker2, Message, Balloon, Icon } from '@alifd/next';
import constant from '@/utils/constant';
import format from '@/utils/format';
import dayjs from 'dayjs';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const { RangePicker } = DatePicker2;

const dateFormat = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: any = {
  labelAlign: 'top',
  colon: true,
};
const TIP = (
  <div style={{ color: 'black' }}>
    <div>任务说明</div>
    <div>1.用户在任务时间内可进入活动页浏览，获得抽奖机会</div>
  </div>
);
export default ({ onCancel, onSubmit, editValue, formData }) => {
  const defaultValue = {
    // 任务时间限制方式 1 活动期间 2 指定时间
    taskTimeLimitWay: 1,
    // 成功浏览数
    perOperateCount: 1,
    // 赠送抽奖机会次数
    perLotteryCount: 1,
    // 完成任务上限
    taskLength: 1,
    // 任务时间
    taskRangeDate: formData.rangeDate,
    taskStartTime: format.formatDateTimeDayjs(formData.rangeDate[0]),
    taskEndTime: format.formatDateTimeDayjs(formData.rangeDate[1]),
  };
  const [taskData, setTaskData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const setData = (data) => {
    setTaskData(data);
  };

  const submit = () => {
    if (!taskData.perOperateCount) {
      Message.error('请输入成功浏览数');
      return;
    }
    if (!taskData.perLotteryCount) {
      Message.error('请输入赠送抽奖机会次数');
      return;
    }
    if (!taskData.taskLength) {
      Message.error('请输入完成任务上限');
      return;
    }
    onSubmit({ ...taskData, taskType: 2 });
  };
  const onDataRangeChange = (taskRangeDate: any[]) => {
    setTaskData({ taskRangeDate });
    setData({
      taskStartTime: format.formatDateTimeDayjs(taskRangeDate[0]),
      taskEndTime: format.formatDateTimeDayjs(taskRangeDate[1]),
    });
  };

  const TipLabel = (
    <span>
      活动内完成任务上限：
      <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
        例如每成功浏览2次店铺获得1次抽奖机会，完成任务限制2次，则用户每日 可浏览4次店铺，获得2次抽奖机会
      </Balloon.Tooltip>
    </span>
  );

  return (
    <div className={styles.lzTask}>
      <LzTipPanel message={TIP} />
      <Form {...formItemLayout} field={field}>
        <FormItem label="任务时间限制">
          <RadioGroup value={taskData.taskTimeLimitWay} onChange={(taskTimeLimitWay) => setData({ taskTimeLimitWay })}>
            <Radio id="1" value={1}>
              活动期间有效
            </Radio>
            <Radio id="2" value={2}>
              指定时间
            </Radio>
          </RadioGroup>
          {taskData.taskTimeLimitWay === 2 && (
            <div className={styles.panel}>
              <RangePicker
                onChange={onDataRangeChange}
                style={{ width: '100%' }}
                name="rangeDate"
                showTime
                hasClear={false}
                format={dateFormat}
                value={taskData.taskRangeDate}
                disabledDate={(date) => {
                  return (
                    date.valueOf() < dayjs(formData.rangeDate[0]).subtract(1, 'day').valueOf() ||
                    date.valueOf() > dayjs(formData.rangeDate[1]).valueOf()
                  );
                }}
              />
            </div>
          )}
        </FormItem>
        <FormItem label="任务条件">
          <div className={styles.panel}>
            每成功关注
            <NumberPicker
              value={taskData.perOperateCount}
              onChange={(perOperateCount) => setData({ perOperateCount })}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />
            件商品，赠送
            <NumberPicker
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
              value={taskData.perLotteryCount}
              onChange={(perLotteryCount) => setData({ perLotteryCount })}
            />
            次抽奖机会
          </div>
        </FormItem>
        <FormItem label={TipLabel} colon={false}>
          限制
          <NumberPicker
            value={taskData.taskLength}
            onChange={(taskLength) => setData({ taskLength })}
            type="inline"
            min={1}
            max={9999999}
            className={styles.number}
          />
        </FormItem>
      </Form>
      <Divider />
      <div className={styles.footer}>
        <Button onClick={onCancel}>取消</Button>
        <Button onClick={submit} type="primary">
          确定
        </Button>
      </div>
    </div>
  );
};
