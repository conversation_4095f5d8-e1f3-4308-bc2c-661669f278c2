import { Button, Form, Input, NumberPicker, DatePicker, Box, Field, Message, Loading } from '@alifd/next';
import { useReducer, useCallback, useState } from 'react';
import * as React from 'react';
import LzTip from '@/components/LzTip';
import moment from 'moment';
import ChooseSku from '@/components/ChooseSku';
import { addPromotion } from '@/api/prize';
import dayjs from 'dayjs';
import { debounce } from '@/utils';

const { RangePicker } = DatePicker;
export default ({ handleCancel, handleSubmit, onlyOne }) => {
  const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  const field: any = Field.useField({ values: { skuList: [] } });
  const [loading, setLoading] = useState(false);
  // 优惠券列表相关model
  const [skuState, skuActions] = useReducer(
    (p, c) => {
      return { ...p, ...c };
    },
    { skus: [], tempSkus: [] },
  );

  const getPostData = () => {
    const priceInfos = skuState.skus.map((sku) => {
      return {
        jingDongPrice: sku.jdPrice,
        promoPrice: sku.salePrice,
        skuId: sku.skuId,
        skuName: sku.skuName,
        skuPicture: sku.skuMainPicture,
        skuMaxNum: sku.skuMaxNum,
      };
    });

    const values = field.getValues();

    return {
      beginTime: dayjs(values.putTime[0]).format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs(values.putTime[1]).format('YYYY-MM-DD HH:mm:ss'),
      name: values.name,
      perMaxNum: values.perMaxNum,
      perMinNum: values.perMinNum,
      totalNum: values.totalNum,
      priceInfos,
    };
  };

  const create = (postData) => {
    setLoading(true);
    addPromotion(postData)
      .then(() => {
        Message.success('创建成功');
        handleSubmit();
        setLoading(false);
      })
      .catch((err) => {
        Message.error(err.message);
        setLoading(false);
      });
  };
  const debouncedCreate = useCallback(
    debounce((data) => create(data), 500),
    [],
  );
  const onOkCreate = (value, errors: unknown): void | boolean => {
    if (!errors) {
      const postData = getPostData();
      debouncedCreate(postData);
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };
  const perMinNumValidator = (rule, value, callback) => {
    field.validate(['perMaxNum']);
    callback();
  };

  const putDateValidator = (rule, value, callback) => {
    const startTime = value[0];
    const endTime = value[1];
    if (startTime !== null && new Date().getTime() > new Date(startTime).getTime()) {
      callback(new Error('活动时间需要大于当前时间'));
    } else if (endTime === null) {
      callback(new Error('请选择活动结束时间'));
    } else if (new Date(endTime).getTime() - new Date(startTime).getTime() > 90 * 24 * 60 * 60 * 1000) {
      callback(new Error('活动时间不得超过90天'));
    } else {
      callback();
    }
  };

  /**
   * 禁用今天之前
   * @param date
   * @returns boolean
   */
  const currentDate = moment().add(-1, 'days');

  const disabledDate = function (date) {
    return date.isBefore(currentDate) as any;
  };

  const perMaxNumValidator = (rule, value, callback) => {
    if ((field.getValue('perMinNum') as any) > value) {
      callback(new Error('最多购买数量必须要大于等于最少购买数量'));
    } else {
      callback();
    }
  };

  const skuValidator = (rule, value, callback) => {
    const skuList = JSON.parse(field.getValue('skuList'));
    if (!skuList || skuList.length === 0) {
      callback(new Error('请选择折扣商品'));
    } else {
      callback();
    }
  };

  const setSku = (skuList) => {
    console.log('list', JSON.stringify(skuList.map((o) => o.skuId)));
    field.setValue('skuList', JSON.stringify(skuList.map((o) => o.skuId)));
    field.validate(['skuList']);
  };
  const onSkuTableChange = (skus) => {
    skuActions({ skus, tempSkus: skus });
    setSku(skus);
    const names = field.getNames().filter((name) => name.indexOf('discount') > 0 || name.indexOf('salePrice') > 0);
    setTimeout(() => {
      field.validate([...names]);
    }, 500);
  };

  return (
    <Loading visible={loading}>
      <Form {...formItemLayout} field={field} style={{ width: 950 }}>
        <Form.Item label="折扣活动名称：" name="name" required requiredMessage="请填写折扣活动名称">
          <Input name="name" maxLength={10} placeholder="请填写折扣活动名称" style={{ width: 335 }} />
        </Form.Item>
        <Form.Item
          label="活动时间："
          name="putTime"
          required
          requiredMessage="请选择活动时间"
          validator={putDateValidator}
        >
          <RangePicker showTime name="putTime" disabledDate={disabledDate} />
          <LzTip>(注：最长为90天)</LzTip>
        </Form.Item>
        <Form.Item label="SKU限购：" required>
          <Box direction="row">
            <>
              <Box>
                <Form.Item
                  name="perMinNum"
                  required
                  requiredMessage="请输入大于1的正整数"
                  validator={perMinNumValidator}
                >
                  每个SKU最少购买&nbsp;
                  <NumberPicker type="inline" step={1} min={1} max={200} defaultValue={1} name="perMinNum" />
                  <span>&nbsp;个，</span>
                </Form.Item>
              </Box>
              <Box>
                <Form.Item
                  name="perMaxNum"
                  required
                  requiredMessage="请输入大于【最少购买数量】的正整数"
                  validator={perMaxNumValidator}
                >
                  <span>最多购买&nbsp;</span>
                  <NumberPicker type="inline" step={1} max={200} min={1} defaultValue={1} name="perMaxNum" />
                  <span>&nbsp;个，才可享受优惠</span>
                </Form.Item>
              </Box>
            </>
            {/* )} */}
          </Box>
        </Form.Item>
        <Form.Item label="折扣商品：" name="skuList" required requiredMessage="请选择折扣商品" validator={skuValidator}>
          <Input htmlType="hidden" name="skuList" />
          <ChooseSku dataSource={skuState.skus} onChange={onSkuTableChange} max={onlyOne ? 1 : 500} />
        </Form.Item>
        <Form.Item
          label="可享受此优惠的最大人数："
          name="totalNum"
          required
          requiredMessage="请输入可享受此优惠的最大人数"
        >
          <NumberPicker type="inline" step={1} device="tablet" min={1} max={9999999} defaultValue={1} name="totalNum" />
          <span>&nbsp;人</span>
        </Form.Item>
        <Form.Item label=" " className="bean_btn">
          <Form.Submit validate type="primary" onClick={onOkCreate}>
            提交
          </Form.Submit>
          <Button onClick={onCloseCreate} style={{ marginLeft: 15 }}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </Loading>
  );
};
