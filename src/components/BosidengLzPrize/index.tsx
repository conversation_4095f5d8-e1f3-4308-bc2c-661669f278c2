import React, { useState } from 'react';
import { Table, Button, Dialog, Form, Message } from '@alifd/next';
import { activityEditDisabled, isDisableSetPrize } from '@/utils';
import { PrizeInfo } from '../../pages/activity/80021/1001/util';
import LzDialog from '../LzDialog';
import ChoosePrize from '../ChoosePrize';

const PRIZE_TYPE = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
const PRIZE_INFO = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
};
const FormItem = Form.Item;

interface Props {
  formData: any;
  onChange: (formData: any) => void;
  typeList?: number[];
  defaultTarget?: number;
  config?: {
    imgWith?: number;
    imgHeight?: number;
    prizeNameLength?: number;
  };
}

export default ({ formData, onChange, defaultTarget = 2, typeList = [1, 2, 3, 4, 6, 7], config }: Props) => {
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);

  const setData = (data): void => {
    onChange({ ...formData, ...data });
  };

  const onCancel = (): void => {
    setVisible(false);
  };
  const onPrizeChange = (data): boolean | void => {
    if (data.prizeType === 1) {
      // 后端用于发奖和保存校验需要
      data.prizeKey = target.toString();
    }
    // 更新指定index 奖品信息
    formData.prizeList[target] = data;
    // 计算总概率
    formData.totalProbability = formData.prizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.prizeList.splice(target, 1, PRIZE_INFO);
      return false;
    }
    setData(formData);
    setVisible(false);
  };
  return (
    <div>
      <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
        <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
        <Table.Column title="奖项名称" dataIndex="prizeName" />
        <Table.Column
          title="奖项类型"
          cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
          dataIndex="prizeType"
        />
        <Table.Column
          title="单位数量"
          cell={(_, index, row) => {
            return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
          }}
        />
        <Table.Column
          title="发放份数"
          cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
        />
        <Table.Column
          title="单份价值(元)"
          cell={(_, index, row) => <div>{row.unitPrice ?? Number(row.unitPrice).toFixed(2)}</div>}
        />
        <Table.Column
          title="中奖概率(%)"
          cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>}
        />
        <Table.Column
          title="每日发放限额"
          cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
        />
        <Table.Column
          title="奖品图"
          cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
        />
        {!activityEditDisabled() && (
          <Table.Column
            title="操作"
            width={130}
            cell={(val, index, _) => (
              <FormItem disabled={isDisableSetPrize(formData.prizeList, index)}>
                <Button
                  text
                  type="primary"
                  onClick={() => {
                    let row = formData.prizeList[index];
                    if (row.prizeName === '谢谢参与') {
                      row = null;
                    }
                    setEditValue(row);
                    setTarget(index);
                    setVisible(true);
                  }}
                >
                  <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                </Button>
                <Button
                  text
                  type="primary"
                  onClick={() => {
                    if (_.prizeType) {
                      Dialog.confirm({
                        v2: true,
                        title: '提示',
                        centered: true,
                        content: '确认清空该奖品？',
                        onOk: () => {
                          formData.prizeList.splice(index, 1, PRIZE_INFO);
                          formData.totalProbability = formData.prizeList
                            .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                            .reduce((v, total) => {
                              return v + Number(total.probability);
                            }, 0);
                          setData(formData);
                        },
                        onCancel: () => console.log('cancel'),
                      } as any);
                    }
                  }}
                >
                  <i className={`iconfont icon-shanchu`} />
                </Button>
                {formData.prizeList.length > 0 && index > 0 && (
                  <Button
                    text
                    type="primary"
                    onClick={() => {
                      formData.prizeList.splice(
                        index - 1,
                        1,
                        ...formData.prizeList.splice(index, 1, formData.prizeList[index - 1]),
                      );
                      setData(formData);
                    }}
                  >
                    <i className={`iconfont icon-iconjiantou-35`} />
                  </Button>
                )}
                {formData.prizeList.length > 0 && index < formData.prizeList.length - 1 && (
                  <Button
                    text
                    type="primary"
                    onClick={() => {
                      formData.prizeList.splice(
                        index,
                        1,
                        ...formData.prizeList.splice(index + 1, 1, formData.prizeList[index]),
                      );
                      setData(formData);
                    }}
                  >
                    <i className={`iconfont icon-iconjiantou-34`} />
                  </Button>
                )}
              </FormItem>
            )}
          />
        )}
      </Table>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          typeList={typeList}
          defaultTarget={defaultTarget}
          isBosideng
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          width={config?.imgWith}
          height={config?.imgHeight}
          prizeNameLength={config?.prizeNameLength}
        />
      </LzDialog>
    </div>
  );
};
