import styles from '../index.module.scss';
import {
  Button,
  Form,
  Input,
  DatePicker2,
  Radio,
  Field,
  NumberPicker,
  Icon,
  Balloon,
  Table,
  Message,
  Dialog,
} from '@alifd/next';
import { useState } from 'react';
import moment from 'moment';
import * as React from 'react';
import ChooseGoods from '@/components/ChooseGoods';
import LzImg from '@/components/LzImg';
import CONST from '@/utils/constant';
import { deepCopy } from '@/utils';
import { addResPrizeCouponShop } from '@/api/prize';

const MSOFDAY = 24 * 60 * 60 * 1000;

const defaultTrigger = <Icon size="small" className={styles.help} type="help" />;

// eslint-disable-next-line complexity
export default ({ handleCancel, handleSubmit }) => {
  const field = Field.useField();
  const { RangePicker } = DatePicker2;
  const formItemLayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };
  const [type, setType] = useState(5);
  const [fen, setFen] = useState(0);
  const [zhang, setZhang] = useState(0);
  const [skuList, setSkuList] = useState<any[]>([]);
  const time = moment();
  const [timeRange, setTimeRange] = useState([]);
  const [couponTime, setCouponTime] = useState([]);
  const [rangeType, setRangeType] = useState(1);

  const showConfirm = () => {
    return new Promise((resolve, reject) => {
      if (rangeType === 1) {
        Dialog.confirm({
          title: '提示',
          content: '当前创建的为店铺优惠券，全店商品可用，请谨慎创建',
          onOk: () => {
            resolve(1);
          },
          onCancel: () => {
            reject();
          },
          onClose: () => {
            reject();
          },
        });
      } else {
        resolve(1);
      }
    });
  };
  // 提交创建京豆表单
  // eslint-disable-next-line complexity
  const onOkCreate = (value, errors: unknown): void | boolean => {
    if (rangeType === 2) {
      value.skuIds = skuList.map((sku) => sku.skuId).join(',');
      if (!value.skuIds) {
        Message.error('请选择适用商品');
        return false;
      }
    }
    if (+value.expireType === 1 && !value.couponValidateDay) {
      Message.error('请输入固定天数');
      return false;
    }
    if (value.couponQuota <= value.couponDiscount) {
      Message.error('优惠券面值需大于优惠金额');
      return false;
    }
    if (value.timeRange[0] && value.timeRange[1]) {
      value.startTime = moment(value.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
      value.endTime = moment(value.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
    }
    if (+value.expireType === 5) {
      if (!value.couponTime) {
        Message.error('请选择优惠券有效期开始时间，结束时间');
        return false;
      }
      if (value.couponTime[0] && value.couponTime[1]) {
        value.couponBeginTime = moment(value.couponTime[0]).format('YYYY-MM-DD HH:mm:ss');
        value.couponEndTime = moment(value.couponTime[1]).format('YYYY-MM-DD HH:mm:ss');
      } else {
        Message.error('请选择优惠券有效期开始时间，结束时间');
        return false;
      }
    }
    if (!errors) {
      showConfirm().then((val): any => {
        value.quantityTotal = fen * zhang;
        delete value.timeRange;
        delete value.couponTime;
        addResPrizeCouponShop(value)
          .then(() => {
            Message.success('创建成功');
            handleSubmit();
          })
          .catch((err) => {
            Message.error(err.message);
          });
      });
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };

  const checkCollectionTime = (rule, range, callback) => {
    if (!range[0]) {
      callback('请选择领券开始时间');
    } else if (!range[1]) {
      callback('请选择领券结束时间');
    } else {
      const [start, end] = range.map((date) => moment(date));
      const dayDuration = (end - start) / MSOFDAY;
      if (dayDuration > 90) {
        callback('领券结束时间与领券开始时间间隔不可超过90天');
      } else if (start.format('YYYY-MM-DD') < time.format('YYYY-MM-DD')) {
        // 提交时间小于进入页面时间
        callback('领券开始时间需要大于当前时间');
      } else {
        callback();
      }
    }
  };

  const checkCouponQuota = (rule, value, callback) => {
    if (value || field.getValue('couponType') === 2) {
      callback();
    } else {
      callback('请设置优惠信息');
    }
  };
  // eslint-disable-next-line complexity
  const checkPeriodValidity = (rule, value, callback) => {
    if (type === 5 && !value) {
      callback('请设置券有效期');
      return;
    } else if (type === 1 && !value) {
      callback('请输入固定天数');
      return;
    } else if (rule.field === 'couponTime') {
      value = couponTime;
      if (value[0] || value[1]) {
        const [couponStart, couponEnd]: any[] = value.map((date) => moment(date));
        const [start, end]: any[] = timeRange.map((date) => moment(date));
        // moment的isBefore方法 及 startDuration 需要忽略毫秒位数
        let startDuration = 0;
        let endDuration = 0;
        if ((couponStart as any) - start > -1000 && (couponStart as any) - start < 1000) {
          startDuration = 0;
        } else {
          startDuration = (couponStart as any) - start;
        }
        if ((couponEnd as any) - end > -1000 && (couponEnd as any) - end < 1000) {
          endDuration = 0;
        } else {
          endDuration = (couponEnd as any) - end;
        }
        if (startDuration < 0) {
          callback('券有效开始时间不应早于领券开始时间');
          return;
        }
        if (endDuration < 0) {
          callback('券有效结束时间不应早于领券截止时间');
          return;
        }
        // moment 的 diff方法 无法做到
        const couponDuration = ((couponEnd as any) - (couponStart as any)) / MSOFDAY;
        if (couponDuration > 90) {
          callback('优惠券有效期不可超过90天');
        }
        if (((couponStart as any) - start) / MSOFDAY > 60) {
          callback('有效期开始时间和领券开始时间间隔不可超过60天');
        }
        // 结束时间，默认为有效期开始时间后30天，可选范围为（有效期开始时间 和 领券结束时间取最大值），至领券结束时间30天内
        if (((couponEnd as any) - end) / MSOFDAY > 30) {
          callback('有效期结束时间和领券结束时间间隔不可超过30天');
          return;
        }
      } else {
        callback('请选择有效期范围');
        return;
      }
    }
    callback();
  };

  const currentDate = moment();

  // 禁用日期函数
  const disabledDateFn = (date, view) => {
    // console.log(date, view)
    switch (view) {
      case 'date':
        // return date.valueOf() <= currentDate.valueOf();
        // return date.diff(currentDate, 'days') < 0 || date.diff(currentDate, 'days') > 30;
        return date.diff(currentDate, 'days') < 0;
      case 'year':
        return date.year() < currentDate.year();
      case 'month':
        return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
      default:
        return false;
    }
  };

  return (
    <div className={styles.CreateJdBeanPlan}>
      <Form {...formItemLayout} style={{ width: 800 }}>
        <Form.Item label="店铺/商品券：">
          <Radio.Group name="rangeType" defaultValue={1} onChange={(val: number) => setRangeType(+val)}>
            <Radio value={1}>店铺券</Radio>
            <Radio value={2}>商品券</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="优惠券名称：" required requiredMessage="请输入优惠券名称">
          <Input
            className="form-input-ctrl"
            trim
            showLimitHint
            placeholder="请输入优惠券名称"
            maxLength={15}
            name="planName"
            style={{ width: '348px' }}
          />
        </Form.Item>
        <Form.Item label="领券时间：" required requiredMessage="请选择领券时间" validator={checkCollectionTime}>
          <RangePicker
            style={{ width: '350px' }}
            className="form-input-ctrl"
            name="timeRange"
            value={timeRange}
            disabledDate={disabledDateFn}
            onChange={(val: any) => {
              if (val[1] && val[1].format('HH:mm:ss') === '00:00:00') {
                val[1] = moment(moment(val[1]).format('YYYY-MM-DD 23:59:59'));
              }
              setTimeRange(val);
            }}
            showTime
            hasClear={false}
          />
        </Form.Item>
        <Form.Item label="优惠券类型：">
          <Radio.Group name="couponType" defaultValue={1}>
            <Radio value={1}>东券</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="优惠方式：">
          <Radio.Group disabled name="discountType" defaultValue={1}>
            <Radio value={1}>满减</Radio>
            <Radio value={5}>满折</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="券有效期：">
          <Radio.Group
            defaultValue={5}
            name="expireType"
            onChange={(val) => {
              setType(val as number);
            }}
          >
            <Radio value={5}>固定时间段</Radio>
            <Radio value={1}>固定天数</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label=" " validator={checkPeriodValidity}>
          {type === 1 && <span style={{ marginRight: 10 }}>券领取后</span>}
          {type === 1 && (
            <NumberPicker
              className="offset"
              style={{ width: 155 }}
              name="couponValidateDay"
              type="inline"
              step={1}
              min={1}
              max={30}
              placeholder="1-30之间的整数"
            />
          )}
          {type === 1 && <span style={{ marginLeft: 10, marginRight: 9 }}>天内使用，逾期失效</span>}
          {type === 1 && (
            <div style={{ marginTop: '10px', color: '#c4c7cc' }}>
              注：领券当天不计入，从领券第二天开始计算；截止到最后一天的23:59:59
            </div>
          )}
          {type === 5 && (
            <RangePicker
              style={{ width: '350px' }}
              name="couponTime"
              className="form-input-ctrl"
              showTime
              hasClear={false}
              disabledDate={disabledDateFn}
              onChange={(val: any) => {
                if (val[1] && val[1].format('HH:mm:ss') === '00:00:00') {
                  val[1] = moment(moment(val[1]).format('YYYY-MM-DD 23:59:59'));
                }
                setCouponTime(val);
              }}
            />
          )}
          {type === 5 && (
            <div style={{ marginTop: '10px', color: '#c4c7cc' }}>
              因京东官方限制，券有效期开始时间默认为00:00:00；结束时间默认为23:59:59
            </div>
          )}
        </Form.Item>

        <Form.Item label="优惠信息：">
          {/* 京券只有面值没有门槛 */}
          <div className="next-form-text-align" style={{ display: 'flex' }}>
            {/* 京券只有面值没有门槛 */}
            <Form.Item validator={checkCouponQuota} className={styles.formInLine}>
              {' '}
              <span style={{ marginRight: 5 }}>满</span>
              <NumberPicker
                className="offset"
                style={{ width: 134 }}
                min={1}
                max={100000}
                name="couponQuota"
                placeholder="请输入"
              />
              <span style={{ marginLeft: 5 }}>元</span>
            </Form.Item>
            <Form.Item required requiredMessage="请设置优惠信息" className={styles.formInLine}>
              {' '}
              <span
                style={
                  field.getValue('couponType') === 1
                    ? {
                        marginLeft: 10,
                        marginRight: 5,
                      }
                    : { marginRight: 5 }
                }
              >
                减
              </span>
              <NumberPicker
                className="offset"
                style={{ width: 134 }}
                min={1}
                max={2000}
                name="couponDiscount"
                placeholder="请输入"
              />
              <span style={{ marginLeft: 5 }}>元</span>
            </Form.Item>
          </div>
        </Form.Item>
        <Form.Item label="计划发放数量：" required requiredMessage="请输入计划发放数量">
          <div className="next-form-text-align" style={{ display: 'flex' }}>
            {' '}
            <span style={{ marginRight: 5 }}>发放</span>
            <Form.Item required requiredMessage="请输入发放份数" className={styles.formInLine}>
              <NumberPicker
                className="offset"
                style={{ width: 134 }}
                min={1}
                max={3000000}
                name="numberOfCopies"
                placeholder="请输入"
                onChange={(value) => {
                  setFen(value);
                }}
              />
              <span style={{ marginLeft: 5 }}>
                份{' '}
                <Balloon trigger={defaultTrigger} closable={false}>
                  每中奖一次发一份优惠券奖品
                </Balloon>
                ，
              </span>
            </Form.Item>{' '}
            <span>每份发放</span>
            <Form.Item required requiredMessage="请输入每份发放张数" className={styles.formInLine}>
              <NumberPicker
                className="offset"
                style={{ width: 134 }}
                min={1}
                max={10}
                name="numPerSending"
                placeholder="请输入"
                onChange={(value) => {
                  setZhang(value);
                }}
              />
            </Form.Item>
            <span style={{ marginLeft: 5 }}>
              张{' '}
              <Balloon trigger={defaultTrigger} closable={false}>
                一份优惠券奖品可设置中奖一次发放多张
              </Balloon>
              ，&nbsp;&nbsp;&nbsp;总计发放：
              <span style={{ color: '#f52743' }}>{fen && zhang ? fen * zhang : 0}</span>张
            </span>
          </div>
          <div style={{ marginTop: '10px', color: '#c4c7cc' }}>
            优惠券总发放数量：发放份数*每份发放张数，设置发放张数可实现每份优惠券发放单张与多张场景。
          </div>
          <div style={{ marginTop: '10px', color: '#c4c7cc' }}>
            注：活动中优惠券的奖品发放以份数为库存单位，默认一份对应发放一张优惠券，若修改增加【每份发放张数】，则每份奖品会发放对应张数的优惠券。
          </div>
        </Form.Item>
        {rangeType === 1 && (
          <Form.Item label="适用商品：">
            <Input defaultValue="全部商品可用" style={{ width: '348px' }} disabled />
          </Form.Item>
        )}
        {rangeType === 2 && (
          <>
            <Form.Item label="适用商品：">
              <ChooseGoods value={skuList} onChange={(data) => setSkuList(data)} />
            </Form.Item>
            <Form.Item label=" ">
              <Table.StickyLock dataSource={skuList}>
                <Table.Column
                  title="商品信息"
                  cell={(val, index, record) => (
                    <div className={styles.part1} style={{ alignItems: 'center' }}>
                      {record.skuMainPicture?.indexOf('360buyimg.com') > -1 ? (
                        <LzImg style={{ width: 60, height: 60, marginRight: '5px' }} src={record.skuMainPicture} />
                      ) : (
                        <LzImg
                          style={{ width: 60, height: 60, marginRight: '5px' }}
                          src={`${CONST.IMAGE_PREFIX}${record.skuMainPicture}`}
                        />
                      )}
                      <div>
                        <div className={styles.part1_p1}>{record.skuName}</div>
                        <div className={styles.part1_p2}>SKU编码：{record.skuId}</div>
                      </div>
                    </div>
                  )}
                />
                <Table.Column
                  width={120}
                  title="京东价（元）"
                  cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.jdPrice)}</span>}
                />
                <Table.Column
                  width={120}
                  title="操作"
                  cell={(val, index, info) => (
                    <Button
                      text
                      type="primary"
                      onClick={() => {
                        const list = deepCopy(skuList);
                        list.splice(index, 1);
                        setSkuList(list);
                      }}
                    >
                      删除
                    </Button>
                  )}
                />
              </Table.StickyLock>
            </Form.Item>
          </>
        )}

        <Form.Item label=" ">
          <Form.Submit validate type="primary" className={styles.form_btn} onClick={onOkCreate}>
            提交
          </Form.Submit>
          <Button className={styles.form_btn} onClick={onCloseCreate} style={{ marginLeft: '15px' }}>
            取消
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
