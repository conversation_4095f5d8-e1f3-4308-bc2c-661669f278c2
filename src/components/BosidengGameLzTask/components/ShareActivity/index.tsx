/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-05 15:37
 * Description:
 */
import React, { useReducer } from 'react';
import styles from '../../index.module.scss';
import LzTipPanel from '@/components/LzTipPanel';
import { Field, Form, NumberPicker, Button, Divider, Balloon, Icon, Message } from '@alifd/next';

const FormItem = Form.Item;

const formItemLayout: any = {
  labelAlign: 'top',
  colon: true,
};
const TIP = (
  <div style={{ color: 'black' }}>
    <div>任务说明</div>
    <div>1.用户成功分享活动可以获得游戏机会</div>
    <div>2.用户每日都可以进入活动做任务</div>
  </div>
);
export default ({ onCancel, onSubmit, editValue }) => {
  const defaultValue = {
    // 成功分享
    perOperateCount: 1,
    // 赠送游戏机会次数
    perLotteryCount: 1,
    // 次数限制
    taskLength: 1,
  };
  const [taskData, setTaskData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const setData = (data) => {
    setTaskData(data);
  };
  const submit = () => {
    if (!taskData.perOperateCount || !taskData.perLotteryCount) {
      Message.error('请填写任务条件');
      return;
    }
    if (!taskData.taskLength) {
      Message.error('请填写每日完成任务上限');
      return;
    }
    onSubmit({ ...taskData, taskType: 12 });
  };
  const TipLabel = (
    <span>
      每日完成任务上限：
      <Balloon.Tooltip v2 trigger={<Icon type="help" size="small" />} align="t">
        例如每成功分享2次活动获得1次游戏机会，完成任务限制2次，则用户每日可分享4次 活动，获得2次游戏机会
      </Balloon.Tooltip>
    </span>
  );

  return (
    <div className={styles.lzTask}>
      <LzTipPanel message={TIP} />
      <Form {...formItemLayout} field={field}>
        <FormItem label="任务条件">
          <div className={styles.panel}>
            每成功分享
            <NumberPicker
              value={taskData.perOperateCount}
              onChange={(perOperateCount) => setData({ perOperateCount })}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />{' '}
            次活动，赠送
            <NumberPicker
              value={taskData.perLotteryCount}
              onChange={(perLotteryCount) => setData({ perLotteryCount })}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />{' '}
            次游戏机会
          </div>
        </FormItem>
        <FormItem label={TipLabel} colon={false}>
          <div className={styles.panel}>
            限制
            <NumberPicker
              value={taskData.taskLength}
              onChange={(taskLength) => setData({ taskLength })}
              type="inline"
              min={1}
              max={9999999}
              className={styles.number}
            />{' '}
          </div>
        </FormItem>
      </Form>
      <Divider />
      <div className={styles.footer}>
        <Button onClick={onCancel}>取消</Button>
        <Button onClick={submit} type="primary">
          确定
        </Button>
      </div>
    </div>
  );
};
