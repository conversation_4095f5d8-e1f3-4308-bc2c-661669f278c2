import { useEffect, useState } from 'react';
import * as React from 'react';
import { Table, Pagination, Balloon, Button, Dialog } from '@alifd/next';
import styles from '../index.module.scss';
import SearchForm from './SearchForm';
import format from '@/utils/format';
import { getCardPageList } from '@/api/prize';
import CreatePlan from './CreatePlan';

const defaultPage = { pageSize: 10, pageNum: 1, total: 0 };
export default ({ onSubmit }: any) => {
  const [pageInfo, setPageInfo] = useState({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const { Tooltip } = Balloon;
  const [planDialog, setPlanDialog] = useState(false);

  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getCardPageList({ ...searchData, ...page })
      .then((res) => {
        setList(res.records || []);
        pageInfo.total = res.total as any;
        pageInfo.pageNum = res.current as any;
        pageInfo.pageSize = res.size as any;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]) => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.cardCode));
    // 如果是永久有效的话，就清空时间，因为部分永久有效的礼品卡有时间
    if (selectRow.validNoEndTime === 1) {
      selectRow.startTime = null;
      selectRow.endTime = null;
    }
    onSubmit(selectRow);
  };

  // 选择器
  const rowSelection: any = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record: { cardCode: string }) => {
      return {
        disabled: false,
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record: object) => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // onSubmit(record);
  };
  const onSearch = (formData: any) => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const parsePointResListStatus = (status: number) => {
    switch (status) {
      case 1:
        return '待生效';
      case 2:
        return '已生效';
      case 3:
        return '已过期';
      default:
        return '已过期';
    }
  };
  const parsePointResListStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return 'table-status-WARNING_COLOR';
      case 2:
        return 'table-status-SUCCESS_COLOR';
      default:
        return 'table-status-LIGHT_GRAY';
    }
  };

  const render = (value, index, record) => {
    const intro = (
      <div
        style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
        }}
      >
        {value}
      </div>
    );
    return (
      <Tooltip v2 trigger={intro} align="tr" arrowPointToCenter>
        {value}
      </Tooltip>
    );
  };

  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  const createPlanSubmit = () => {
    loadPageList(defaultPage);
    setPlanDialog(false);
  };
  const toGiftCardList = () => {
    setPlanDialog(true);
  };

  return (
    <div className={styles.PropertyJdBeanPlan}>
      <SearchForm onSearch={onSearch} />
      <div className={styles.reminderBox}>
        <span className={styles.totalStyle}>共有 {pageInfo.total} 条符合条件的记录</span>
        <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toGiftCardList}>
          新建礼品卡 &gt;
        </Button>
      </div>
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="cardCode"
        fixedHeader
        maxBodyHeight={500}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column width={120} align="left" title="礼品卡名称" dataIndex="cardName" />
        <Table.Column align="left" title="礼品卡code" dataIndex="cardCode" />
        <Table.Column align="left" title="描述" cell={render} dataIndex="cardDesc" />
        <Table.Column
          align="left"
          title="激活方式"
          cell={(value, index, data) => <div>{data.cardType ? '卡号和密码' : '仅卡号'}</div>}
        />
        <Table.Column
          width={160}
          align="left"
          title="创建时间"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(new Date(data.createTime))}</div>}
        />
        <Table.Column
          width={170}
          align="left"
          title="有效期"
          cell={(value, index, data) => {
            return data.startTime && data.validNoEndTime === 0 ? (
              <div>
                <div>起：{data.startTime ? format.formatDateTimeDayjs(new Date(data.startTime)) : '-'}</div>
                <div>止：{data.endTime ? format.formatDateTimeDayjs(new Date(data.endTime)) : '-'}</div>
              </div>
            ) : (
              '永久有效'
            );
          }}
        />
        <Table.Column align="left" title="剩余数量" dataIndex="cardSurplus" />
        <Table.Column
          align="left"
          title="时效状态"
          cell={(value, index, data) => (
            <span className={styles[parsePointResListStatusColor(data.planStatus)]}>
              {parsePointResListStatus(data.planStatus)}
            </span>
          )}
        />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        className={styles.pagination}
      />
      <Dialog
        title="创建礼品卡"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        <CreatePlan handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} />
      </Dialog>
    </div>
  );
};
