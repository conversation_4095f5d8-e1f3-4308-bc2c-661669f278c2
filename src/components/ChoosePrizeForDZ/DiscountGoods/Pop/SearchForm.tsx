import { useState } from 'react';
import * as React from 'react';
import { Button, Form, Input } from '@alifd/next';

interface FormData {
  planName: string;
  skuId: string;
  skuName: string;
}
const initBeanSearchData: FormData = {
  planName: '',
  skuId: '',
  skuName: '',
};

export default ({ onSearch }: any) => {
  const [beanSearchData, setBeanSearchData] = useState(initBeanSearchData);

  /**
   * 京豆筛选框赋值
   * @param {*} value 筛选值
   */
  const selectData = (value: any) => {
    setBeanSearchData({
      ...beanSearchData,
      ...value,
    });
  };

  const onSearchClick = () => {
    onSearch(beanSearchData);
  };

  const onResetClick = () => {
    setBeanSearchData(initBeanSearchData);
    onSearch(initBeanSearchData);
  };

  return (
    <div>
      <Form inline>
        <Form.Item className="item" label="折扣权益名称：">
          <Input
            value={beanSearchData.planName}
            className="dialog-search-ctrl"
            placeholder="请输入折扣权益名称"
            onChange={(planName) => selectData({ planName })}
          />
        </Form.Item>
        <Form.Item className="item" label="SKU编码：">
          <Input
            value={beanSearchData.skuId}
            className="dialog-search-ctrl"
            placeholder="请输入SKU编码"
            onChange={(skuId) => {
              const numericValue = skuId.replace(/\D/g, '');
              selectData({ skuId: numericValue });
            }}
          />
        </Form.Item>
        <Form.Item className="item" label="SKU商品名称：">
          <Input
            value={beanSearchData.skuName}
            className="dialog-search-ctrl"
            placeholder="请输入SKU商品名称"
            onChange={(skuName) => selectData({ skuName })}
          />
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSearchClick}>
            查询
          </Button>
          <Button style={{ marginLeft: 10 }} type="normal" onClick={onResetClick}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
