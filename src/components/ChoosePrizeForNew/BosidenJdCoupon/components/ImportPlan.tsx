/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023/9/5 10:24
 * Description: 导入优惠券
 */

import React, { useState } from 'react';
import { Button, Form, Input, Select, Table, Message } from '@alifd/next';
import CouponThumb from '@/components/CouponThumb';
import LzTag from '@/components/LzTag';
import { addResPrizeCouponSO, getFreeCouponInfo } from '@/api/prize';

const formItemLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

export default ({ close }) => {
  // 搜索条件
  const [conditions, setConditions] = useState({
    putKey: '',
    planName: '',
    discountType: 1,
    rangeType: 1,
  });

  // 修改搜索条件
  const changeCondition = (condition) => {
    setConditions({
      ...conditions,
      ...condition,
    });
  };

  // 查询到的优惠券信息
  const [couponData, setCouponData] = useState<any[]>([]);

  // 查询符合条件的京东优惠券
  const search = async (value, errors) => {
    if (!errors) {
      try {
        const data: any = await getFreeCouponInfo(value);
        if (data) {
          data.couponDiscount = data.discount; // 优惠
          data.couponQuota = data.quota; // 使用条件
          data.couponType = data.prizeType; // 京券/东券
          setCouponData([data]);
        }
        // 返回的数据不符合组件的数据格式转换一下
      } catch (e) {
        Message.error(e.message);
      }
    }
  };
  const importCoupon = async (): Promise<any> => {
    if (!conditions.putKey) {
      Message.error('请输入putKey');
      return false;
    }
    if (!conditions.planName) {
      Message.error('请输入优惠券名称');
      return false;
    }
    if (!conditions.discountType) {
      Message.error('请选择优惠方式');
      return false;
    }
    if (!conditions.rangeType) {
      Message.error('请选择范围类型');
      return false;
    }
    try {
      await addResPrizeCouponSO(conditions);
      close(true);
    } catch (e) {
      Message.error(e.message);
    }
  };

  return (
    <div className="coupon-importer" style={{ width: 900 }}>
      <div className="section">
        <Form {...formItemLayout}>
          <Form.Item label="putKey：" required requiredMessage="请输入putKey">
            <Input
              className="input-ctrl"
              placeholder="请输入putKey"
              name="putKey"
              onChange={(putKey) => changeCondition({ putKey })}
              value={conditions.putKey}
            />
          </Form.Item>
          <Form.Item
            label="优惠券名称："
            required
            requiredMessage="请输入优惠券名称"
            extra={'注:无法获取自营店铺优惠券名称，需商家手动填写，便于系统内识别'}
          >
            <Input
              className="input-ctrl"
              placeholder="请输入优惠券名称"
              name="planName"
              maxLength={12}
              onChange={(planName) => changeCondition({ planName })}
              value={conditions.planName}
            />
          </Form.Item>
          <Form.Item
            label="优惠方式："
            required
            requiredMessage="请选择优惠方式"
            extra={<div>注:无法获取京东自营店铺优惠券的优惠方式，默认为“满减”</div>}
          >
            <Select
              className="input-ctrl"
              name="discountType"
              onChange={(discountType) => changeCondition({ discountType })}
              showSearch
              hasClear
              value={conditions.discountType}
            >
              <Select.Option value={1}>满减</Select.Option>
              <Select.Option value={2}>满折</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="范围类型："
            required
            requiredMessage="请选择范围类型"
            extra={
              conditions.rangeType == 1 ? (
                <div style={{ color: 'red' }}>
                  注：店铺券适用于全店所有商品；无法获取京东自营店铺优惠券的范围类型，需商家手动设置，请务必与所申请的券范围类型一致
                </div>
              ) : (
                <div style={{ color: 'red' }}>
                  注:商品券适用于指定的部分商品;无法获取京东自营店铺优惠券的范围类型，需商家手动设置，请务必与所申请的券范围类型一致
                </div>
              )
            }
          >
            <Select
              className="input-ctrl"
              name="rangeType"
              onChange={(rangeType) => changeCondition({ rangeType })}
              showSearch
              hasClear
              value={conditions.rangeType}
            >
              <Select.Option value={1}>店铺券</Select.Option>
              <Select.Option value={2}>商品券</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label=" ">
            <Form.Submit type="primary" onClick={search}>
              查询
            </Form.Submit>
          </Form.Item>

          <Form.Item label=" ">
            <Table.StickyLock dataSource={couponData}>
              <Table.Column title="优惠券名称" dataIndex="planName" />
              <Table.Column
                title="优惠券信息"
                width={180}
                cell={(value, index, coupon) => <CouponThumb info={coupon} />}
              />
              <Table.Column
                title="领取时间"
                width={180}
                cell={(value, index, coupon) => (
                  <div>
                    <p style={{ margin: 0 }}>起：{coupon.activityStartTime}</p>
                    <p style={{ margin: 0 }}>止：{coupon.activityEndTime}</p>
                  </div>
                )}
              />
              <Table.Column
                title="时效状态"
                width={90}
                dataIndex="planStatus"
                cell={(status) =>
                  [
                    <LzTag.Warning>待生效</LzTag.Warning>,
                    <LzTag.Success>已生效</LzTag.Success>,
                    <LzTag.Disable>已过期</LzTag.Disable>,
                  ][status - 1]
                }
              />
            </Table.StickyLock>
          </Form.Item>
          <Form.Item label=" ">
            <Button type="primary" onClick={importCoupon}>
              导入
            </Button>
            <Button style={{ marginLeft: 10 }} onClick={close}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};
