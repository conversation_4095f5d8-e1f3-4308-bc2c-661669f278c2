/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023/9/5 10:05
 * Description:
 */
import styles from '../index.module.scss';
import { Button, DatePicker2, Form, Input, NumberPicker, Radio, Message, Dialog, Loading } from '@alifd/next';
import { useState } from 'react';
import * as React from 'react';
import Moment from 'moment';
import { addResPrizeHongBao } from '@/api/prize';

export default ({ handleCancel, handleSubmit }) => {
  const { RangePicker } = DatePicker2;
  const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  // 每人红包金额
  const [redValue, setRedValue] = useState<number | null>(null);
  // 红包有效期选项
  const [hongBaoExpireType, setHongBaoExpireType] = useState(2);
  // 红包有效期范围
  const [packetDataTime, setPacketDataTime] = useState([]);
  // 红包有效期时间天数
  const [hongBaoExpireDay, setHongBaoExpireDay] = useState(30);
  // 总发放份数
  const [redNum, setRedNum] = useState('');
  // 每人每天限领
  const [limitDay, setLimitDay] = useState(1);
  // 每人计划期间限领
  const [limitPlan, setLimitPlan] = useState(3);
  const [loading, setLoading] = useState(false);

  // 校验时间
  const checkPutDate = (rule, value: any[], callback: Function) => {
    if (value[1] === null) {
      callback('请选择计划结束时间');
    } else {
      callback();
    }
  };
  // 提交创建红包表单
  // eslint-disable-next-line complexity
  const onOkCreate = (value, errors: unknown): void | boolean => {
    if (!errors) {
      if (!value.planName) {
        Message.error('请输入计划名称');
        return;
      }
      if (!value.putTime || !value.putTime[1] || !value.putTime[0]) {
        Message.error('请选择计划时间');
        return;
      }
      if (+(+redValue! * +redNum).toFixed(2) < 1) {
        Message.error('计划总金额最小1元');
        return;
      }
      // if (+(+redValue * +redNum).toFixed(2) > +(state.accountData as any).availableNum) {
      //   Message.error('活动总费用不可超过可用余额');
      //   return;
      //
      if (hongBaoExpireType === 1 && (!packetDataTime[0] || !packetDataTime[1])) {
        Message.error('红包有效期时间不可为空');
        return;
      }
      if (!limitDay) {
        Message.error('每人每天限领不可为空');
        return;
      } else if (!limitPlan) {
        Message.error('每人计划期间限领不可为空');
        return;
      }
      const hongBaoBeginTime = (packetDataTime[0] as any)?.format('YYYY-MM-DD 00:00:00');
      const hongBaoEndTime = (packetDataTime[1] as any)?.format('YYYY-MM-DD 23:59:59');
      const timeLessThan = hongBaoBeginTime > value.putTime[0]?.format('YYYY-MM-DD HH:mm:ss');
      const timeGreaterThan = hongBaoEndTime < value.putTime[1]?.format('YYYY-MM-DD HH:mm:ss');

      if (hongBaoExpireType === 1 && (timeLessThan || timeGreaterThan)) {
        let msg: any;
        if (timeLessThan) {
          msg = `红包有效期开始时间需小于等于计划开始时间`;
        }
        if (timeGreaterThan) {
          msg = `红包有效期结束时间需大于等于计划结束时间`;
        }
        if (timeLessThan && timeGreaterThan) {
          msg = `红包有效期开始时间需小于等于计划开始时间,且红包有效期结束时间需大于等于计划结束时间`;
        }
        Message.error(msg);
        return;
      }
      if (hongBaoExpireType === 1 && timeGreaterThan) {
        Message.error('红包有效期结束时间需大于等于计划结束时间');
        return;
      }
      if (hongBaoExpireType === 1 && (packetDataTime[1] as any).diff(packetDataTime[0] as any, 'days') >= 30) {
        Message.error('红包有效期不可大于30天');
        return;
      }
      const valueForm = {
        ...value,
        quantityTotal: redNum,
        aveAmount: redValue,
        sendRule: 1,
        beginDate: value.putTime[0].format('YYYY-MM-DD HH:mm:ss'),
        endDate: value.putTime[1].format('YYYY-MM-DD HH:mm:ss'),
        hongBaoBeginTime,
        hongBaoEndTime,
        hongBaoExpireDay,
        userDayCount: limitDay,
        userActivityCount: limitPlan,
      };
      if (new Date(valueForm.beginDate).getTime() < new Date().getTime()) {
        Message.error('计划起始时间要大于当前时间');
        return;
      }
      setLoading(true);
      addResPrizeHongBao(valueForm)
        .then((res) => {
          Message.success('创建红包计划成功');
          handleSubmit();
          setLoading(false);
        })
        .catch((err) => {
          setLoading(false);
          Message.error(err.message);
        });
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };
  const testValue1 = () => {
    if (!redValue || +redValue < 0.1) {
      setRedValue(0.1);
    }
  };
  const testValueZ = (val) => {
    // val = val.replace(/[^\\.\d]/g, '').replace('.', '');
    setRedNum(val);
  };

  const currentDate = Moment();
  const disabledDateFn = (date, view) => {
    // console.log(date, view)
    switch (view) {
      case 'date':
        // return date.valueOf() <= currentDate.valueOf();
        // return date.diff(currentDate, 'days') < 0 || date.diff(currentDate, 'days') > 30;
        return date.diff(currentDate, 'days') < 0;
      case 'year':
        return date.year() < currentDate.year();
      case 'month':
        return date.year() * 100 + date.month() < currentDate.year() * 100 + currentDate.month();
      default:
        return false;
    }
  };
  const previewImg = (url) => {
    Dialog.show({
      content: <img src={url} alt="" className={styles.previewImg} />,
      footer: false,
    });
  };
  return (
    <div className={styles.CreateJdBeanPlan}>
      <Loading visible={loading}>
        <Form {...formItemLayout} style={{ width: 800 }}>
          <Form.Item
            label="计划名称："
            required
            className={styles.item}
            requiredMessage="请输入计划名称"
            extra={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                将在移动端透出，请谨慎命名{' '}
                <Button
                  text
                  type="primary"
                  onClick={() =>
                    previewImg(
                      '//img10.360buyimg.com/imgzone/jfs/t1/162743/9/39503/11582/6554873aF971f374c/466d00defe56129c.png',
                    )
                  }
                >
                  查看示例
                </Button>
              </div>
            }
          >
            <Input trim placeholder="请输入计划名称" maxLength={20} showLimitHint name="planName" />
          </Form.Item>
          <Form.Item
            label="计划时间："
            required
            className="bean_btn"
            requiredMessage="请选择计划时间"
            validator={checkPutDate}
          >
            <RangePicker disabledDate={disabledDateFn} showTime name="putTime" />
          </Form.Item>
          <Form.Item label="计划描述：" className={styles.item}>
            <Input.TextArea placeholder="请输入计划描述" maxLength={200} showLimitHint name="planContent" />
          </Form.Item>
          <Form.Item
            label="单份红包金额："
            required
            className="bean_btn"
            requiredMessage="请输入单份红包金额"
            help="最小0.1元"
          >
            <NumberPicker
              placeholder="请输入单份红包金额"
              style={{ width: 140 }}
              precision={2}
              onChange={(value) => setRedValue(value)}
              onBlur={testValue1}
              value={redValue as number}
              name="aveAmount"
            />
            &nbsp;元
          </Form.Item>
          <Form.Item label="计划发放数量：" required className="bean_btn" requiredMessage="请输入计划发放数量">
            <Form.Item required requiredMessage="请输入计划发放数量">
              <NumberPicker
                onChange={(value) => testValueZ(value)}
                min={1}
                style={{ width: 140 }}
                placeholder="请输入计划发放数量"
                value={redNum}
                name="quantityTotal"
                max={9999999}
              />
              &nbsp;份
            </Form.Item>
          </Form.Item>
          <Form.Item label="计划总金额：" className={styles.item} help="计划总金额最小1元">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Input
                disabled
                maxLength={20}
                name="planName"
                value={!!redValue && !!redNum ? (+redValue * +redNum).toFixed(2) : ''}
              />
              &nbsp;元
            </div>
            {/* <div style={{color: '#999999', textAlign: 'left'}}>活动总费用最小1元</div> */}
          </Form.Item>
          {/* 增加红包范围 */}
          <Form.Item label="红包有效期：" required requiredMessage="红包有效期类型不可为空">
            <Radio.Group
              name="hongBaoExpireType"
              value={hongBaoExpireType}
              onChange={(e) => {
                if (e === 2) {
                  setHongBaoExpireDay(30);
                }
                setHongBaoExpireType(e as any);
              }}
            >
              {/* 暂时下掉京券选项 */}
              <Radio value={1}>固定时间段</Radio>
              <Radio value={2}>固定天数</Radio>
            </Radio.Group>
            <Form.Item style={{ marginTop: '12px' }}>
              {hongBaoExpireType == 1 ? (
                <Form.Item required requiredMessage="请输入红包有效期">
                  <RangePicker
                    name="time"
                    disabledDate={disabledDateFn}
                    format="YYYY-MM-DD"
                    onChange={(e) => {
                      setPacketDataTime({ ...(e as any) });
                    }}
                  />
                </Form.Item>
              ) : (
                <Form.Item required requiredMessage="请输入红包有效期">
                  红包领取后&nbsp;
                  <NumberPicker
                    name="hongBaoExpireDay"
                    value={hongBaoExpireDay}
                    type="inline"
                    defaultValue={30}
                    min={1}
                    max={30}
                    onChange={(e) => setHongBaoExpireDay(e)}
                  />
                  &nbsp; 天内使用，逾期失效
                </Form.Item>
              )}
            </Form.Item>
          </Form.Item>

          <Form.Item label="领取限制：" required>
            <Form.Item required requiredMessage="请输入每人每天限领次数">
              每人每天限领&nbsp;
              <NumberPicker
                name="limitDay"
                onChange={(value) => setLimitDay(value)}
                value={limitDay}
                defaultValue={1}
                min={1}
                max={100}
                type="inline"
              />
              &nbsp;次
              <div style={{ color: '#999' }}>
                注：请根据计划用途合理设置，若配置于一天可多次领奖的活动，需确保用户可多次领取，以免产生客诉
              </div>
            </Form.Item>
            <Form.Item required requiredMessage="请输入每人计划期间限领次数">
              每人计划期间限领&nbsp;
              <NumberPicker
                name="limitPlan"
                onChange={(value) => setLimitPlan(value)}
                value={limitPlan}
                defaultValue={3}
                min={1}
                max={100}
                type="inline"
              />
              &nbsp;次
              <div style={{ color: '#999' }}>
                请根据计划用途合理设置，若将计划复用至多个活动，请确保所设置数值足够大，以避免出现用户在前一活动已领，后续活动因达到领取上限无法领取的情况
              </div>
            </Form.Item>
          </Form.Item>

          <Form.Item label=" ">
            <Form.Submit validate type="primary" disabled={loading} onClick={onOkCreate}>
              提交
            </Form.Submit>
            <Button onClick={onCloseCreate} style={{ marginLeft: '10px' }}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Loading>
    </div>
  );
};
