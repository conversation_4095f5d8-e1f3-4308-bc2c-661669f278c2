import { Form, NumberPicker, Grid, Field, Message, Icon, Input, Button, Radio } from '@alifd/next';
import React, { useEffect, useReducer, useState } from 'react';
import styles from './index.module.scss';
import ChoosePromotion from '@/components/ChoosePromotion';
import ChooseGoods from '@/components/ChooseGoods';
import LzDialog from '@/components/LzDialog';
import { FormLayout } from '@/pages/activity/20003/2002/util';
import { activityEditDisabled } from '@/utils';
import LzImageSelector from '@/components/LzImageSelector';

interface ComponentProps {
  [propName: string]: any;
}

const FormItemLayout: FormLayout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
  constructor(skuId: number) {
    this.jdPrice = '0';
    this.seq = 0;
    this.skuId = skuId;
    this.skuMainPicture =
      '//img10.360buyimg.com/imgzone/jfs/t1/44645/27/24207/24564/659d00d9F0467f8c5/ef658035745cc059.png';
    this.skuName = '虚拟sku名称';
  }
}

const PropertyJdPromotion = ({
  editValue,
  onChange,
  onCancel,
  hasLimit = true,
  hasProbability = true,
  formData,
  width,
  height,
  prizeNameLength,
  sendTotalCountMax = 999999999,
  promoType = ['1'],
}: ComponentProps) => {
  const equityImg = '';
  const defaultValue = {
    prizeKey: null,
    prizeType: 11,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
    unitCount: 1,
    tokenType: '1',
    tokenTotalType: 1,
    kickToken: 1,
  };
  const field = Field.useField();

  const [inputSkuList, setInputSkuList] = useState('');
  const [showIconDialog, setShowIconDialog] = useState(false);
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);

  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };

  const submit = (values: any, errors: any): boolean | void => {
    if (!prizeData.tokenPrize) {
      Message.error(`请选择令牌`);
      return false;
    }

    if (!prizeData.skuList) {
      Message.error(`请选择商品`);
      return false;
    }

    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    if (hasProbability && +prizeData.probability <= 0) {
      Message.error(`中奖概率必须大于0`);
      return false;
    }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100%`);
      return false;
    }
    // 如果是总价促销令牌，且是满减类型，需要默认传一个sku
    if (prizeData.tokenType === '10' && prizeData.tokenTotalType === 0) {
      prizeData.skuList = [{ skuId: '1' }];
    }
    !errors && onChange(prizeData);
  };

  // 选择SKU
  const chooseSku = (skuList) => {
    setData({
      prizeImg: skuList[0]?.skuMainPicture,
      skuId: skuList[0].skuId,
      skuMainPicture: skuList[0].skuMainPicture,
      skuName: skuList[0].skuName,
      skuList,
    });
  };

  // 展示弹窗
  const toIconDialog = () => {
    setShowIconDialog(true);
  };

  // 选择令牌
  const onTokenChange = (data): boolean | void => {
    if (data) {
      prizeData.tokenPrize = data;
      prizeData.prizeName = data.prizeName;
      prizeData.prizeKey = data.prizeKey;
      field.setError('prizeName', '');
      // console.log('prizeData1', prizeData.tokenPrize);
    } else {
      prizeData.tokenPrize = null;
      prizeData.prizeKey = null;
    }
    setData(prizeData);
  };

  // 处理用户输入SKU
  const checkInputSku = (val) => {
    const regex = /^[0-9,]*$/;
    // 使用正则表达式进行验证
    if (!regex.test(val)) {
      Message.error('只允许输入数字及英文逗号');
      return;
    }
    if (val.match(/,,/)) {
      Message.error('禁止连续输入两个及以上的逗号');
      return;
    }
    const skuArr = val.split(',');
    // 检查是否有超过 20 位数字的 SKU
    const isSkuTooLong = skuArr.some((skuId) => skuId.length > 15);
    if (isSkuTooLong) {
      Message.error('SKU 限制为 15 位');
      return;
    }
    if (skuArr.length > 10) {
      Message.error('最多添加10个SKU');
      return;
    }
    const skuList: Sku[] = [];
    skuArr.forEach((skuId) => {
      if (skuId) {
        skuList.push(new Sku(parseInt(skuId, 10)));
      }
      // if (skuId) {
      //   try {
      //     const bigIntSkuId = skuId.toString();
      //     giftSkuList.push(new Sku(bigIntSkuId));
      //   } catch (e) {
      //     Message.error(`无效的 SKU: ${skuId}`);
      //   }
      // }
    });
    setInputSkuList(val);
    setData({ skuList });
  };

  useEffect(() => {
    if (promoType.length === 1) {
      setData({ tokenType: promoType[0] });
    }
    if (editValue) {
      const skuStr = editValue.skuList.map((sku) => sku.skuId).join(',');
      setInputSkuList(skuStr);
    }
  }, []);

  return (
    <div className={styles.PropertyJdPromotion}>
      <Form field={field} {...FormItemLayout}>
        <Form.Item label="令牌类型" disabled={promoType.length <= 1}>
          <Radio.Group
            value={prizeData.tokenType}
            onChange={(data) => {
              setInputSkuList('');
              setData({
                tokenType: data,
                skuList: [],
                tokenPrize: null,
                prizeKey: null,
                originalPrize: '',
                discountedPrize: '',
                sendTotalCount: '',
                tokenTime: '',
              });
            }}
          >
            <Radio value={'1'}>单品促销令牌</Radio>
            <Radio value={'10'}>总价促销令牌</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="选择令牌" required>
          <ChoosePromotion
            disabled={activityEditDisabled()}
            promoType={prizeData.tokenType}
            submit={onTokenChange}
            value={prizeData.tokenPrize ?? null}
          />
          <div className={styles.FormItemExtra}>
            注意：在申请令牌时，注意勾选允许使用1次，避免一个令牌多次使用
            <Icon
              style={{ color: '#FF3333', marginLeft: '10px', cursor: 'pointer' }}
              type="help"
              onClick={toIconDialog}
            />
          </div>
        </Form.Item>

        {prizeData.tokenType === '1' && (
          <>
            <Form.Item label="选择商品" required requiredMessage={'添加商品'}>
              <ChooseGoods value={prizeData.skuList} onChange={chooseSku} max={1} />
              <div className={styles.FormItemExtra}>
                注意：商品必须选择与令牌绑定的SKU，且设置了SKU的限购数量，否则用户会无限购买
              </div>
            </Form.Item>
            {/* <Form.Item label="是否踢令牌"> */}
            {/*  <Radio.Group */}
            {/*    value={prizeData.kickToken} */}
            {/*    onChange={(kickToken) => { */}
            {/*      setData({ kickToken }); */}
            {/*    }} */}
            {/*  > */}
            {/*    <Radio value={0}>享受权益后踢出</Radio> */}
            {/*    <Radio value={1}>享受权益后不踢出</Radio> */}
            {/*  </Radio.Group> */}
            {/* </Form.Item> */}
            <Form.Item label="划线价" required requiredMessage="请输入划线价">
              <NumberPicker
                placeholder="请输入划线价"
                className={styles.formNumberPicker}
                name="originalPrize"
                min={1}
                max={9999999}
                step={1}
                precision={2}
                type="inline"
                value={prizeData.originalPrize}
                onChange={(originalPrize) => {
                  setData({ originalPrize });
                }}
              />
              元
            </Form.Item>
            <Form.Item label="折扣价" required requiredMessage="请输入折扣价">
              <NumberPicker
                placeholder="请输入折扣价"
                className={styles.formNumberPicker}
                name="discountedPrize"
                min={1}
                max={9999999}
                step={1}
                precision={2}
                type="inline"
                value={prizeData.discountedPrize}
                onChange={(discountedPrize) => {
                  setData({ discountedPrize });
                }}
              />
              元
            </Form.Item>
            {/* <Form.Item label="单份价值" required requiredMessage="请输入单份价值"> */}
            {/*  <NumberPicker */}
            {/*    className={styles.formNumberPicker} */}
            {/*    onChange={(unitPrice: any) => setData({ unitPrice })} */}
            {/*    name="unitPrice" */}
            {/*    type="inline" */}
            {/*    precision={2} */}
            {/*    max={9999999} */}
            {/*    min={1} */}
            {/*    value={prizeData.unitPrice} */}
            {/*  /> */}
            {/*  元 */}
            {/* </Form.Item> */}
          </>
        )}

        {prizeData.tokenType === '10' && (
          <>
            <Form.Item label="总价类型">
              <Radio.Group
                value={prizeData.tokenTotalType}
                onChange={(tokenTotalType) => {
                  setInputSkuList('');
                  setData({
                    tokenTotalType,
                    skuList: [],
                    tokenPrize: null,
                    prizeKey: null,
                    originalPrize: '',
                    discountedPrize: '',
                    sendTotalCount: '',
                    tokenTime: '',
                  });
                }}
              >
                <Radio value={1}>满赠</Radio>
                <Radio value={0}>满减</Radio>
              </Radio.Group>
              <div className={styles.tip}>
                {prizeData.tokenTotalType === 1 && <div>提示：满赠令牌可能会被同一用户多次使用。</div>}
                {prizeData.tokenTotalType === 0 && <div>提示：满减令牌“令牌有效时长”内皆可使用。</div>}
              </div>
            </Form.Item>
            {prizeData.tokenTotalType === 0 && (
              <Form.Item label="令牌有效时长" required requiredMessage="请输入令牌有效时长">
                <NumberPicker
                  placeholder="请输入令牌有效时长"
                  className={styles.formNumberPicker}
                  name="tokenTime"
                  min={1}
                  max={100}
                  step={1}
                  type="inline"
                  value={prizeData.tokenTime}
                  onChange={(tokenTime) => {
                    setData({ tokenTime });
                  }}
                />
                小时
              </Form.Item>
            )}
            {prizeData.tokenTotalType === 1 && (
              <>
                <Form.Item label="赠品SKU" required requiredMessage="请输入赠品SKU">
                  <Input.TextArea
                    name="inputSkuList"
                    value={inputSkuList}
                    placeholder="请输入令牌所绑定的总价促销活动的赠品SKU，多个以英文逗号分隔"
                    maxLength={170}
                    style={{ width: '300px' }}
                    showLimitHint
                    hasBorder
                    rows={8}
                    className="form-input-ctrl"
                    onChange={checkInputSku}
                  />
                  <div className={styles.tip}>
                    1. 添加令牌所绑定的总价促销活动的赠品SKU，最多添加10个；
                    <div>
                      2. <span style={{ color: 'red' }}>不要填写影子分身SKU，否则会造成用户刷单。</span>
                    </div>
                  </div>
                </Form.Item>
                {/* <Form.Item label="满赠门槛" required requiredMessage="请输入满赠门槛">
                  <NumberPicker
                    className={styles.formNumberPicker}
                    type="inline"
                    min={1}
                    max={9999999}
                    name="fullGiftThreshold"
                    value={prizeData.fullGiftThreshold}
                    onChange={(fullGiftThreshold) => setData({ fullGiftThreshold })}
                  />
                  元
                  <div className={styles.tip}>
                    务必与官方后台设置信息一致，满赠门槛会在活动规则、移动端展示{' '}
                    <Balloon
                      v2
                      trigger={
                        <Button text type={'primary'}>
                          查看官方设置示例
                        </Button>
                      }
                      triggerType="hover"
                      closable={false}
                      popupClassName={styles.balloon}
                    >
                      <div>
                        <img
                          style={{ width: '550px' }}
                          src="//img10.360buyimg.com/imgzone/jfs/t1/96612/28/48228/18239/655ac235F12454c02/81cabf0d490d2b6d.png"
                          alt=""
                        />
                      </div>
                    </Balloon>
                  </div>
                </Form.Item> */}
              </>
            )}
          </>
        )}

        <Form.Item label="发放份数" required requiredMessage="请输入发放份数">
          <NumberPicker
            className={styles.formNumberPicker}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount })}
            type="inline"
            min={1}
            max={sendTotalCountMax}
            disabled={activityEditDisabled()}
          />
          份
          <div className={styles.tip}>
            <div>1. 达到人数上限后，用户将不可参与此促销活动</div>
            <div>2. 请根据累计最大参与人数预留赠品，请保证赠品库存充足；</div>
          </div>
        </Form.Item>
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片" required requiredMessage="请上传奖品图片">
          <Input htmlType="hidden" name="prizeImg" value={prizeData.prizeImg} />
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                  field.setErrors({ prizeImg: '' });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: prizeData.skuMainPicture ? prizeData.skuMainPicture : '' });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>

        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <LzDialog
        title={false}
        visible={showIconDialog}
        footer={false}
        onClose={() => setShowIconDialog(false)}
        style={{ width: '80%' }}
      >
        <img
          style={{ width: '100%' }}
          src="//img10.360buyimg.com/imgzone/jfs/t1/112695/34/41067/44228/65e19000F65eb2b97/3f6c3723766cb9c8.png"
          alt=""
        />
      </LzDialog>
    </div>
  );
};

export default PropertyJdPromotion;
