import React, { useEffect, useState } from 'react';
import { Button, Icon } from '@alifd/next';
import SkuSelector from './components/SkuSelector';
import LzDialog from '@/components/LzDialog';

const emptyFunction = () => {};

export default (props) => {
  const { defaultValue, disabled, value, max = 0, planId, couponType, onChange = emptyFunction } = props;
  const [visible, setVisible] = useState(false);
  const [skus, setSkus] = useState(defaultValue || []);
  const [filterList] = useState([]);

  const onChooseGoodsClick = () => {
    setVisible(true);
  };

  const onCloseDialog = () => {
    setVisible(false);
  };

  const onSelectedRow = (data) => {
    data.forEach((val, idx) => {
      val.seq = idx;
    });
    onChange(data);
    setSkus(data);
    setVisible(false);
  };

  useEffect(() => {
    setSkus(value || []);
  }, [value]);

  return (
    <span>
      {!disabled && (
        <>
          <Button type="primary" onClick={onChooseGoodsClick} style={{ marginBottom: 10 }}>
            <Icon type="add" />
            添加商品{max > 0 ? (skus && skus.length > 0 ? `（${skus.length}/${max}）` : `（0/${max}）`) : ''}
          </Button>
        </>
      )}
      {/* 选择选择商品弹窗 */}
      <LzDialog
        title="选择商品"
        className="lz-dialog-medium"
        style={{ width: 921 }}
        visible={visible}
        onClose={onCloseDialog}
        onCancel={onCloseDialog}
        footer={false}
      >
        <div style={{ maxHeight: '650px' }}>
          <SkuSelector
            max={max}
            value={skus}
            planId={planId}
            couponType={couponType}
            filterList={filterList}
            confirm={onSelectedRow}
            cancel={onCloseDialog}
          />
        </div>
      </LzDialog>
    </span>
  );
};
