/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024/6/12 14:54
 * Description:
 */
export const initData = {
  pageNum: 1,
  pageSize: 20,
  total: 0,
  activityName: '',
  activityType: [],
  dateRange: [],
  activeKey: '2',
};
function addQueryParam(url, key, value) {
  const params = new URLSearchParams(url.search.slice(1));
  params.set(key, value);
  url.search = params.toString();
  // @ts-ignore
  history.replaceState(null, null, url.href);
}
const bsdPager = {
  state: {
    ...initData,
  },
  reducers: {
    setState(prevState, info) {
      const newState = {
        ...prevState,
        ...info,
      };
      const currentUrl = new URL(window.location.href);
      addQueryParam(currentUrl, 'params', JSON.stringify(newState));
      return newState;
    },
  },
};
export default bsdPager;
