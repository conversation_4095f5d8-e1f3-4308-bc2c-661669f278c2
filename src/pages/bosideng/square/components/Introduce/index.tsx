import React from 'react';
import { <PERSON><PERSON>, Button } from '@alifd/next';
import styles from './style.module.scss';
import type10107 from './10107';

export default (props) => {
  const { activityType } = props;

  const introduceMap = {
    10107: type10107,
  };

  const Introduce = introduceMap[activityType];

  const introduceEle = Introduce ? (
    <Balloon
      v2
      trigger={
        <Button text type="primary">
          玩法介绍
        </Button>
      }
      triggerType="hover"
      closable={false}
      popupClassName={styles.balloon}
    >
      <Introduce />
    </Balloon>
  ) : null;
  return introduceEle;
};
