.guidance_box {
  //height: calc(100vh - 52px);
  background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/210114/17/40873/15624/650551d3F15ad9be9/ddd481886379e92d.png");
  background-repeat: no-repeat;
  background-size: cover;
  height: calc(100vh - 50px);
  margin: -20px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none;
  }
  .header {
    width: 100%;
    height: 280px;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: space-around;
    padding: 50px 0;
    position: relative;
    .guidance_title {
      font-size: 36px;
      font-weight: 600;
      color: #333;
    }
    .guidance_subb_title {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0,0,0,0.45);
    }
    .guidance_btn {
      margin-top: 32px;
      width: 234px;
      height: 40px;
      background: #39f;
      box-shadow: 0 10px 16px 2px rgba(51,153,255,0.31);
      border-radius: 4px;
      font-size: 14px;
      color: #fff;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      img {
        width: 14px;
        height: 14px;
        margin-left: 10px;
      }
    }
    .customer_service_box {
      position: absolute;
      right: 20px;
      top: 10px;
      width: 100px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 400;
      color: #9e9e9e;
      cursor: pointer;
      img {
        width: 100px;
        height: 100px;
      }
    }
  }
  .content {
    display: flex;
    justify-content: space-between;
    .left {
      display: flex;
      flex-direction: column;
      padding-left: 40px;
      max-width: 380px;
      .des_box {
        margin-bottom: 20px;
        .left_title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          display: flex;
          align-items: center;
          .dot {
            display: inline-block;
            width: 20px;
            height: 20px;
            margin-right: 10px ;
          }
        }
        p {
          padding: 0;
          margin: 0  0 0 30px;
          font-size: 14px;
          color: rgba(0,0,0,0.65);
        }
      }
    }
    .middle {
      width: 200px;
      height: 392px;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/100946/37/44911/12286/65093f82F2f771e8a/b4624e4d231a1983.png");
      background-repeat: no-repeat;
      background-size: 100%;
      position: relative;
    }
    .right {
      display: flex;
      justify-content: space-around;
      margin-right: 10px;
      .des {
        margin: 10px auto;
      }
      .imgBox {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        width: 440px;
        gap: 15px;
        img {
          width: 200px;
        }
      }
    }
  }
}
.balloonBox {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  color: #3d7fff;
  font-size: 12px;
  img {
    width: 200px;
  }
}
