.body {
  width: 270px;
  background: #fff;
  border-radius: 10px;
  .btn_group {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    .btn {
      margin-left: 10px;
    }
  }
  .step1 {
    height: 200px;
    .noticeTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .tip {
      color: #3dafff;
      margin: 10px 0;
    }
    .step {

      font-weight: bold;
      font-size: 14px;
    }
  }
  .step2 {
    height: 140px;
    .noticeTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .step {

      font-weight: bold;
      font-size: 14px;
    }
  }
  .step3 {
    height: 160px;
    .noticeTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .step {
      font-weight: bold;
      font-size: 14px;
    }
  }
}
