import React from 'react';

import { Form, Button, Grid, Icon, Input } from '@alifd/next';
import TooltipLabel from '@/pages/smartCard/MemberCard/components/TooltipLabel';
import ImageSelector from '@/components/LzImageSelector';
import styles from './index.module.scss';

const CardRight = ({ item, index, setForm, formData, addHot, deleteClick }) => {
  return (
    <div className={styles.right}>
      <div className={styles.main}>
        <Form labelCol={{ fixedSpan: 8 }} wrapperCol={{ span: 16 }}>
          <div>
            <div style={{ color: '#333', fontSize: '16px', marginBottom: '10px' }}>
              <TooltipLabel
                position="left"
                label="自定义图片热区"
                tooltip=""
                img="//img10.360buyimg.com/imgzone/jfs/t1/151355/40/39798/8571/653f769bF0507d7e5/1a3c30560941bd93.png"
              />
            </div>
            <div style={{ marginTop: '20px' }}>
              <p>
                图片要求：(1)图片尺寸：叠卡滑动660px*190px
                ，单卡滑动480px*220px；(2)图片大小：不超过1M；(3)图片格式：JPG、JPEG、PNG、GIF
              </p>
              <p>链接要求：(1)链接须包含.isvjcloud.com，.isvjd.com或.jd.com；(2)只能以http://，https://开头</p>
            </div>
            <Form.Item>
              <Button
                style={{ margin: '20px auto', width: '100%', color: '#3399FF' }}
                type="normal"
                disabled={item.hot.length == 6}
                onClick={() => {
                  addHot(index);
                }}
              >
                <Icon className="iconfont icon-a-1plus" style={{ marginRight: 10 }} />
                {`添加热区（${item.hot.length}/6）`}
              </Button>
              <div className={styles.hotDivBox}>
                {item.hot.map((v, i) => (
                  <div className={styles.hotDiv} key={i}>
                    {i > 2 && (
                      <i
                        onClick={() => {
                          deleteClick(index, i);
                        }}
                        className={`${styles.hotDlete} iconfont icon-a-fill17`}
                      />
                    )}
                    <div>
                      <Form.Item>
                        <Grid.Row>
                          <Form.Item style={{ marginRight: 30, marginBottom: 0 }}>
                            <ImageSelector
                              value={v.img}
                              width={formData.cardInfo[index].swiperType == 2 ? 660 : 480}
                              height={formData.cardInfo[index].swiperType == 2 ? 190 : 220}
                              bgWidth={272}
                              bgHeight={137}
                              onChange={(img) => {
                                formData.cardInfo[index].hot[i].img = img;
                                setForm(formData);
                              }}
                            />
                          </Form.Item>
                        </Grid.Row>
                      </Form.Item>
                      <Form.Item>
                        <Input
                          trim
                          placeholder="请输入活动区链接,无需跳转可不填"
                          name="url"
                          value={v.url}
                          onChange={(url) => {
                            formData.cardInfo[index].hot[i].url = url;
                            setForm(formData);
                          }}
                          style={{ minWidth: '272px' }}
                        />
                      </Form.Item>
                    </div>
                  </div>
                ))}
              </div>
            </Form.Item>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default CardRight;
