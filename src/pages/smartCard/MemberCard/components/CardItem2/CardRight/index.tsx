import React from 'react';

import { Form, Select, Button, Grid, Icon, Input } from '@alifd/next';
import ImageSelector from '@/components/LzImageSelector';
import TooltipLabel from '@/pages/smartCard/MemberCard/components/TooltipLabel';
import styles from './index.module.scss';

const CardRight = ({ item, index, setForm, formData, copyMemberHot, addHot, deleteClick }) => {
  return (
    <div className={styles.right}>
      <div className={styles.controlWrapper}>
        <div className={styles.main}>
          <Form labelCol={{ fixedSpan: 8 }} wrapperCol={{ span: 16 }}>
            <div>
              <div>
                <div style={{ color: '#333', fontSize: '16px', marginBottom: '10px' }}>
                  <TooltipLabel
                    position="left"
                    label="自定义图片热区"
                    tooltip=""
                    img="//img10.360buyimg.com/imgzone/jfs/t1/151355/40/39798/8571/653f769bF0507d7e5/1a3c30560941bd93.png"
                  />
                </div>
                <Form labelTextAlign="left" labelCol={{ fixedSpan: 8 }} wrapperCol={{ span: 16 }}>
                  <Form.Item
                    style={{ margin: 0 }}
                    label={
                      <TooltipLabel
                        position="left"
                        label="引用其他等级热区"
                        tooltip="将其他等级的热区设置引用至当前等级，会覆盖当前等级热区设置，非必选"
                        img=""
                      />
                    }
                  >
                    <Select
                      name="copyType"
                      style={{ margin: 0, width: '215px', marginLeft: '-30px' }}
                      value={formData.cardInfo[index].copyType}
                      followTrigger
                      onChange={(member) => {
                        copyMemberHot(member, index);
                        formData.cardInfo[index].copyType = member;
                      }}
                    >
                      <Select.Option value={-1}>不引用</Select.Option>
                      {formData.cardInfo &&
                        formData.cardInfo.map((it) => (
                          <Select.Option disabled={index == it.type} key={it.type} value={it.type}>
                            {it.name}
                          </Select.Option>
                        ))}
                    </Select>
                  </Form.Item>
                </Form>
              </div>
              <div style={{ marginTop: '20px' }}>
                <p>图片要求：(1)图片尺寸：290px * 170px；(2)图片大小：不超过1M；(3)图片格式：JPG、JPEG、PNG、GIF</p>
                <p>链接要求：(1)链接须包含.isvjcloud.com，.isvjd.com或.jd.com；(2)只能以http://，https://开头</p>
              </div>
              <Form.Item>
                <Button
                  style={{ margin: '20px auto', width: '100%', color: '#3399FF' }}
                  type="normal"
                  disabled={item.hot.length == 6}
                  onClick={() => {
                    addHot(index);
                    formData.cardInfo[index].copyType = -1;
                  }}
                >
                  <Icon className="iconfont icon-a-1plus" style={{ marginRight: 10 }} />
                  {`添加热区（${item.hot.length}/6）`}
                </Button>

                <div className={styles.hotDivBox}>
                  {item.hot.map((v, i) => (
                    <div className={styles.hotDiv} key={i}>
                      {i > 2 && (
                        <i
                          onClick={() => {
                            deleteClick(index, i);
                            formData.cardInfo[index].copyType = -1;
                          }}
                          className={`${styles.hotDlete} iconfont icon-a-fill17`}
                        />
                      )}
                      <div>
                        <Form.Item>
                          <Grid.Row>
                            <Form.Item style={{ marginRight: 30, marginBottom: 0 }}>
                              <ImageSelector
                                value={v.img}
                                width={290}
                                height={170}
                                bgWidth={272}
                                bgHeight={137}
                                onChange={(img) => {
                                  // setHot({img}, i, index);
                                  formData.cardInfo[index].hot[i].img = img;
                                  formData.cardInfo[index].copyType = -1;
                                  setForm(formData);
                                }}
                              />
                            </Form.Item>
                          </Grid.Row>
                        </Form.Item>
                        <Form.Item>
                          <Input
                            trim
                            placeholder="请输入活动区链接,无需跳转可不填"
                            name="url"
                            value={v.url}
                            onChange={(url) => {
                              formData.cardInfo[index].hot[i].url = url;
                              formData.cardInfo[index].copyType = -1;
                              setForm(formData);
                            }}
                            style={{ minWidth: '272px', marginRight: '10px' }}
                          />
                        </Form.Item>
                      </div>
                    </div>
                  ))}
                </div>
              </Form.Item>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CardRight;
