.preview {
  position: relative;
  .bg {
    width: 100%;
    display: block;
  }
  .hotZone {
    position: absolute;
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
  }
  img {
    width: 100%;
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .container {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
  }

  .MemberContainer {
    @extend .container;
    .imgUpload {
      display: flex;

      .tip {
        margin-top: 10px;
      }

      .removeHotZone {
        margin-top: 4px;
        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
    .setting {
      margin-left: 15px;
      .btn {
        width: 300px;
      }
      .urlContainer {
        width: 300px;
        height: 28px;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border-radius: 5px;
        margin-top: 8px;
        position: relative;
        .url {
          width: 100%;
          display: flex;
          overflow-x: scroll;
          white-space: nowrap;
        }
        .url::-webkit-scrollbar {
          display: none;
        }

        i {
          position: absolute;
          right: -17px;
          top: 5px;
          cursor: pointer;
          font-size: 12px;
        }
      }
    }
  }
}
