import React from 'react';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { Divider, Button, Input, Form, Field } from '@alifd/next';
import { deepCopy } from '@/utils';

const formItemLayout = {
  labelCol: {
    fixedSpan: 4,
  },
  wrapperCol: {
    span: 14,
  },
};
export default ({ onSubmit, onClose, editIcon }) => {
  const equityFiled = Field.useField({});
  const [iconInfo, setIconInfo] = React.useState<any>(editIcon ? deepCopy(editIcon) : {});

  const onConfirm = () => {
    let err: any = null;
    equityFiled.validate((errors: any): void => {
      err = errors;
    });
    if (!err) {
      onSubmit(iconInfo, editIcon ? 'edit' : 'add');
      setIconInfo({});
      onClose();
    }
  };
  return (
    <div className={styles.editMemberCard}>
      <Form field={equityFiled} {...formItemLayout} labelAlign={'left'} colon>
        <Form.Item required requiredMessage={'请上传权益图标'} label={'权益图标'} name={`icon`}>
          <Input htmlType={'hidden'} name={`icon`} value={iconInfo.icon} />
          <LzImageSelector
            bgWidth={107}
            bgHeight={130}
            name={`icon`}
            value={iconInfo.icon}
            onChange={(icon) => {
              setIconInfo({ ...iconInfo, icon });
            }}
          />
          <div className={styles.tip}>建议尺寸 110*110px, png格式，500k以内</div>
        </Form.Item>
        <Form.Item required requiredMessage={'请输入icon名称'} label={'icon名称'} name={`text`}>
          <Input
            placeholder={'请输入icon名称'}
            className={styles.levelUrl}
            value={iconInfo.text}
            name={`text`}
            maxLength={20}
            onChange={(text) => {
              setIconInfo({ ...iconInfo, text });
            }}
          />
        </Form.Item>
        <Form.Item label={'icon明细图'} name={`img`}>
          <LzImageSelector
            bgWidth={107}
            bgHeight={130}
            name={`img`}
            value={iconInfo.img}
            onChange={(img) => {
              setIconInfo({ ...iconInfo, img });
            }}
          />
          <div className={styles.tip}>建议尺寸 650*823px, png格式，500k以内</div>
        </Form.Item>
      </Form>
      <Divider />
      <div className={styles.footer}>
        <Button type={'primary'} onClick={onConfirm}>
          确定
        </Button>
        <Button onClick={onClose}>取消</Button>
      </div>
    </div>
  );
};
