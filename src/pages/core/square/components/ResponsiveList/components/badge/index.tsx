import React from 'react';
import styles from './style.module.scss';

function stringToColor(text) {
  if (text.includes('#')) {
    return `#${text.split('#')[1]}`;
  }
  return '#FF3333';
}

function stringWithoutColor(text) {
  if (text.includes('#')) {
    return text.split('#')[0];
  }
  return text;
}

export default ({ badge }: { badge: string }) => {
  const color = stringToColor(badge);
  const text = stringWithoutColor(badge);
  return (
    <div className={styles.Badge} style={{ backgroundColor: color }}>
      <div className={styles.BadgeContext}>{text}</div>
    </div>
  );
};
