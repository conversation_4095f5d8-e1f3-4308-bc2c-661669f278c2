import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Select, Button, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataWinningAddDrawLog, dataWinningAddDrawLogExport } from '@/api/v92006';
import Utils, {deepCopy, downloadExcel, getParams} from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

const STATUS = [
  { label: '全部', value: 0 },
  { label: '未中奖', value: 1 },
  { label: '待发放', value: 2 },
  { label: '已中奖', value: 3 },
  { label: '已失效', value: 4 },
];

const PRIZE_TYPE = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage, isWin: formValue.isWin ==0 ? null : formValue.isWin-1 });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.startDate = dayJs(query.dateRange[0]).format('YYYY-MM-DD HH:mm:ss');
    query.endDate = dayJs(query.dateRange[1]).format('YYYY-MM-DD HH:mm:ss');
    dataWinningAddDrawLog(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    formValue.startDate = dayJs(formValue.dateRange[0]).format('YYYY-MM-DD HH:mm:ss');
    formValue.endDate = dayJs(formValue.dateRange[1]).format('YYYY-MM-DD HH:mm:ss');
    dataWinningAddDrawLogExport(formValue).then((data: any) => downloadExcel(data, '加购抽奖记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="dateRange" label="领取时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
         <Form.Item name="isWin" label="中奖状态">
          <Select followTrigger mode="single" defaultValue="" style={{ marginRight: 8 }} dataSource={STATUS} />
         </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        {/* <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}> */}
        {/*  生成人群包 */}
        {/* </Button> */}
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.encryptPin ? Utils.mask(row.encryptPin) : '-'}
                  {row.encryptPin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.encryptPin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        {/*<Table.Column title="活动名称" dataIndex="activityName" />*/}
        <Table.Column
          title="获奖时间"
          dataIndex="winningTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.winningTime)}</div>}
        />
        <Table.Column
          title="奖品类型"
          cell={(_, index, row) => (
            <div>
              {PRIZE_TYPE[row.prizeType] || '--'}
            </div>
          )}
          dataIndex="prizeType"
        />
        <Table.Column title="奖品名称" dataIndex="prizeName" />
        <Table.Column title="会员等级(领取时)" dataIndex="memberLevel" />
        {/*<Table.Column*/}
        {/*  title="订单号"*/}
        {/*  dataIndex="orderId"*/}
        {/*  cell={(value, index, data) => <div>{data.orderId ?? '--'}</div>}*/}
        {/*/>*/}
        <Table.Column
          title="中奖状态"
          cell={(_, index, row) => (
            <div>
              {STATUS.find((item) => item.value-1 === row.type)?.label || `--`}
            </div>
          )}
          dataIndex="type"
        />
        {/*<Table.Column title="详情" dataIndex="details" />*/}
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {/* <LzDialog */}
      {/*  title="生成人群包" */}
      {/*  className="lz-dialog-mini" */}
      {/*  visible={packVisible} */}
      {/*  footer={false} */}
      {/*  onCancel={() => setPackVisible(false)} */}
      {/*  onClose={() => setPackVisible(false)} */}
      {/* > */}
      {/*  <LzGenerateCrowdBag */}
      {/*    dataUploadPin={lotteryUploadPin} */}
      {/*    formValue={field.getValues()} */}
      {/*    cancel={() => setPackVisible(false)} */}
      {/*  /> */}
      {/* </LzDialog> */}
    </div>
  );
};
