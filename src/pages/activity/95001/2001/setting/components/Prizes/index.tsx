/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle } from 'react';
import { Form, Field, DatePicker2, Message, Radio } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const RadioGroup = Radio.Group;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (receiveRangeData): void => {
    if (format.formatDateTimeDayjs(receiveRangeData[0]) === format.formatDateTimeDayjs(receiveRangeData[1])) {
      Message.error('领奖开始时间不能等于领奖结束时间');
      return;
    }
    // if (format.formatDateTimeDayjs(receiveRangeData[0]) === formData.startTime && !formData.signEndTime) {
    //   Message.error('领奖开始时间不能等于签到开始时间');
    //   return;
    // }
    setData({
      grantStartTime: format.formatDateTimeDayjs(receiveRangeData[0]),
      grantEndTime: format.formatDateTimeDayjs(receiveRangeData[1]),
      endTime: format.formatDateTimeDayjs(receiveRangeData[1]),
    });
  };
  // useEffect(() => {
  //   if (new Date(formData.grantStartTime) < new Date(formData.signEndTime)) {
  //     Message.error('领奖开始时间必须晚于或等于签到结束时间');
  //     setData({
  //       grantStartTime: '',
  //     });
  //   }
  // }, [formData.grantStartTime]);
  // 禁用日期
  // 签到时间校验
  const validateTime = (rule, val, callback): void => {
    if (activityEditDisabled()) {
      callback();
      return;
    }
    const grantStartTime = val[0];
    const grantEndTime = val[1];
    if (grantStartTime && grantEndTime) {
      // 领奖间隔不大于60天
      const diff = dayjs(grantEndTime).diff(dayjs(grantStartTime), 'day');
      if (diff > 60) {
        callback('领奖间隔不大于60天');
        return;
      }
      const diff7 = dayjs(grantStartTime).diff(dayjs(value?.orderEndTime), 'day');
      if (diff7 < 7) {
        callback('领奖开始时间不得早于订单结算截止时间7天后');
        return;
      }
      // 领奖开始时间不得早于报名截止时间
      const isBefore1 = dayjs(grantStartTime).isBefore(value?.applicationEndTime);
      const isBefore2 = dayjs(grantStartTime).isBefore(value?.orderEndTime);
      if (isBefore1 || isBefore2) {
        console.log(5);
        callback('领奖开始时间不得早于报名截止时间，及订单结算截止时间');
        return;
      }
      // 领奖时间不得早于当前时间
      const isBefore = dayjs(grantStartTime).isBefore(dayjs());
      if (isBefore) {
        callback('领奖开始时间不早于当前时间');
        return;
      }
    } else {
      callback('请设置领奖时间');
      return;
    }
    callback('');
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect(() => {
    if (value?.orderStartTime && value?.orderEndTime) {
      field.validatePromise('receiveRangeData');
    }
  }, [value?.orderRestrainRangeData]);

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="发放设置">
        <Message type="notice">
          注：领奖开始当日凌晨为订单结算时间，客户可在订单排名结算完成后，在领奖时间段内可领取奖品，超过领奖截止时间不可领奖
          结算后排名不可改变，为保证结算订单完整，领奖开始时间不得早于订单结算截止时间7天后
        </Message>
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="" />
          <FormItem required label="领奖时间" validator={validateTime}>
            <RangePicker
              className="w-300"
              name="receiveRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={[new Date(formData.grantStartTime), new Date(formData.grantEndTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <div className={styles.tip}>
              注：领奖周期不得超过60天，且领奖开始时间不得早于报名截止时间，及订单结算截止时间
            </div>
          </FormItem>
          <FormItem required label="可领取奖项数量">
            <RadioGroup value={formData.grantType} onChange={(grantType: number) => setData({ grantType })}>
              <Radio id="0" value={0}>
                全部奖项
              </Radio>
              <Radio id="1" value={1}>
                最高奖项（如无库存不发放）
              </Radio>
              <Radio id="2" value={2}>
                最高奖项（如无库存，降级发放）
              </Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
