/**
 * Author: z<PERSON>yue
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  buyTimes: number;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  exchangeImg?: string;
}

export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  shopNameColor: string;
  ruleBg: string;
  myPrizeBg: string;
  prizeBg: string;
  isAccordColor: string;
  orderLimitTextColor: string;
  thresholdColor: string;
  prizeItemBg: string;
  prizeItemTitleColor: string;
  getPrizeBtn: string;
  branZone: string;
  showSkuBg: string;
  priceColor: string;
  jumpUrl: string;
  isShowJump: boolean;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  canNotCloseJoinPopup: string;
  footerIsOpen: string;
  btnToTop: string;
  hotZoneList: any[];
}

export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  partake: number;
  partakeRangeData: [string, string] | null;
  partakeStartTime: string;
  partakeEndTime: string;
  crowdPackage: number;
  limitOrder: number;
  firstBuyType: number;
  orderRestrainRangeData: [Dayjs, Dayjs];
  orderRestrainStartTime: string;
  orderRestrainEndTime: string;
  orderRestrainStatus: number;
  orderStrokeCount: number;
  itemTotalOrAmount: number;
  awardDays: number;
  isDelayedDisttribution: number;
  orderStrokeStatus: number;
  itemTotal: number;
  days: number;
  orderRestrainAmount: string;
  orderSkuisExposure: number;
  orderSkuList: any[];
  crowdBag: any;
  totalProbability: number;
  ladderPrizeList: [];
  registrationPrizeList: []; // 根据实际情况，可能需要定义奖品的类型
  multiplePrizeList: [];
  receiveNum: number; // 最多能领几份奖品
  endActivity: number;
  isExposure: number;
  skuList: any[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  priceType: number;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/95036/20/52422/154844/66f51f35Fa384f1b0/ef326c466a3a70af.png',
  // 页面背景颜色
  actBgColor: '#143696',
  // 文字颜色
  shopNameColor: '',
  // 活动规则
  ruleBg: '//img10.360buyimg.com/imgzone/jfs/t1/196549/37/48402/5849/66f51f3cFf371fc6c/2339fed9176b4c96.png',
  // 我的奖品
  myPrizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/87898/2/46100/5925/66f51f35Ffbf1ce76/e22717ae394558d9.png',
  // 奖励区域背景图
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/100872/38/48422/295363/66f51f37F7eba6885/50f8acff1c1dfa93.png',
  // 是否符合条件提示语文字颜色
  isAccordColor: '#1274d4',
  // 订单限制提示字体颜色
  orderLimitTextColor: '#b1c8ed',
  // 门槛金额文字颜色
  thresholdColor: '#f0f8ff',
  // 权益任选单个礼品背景
  prizeItemBg: '//img10.360buyimg.com/imgzone/jfs/t1/102113/26/51768/12410/66f51f3cFf581dab0/3513ab7f2438e9b4.png',
  // 权益任选奖品名颜色
  prizeItemTitleColor: '#1f50a4',
  // 权益领取按钮
  getPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/248634/29/19462/6122/66f607afF2cad5be4/1ac4a3c61aec22f6.png',
  // 品牌旗舰专区
  branZone: '//img10.360buyimg.com/imgzone/jfs/t1/245076/7/19637/207527/66f51f3cFb4e75121/2385b9132d9f4711.png',
  // 参与活动商品背景
  showSkuBg: '//img10.360buyimg.com/imgzone/jfs/t1/231243/21/22432/1753660/67108901F80cc47f9/9a93ce4c7248382a.png',
  priceColor: '',
  cmdImg: '#fff',
  jumpUrl: '',
  isShowJump: true,
  h5Img: '',
  mpImg: '',
  canNotCloseJoinPopup: '',
  btnToTop: '',
  footerIsOpen: '2',
  hotZoneList: [],
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `复购有礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 参与时段限制（不提交）
    partake: 0,
    // 参与时段限制（不提交）
    partakeRangeData: null,
    // 参与开始时间
    partakeStartTime: '',
    // 参与结束时间
    partakeEndTime: '',
    // 参与者生成人群包
    crowdPackage: 0,
    // 是否延迟发奖
    isDelayedDisttribution: 0,
    // 延迟延迟发奖天数
    awardDays: 0,
    // 限制订单
    limitOrder: 1,
    // 复购类型  0:会员复购 1:店铺复购
    firstBuyType: 1,
    // 限制订单时间
    orderRestrainRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 限制订单开始时间
    orderRestrainStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 限制订单结束时间
    orderRestrainEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 限制订单状态
    orderRestrainStatus: 1,
    // 限制1：单笔 2：多笔
    orderStrokeCount: 1,
    // 订单笔数
    orderStrokeStatus: 1,
    // 笔数还是金额   1件数  2金额
    itemTotalOrAmount: 2,
    // 件数
    itemTotal: 1,
    // 近多少天
    days: 180,
    // 限制订单金额
    orderRestrainAmount: '0',
    // 订单商品全点商品或指定商品
    orderSkuisExposure: 0,
    // 限制订单商品列表
    orderSkuList: [],
    // 中奖总概率
    totalProbability: 0,
    // 阶梯列表
    ladderPrizeList: [],
    // 报名奖励列表
    registrationPrizeList: [],
    // 复购多选奖励列表
    multiplePrizeList: [],
    // 最多能领几份奖品
    receiveNum: 1,
    // 全部奖品发放完强制结束活动
    endActivity: 0,
    // 是否开启曝光
    isExposure: 1,
    // 商品列表
    skuList: [],
    // 是否开启分享
    shareStatus: 0,
    // 分享标题
    shareTitle: '会员复购有礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    priceType: 0,
  };
};

export const PRIZE_INFO: PrizeInfo = {
  buyTimes: 1,
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};
// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit' && getParams('status') === '2';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 订单时间超出活动时间范围内
const isOrderTimeInvalid = (formData: PageData) => {
  if (dayjs(formData.startTime).subtract(180, 'days').valueOf() > dayjs(formData.orderRestrainStartTime).valueOf()) {
    Message.error('订单开始时间不得超过活动开始时间前180天');
    return false;
  }
  if (formData.endTime < formData.orderRestrainEndTime) {
    Message.error('订单结束时间不得超过活动结束时间');
    return false;
  }
  return true;
};

// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  // 红包不校验时间
  if (type === 0 || type === 3 || type === 4 || type === 6) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const delayeTime = dayjs(formData.endTime).add(formData.awardDays, 'day');
    const isEnd: boolean = dayjs(delayeTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(
        `奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间${
          formData.awardDays ? `+延迟发奖天数` : ''
        }`,
      );
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (
  ladderPrizeList: PrizeInfo[],
  registrationPrizeList: PrizeInfo[],
  multiplePrizeList: PrizeInfo[],
  formData: PageData,
): boolean => {
  for (let i = 0; i < ladderPrizeList.length; i++) {
    if (!isPrizeValid(ladderPrizeList[i], formData)) {
      return false;
    }
  }
  for (let i = 0; i < registrationPrizeList.length; i++) {
    if (!isPrizeValid(registrationPrizeList[i], formData)) {
      return false;
    }
  }
  for (let i = 0; i < multiplePrizeList.length; i++) {
    if (!isPrizeValid(multiplePrizeList[i], formData)) {
      return false;
    }
    if (multiplePrizeList[i].prizeType === 7 && !multiplePrizeList[i].exchangeImg) {
      Message.error('请上传礼品卡兑换流程图');
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  // // 检查报名奖励列表
  // if (formData.registrationPrizeList.some((item: any) => item.prizeImg === '')) {
  //   Message.error('请完善报名奖励列表');
  //   return false;
  // }
  // 检查复购多选奖励列表
  if (formData.multiplePrizeList.some((item: any) => item.prizeImg === '')) {
    Message.error('请完善复购多选奖励列表');
    return false;
  }
  // // 检查阶梯奖品列表
  // if (formData.ladderPrizeList.some((item: any) => item.prizeImg === '' || !item.buyTimes)) {
  //   Message.error('请完善阶梯奖品列表');
  //   return false;
  // }
  // 如果所有检查都通过，则返回 true
  return true;
};

const checkLadderPrizeList = (ladderPrizeList: PrizeInfo[]) => {
  for (let i = 1; i < ladderPrizeList.length; i++) {
    if (ladderPrizeList.length > 1 && ladderPrizeList[i - 1]?.buyTimes >= ladderPrizeList[i]?.buyTimes) {
      Message.error('阶梯奖品列表购买次数应递增');
      return false;
    }
  }
  return true;
};

const checkSkuList = (isExposure, skuList) => {
  if (isExposure) {
    if (skuList.length === 0) {
      Message.error('请选择曝光商品');
      return false;
    }
  }
  return true;
};

export const checkActivityData = (
  ladderPrizeList: PrizeInfo[],
  registrationPrizeList: PrizeInfo[],
  multiplePrizeList: PrizeInfo[],
  formData: PageData,
): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择奖品
  if (!hasPrize(formData)) {
    return false;
  }
  if (!checkLadderPrizeList(ladderPrizeList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 最大领取份数校验
  if (formData.receiveNum > multiplePrizeList.length) {
    Message.error('复购多选奖励列表最大领取份数不能多于复购多选列表奖品数量');
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 订单时间超出活动时间范围内
  if (!isOrderTimeInvalid(formData)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(ladderPrizeList, registrationPrizeList, multiplePrizeList, formData)) {
    // return false;
    return true;
  }
  // 检测曝光商品
  if (!checkSkuList(formData.isExposure, formData.skuList)) {
    return false;
  }
  return true;
};
