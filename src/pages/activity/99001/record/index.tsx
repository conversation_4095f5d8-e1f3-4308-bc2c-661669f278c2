/**
 * 大转盘抽奖数据报表
 */
import React, { useEffect, useState } from 'react';
import { Button, Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import PromotionRecord from '@/pages/activity/99001/record/components/PromotionRecord';
// import OrderRecord from '@/pages/activity/99001/record/components/OrderRecord';
import WinRecord from '@/pages/activity/99001/record/components/WinRecord';
import UserRecord from '@/pages/activity/99001/record/components/UserRecord';
import { dataCheckLockPrizeIsSupport } from '@/api/v99001';
import { getParams } from '@/utils';

export default () => {
  const [isShow, setIsShow] = useState(false);

  const isShowTwoTab = async () => {
    try {
      const data = await dataCheckLockPrizeIsSupport({ activityId: getParams('id') });
      console.log('是否展示', data);
      setIsShow(data);
    } catch (err) {
      console.log(err);
    }
  };

  useEffect(() => {
    isShowTwoTab();
  }, []);

  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="N元锁权数据报表">
        {/* <Button */}
        {/*  onClick={() => { */}
        {/*    exportCombinedLogs([dataOrderRecordExport, dataTokenLogRecordExport], '下单阶梯有礼数据报表'); */}
        {/*  }} */}
        {/*  style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }} */}
        {/* > */}
        {/*  导出全部 */}
        {/* </Button> */}
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="领奖记录" key="1">
            <WinRecord />
          </Tab.Item>
          {isShow && (
            <Tab.Item title="令牌操作记录" key="2">
              <PromotionRecord />
            </Tab.Item>
          )}
          {isShow && (
            <Tab.Item title="用户明细查询" key="3">
              <UserRecord />
            </Tab.Item>
          )}
        </Tab>
      </LzPanel>
    </div>
  );
};
