import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动名称">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
          formData.endTime,
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="订单结算周期">{`${format.formatDateTimeDayjs(
          formData.orderStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderEndTime)}`}</FormItem>
        <FormItem label="订单价格结算方式">实付金额</FormItem>
        <FormItem label="订单金额计算方式">{formData.priceCalculateType === 1 ? '最高单笔金额' : '累计金额'}</FormItem>
        <FormItem label="奖品设置">
          <FormItem isPreview={false}>
            <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
              <Table.Column
                title="获奖消费金额(元)"
                cell={(_, index, row) => (
                  <div>{row.prizeName ? (row.orderPrice ? Number(row.orderPrice).toFixed(2) : 0) : ''}</div>
                )}
              />
              <Table.Column title="奖品名称" dataIndex="prizeName" />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => (
                  <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                    {PRIZE_TYPE[row.prizeType]}
                  </div>
                )}
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : '0'}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
            </Table>
          </FormItem>
        </FormItem>
        <FormItem label="领奖时间">{`${format.formatDateTimeDayjs(
          formData.awardStartTime,
        )}至${format.formatDateTimeDayjs(formData.awardEndTime)}`}</FormItem>
        {formData.awardType === 1 && <FormItem label="奖品领取范围">全部奖品</FormItem>}
        {formData.awardType === 2 && <FormItem label="奖品领取范围">选取单个奖品</FormItem>}

        <FormItem label="活动分享">{formData.shareStatus == 1 ? '开启' : '关闭'}</FormItem>
        {formData.shareStatus == 1 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}

        <FormItem label="规则内容">
          <div className="rule-word-break" style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
            {formData.rules}
          </div>
        </FormItem>
      </Form>
    </div>
  );
};
