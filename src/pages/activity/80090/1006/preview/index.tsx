/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
          formData.endTime,
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="活动生成人群包">{`${formData.crowdPackage === 1 ? '开启' : '关闭'}`}</FormItem>
        <FormItem label="复购类型">{formData.firstBuyType === 0 ? '会员复购' : '店铺复购'}</FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderRestrainStartTime,
        )}至${format.formatDateTimeDayjs(formData.orderRestrainEndTime)}`}</FormItem>
        <FormItem label="订单状态">{formData.orderRestrainStatus === 1 ? '已完成' : '已付款'}</FormItem>
        <FormItem label="订单笔数">
          {formData.orderStrokeCount === 1 ? '单笔' : `大于等于${formData.orderStrokeStatus}笔`}
        </FormItem>
        {/* <FormItem label="奖品延迟发放">{formData.awardDays > 0 ? `延迟发放${formData.awardDays}天` : '否'}</FormItem> */}
        <FormItem label="订单拆单后是否合并">{formData.split ? '是' : '否'}</FormItem>
        <FormItem label="价格类型">
          {formData.priceType === 0 ? '京东价' : '实付价 (POP店为实付价，自营店为京东价)'}
        </FormItem>
        <FormItem label="订单金额">大于等于{formData.orderRestrainAmount}元</FormItem>
        <FormItem label="复购规则">
          近{formData.days}天已购，且订单总金额大于等于{formData.beforeOrderAmount}元
        </FormItem>
        <FormItem label="订单商品">
          {formData.orderSkuisExposure === 0 && <div>全部商品</div>}
          {formData.orderSkuisExposure === 1 && (
            <div className={styles.container}>
              {formData.orderSkuList?.map((sku) => {
                return (
                  <div className={styles.skuContainer}>
                    <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                    <div>
                      <div className={styles.skuName}>{sku.skuName}</div>
                      <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                      <div className={styles.price}>¥ {sku.jdPrice}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </FormItem>
        <FormItem label="奖项设置" isPreview={false}>
          <Table
            dataSource={formData.prizeList.filter((e) => e.prizeName !== '谢谢参与')}
            style={{ marginTop: '15px' }}
          >
            <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
            <Table.Column title="奖项名称" dataIndex="prizeName" />
            <Table.Column
              title="奖项类型"
              cell={(_, index, row) => (
                <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                  {PRIZE_TYPE[row.prizeType]}
                </div>
              )}
            />
            <Table.Column
              title="单位数量"
              cell={(_, index, row) => {
                return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
              }}
            />
            <Table.Column
              title="发放份数"
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
            <Table.Column
              title="单份价值(元)"
              cell={(_, index, row) => <div>{row.unitPrice ?? Number(row.unitPrice).toFixed(2)}</div>}
            />
            {/* <Table.Column */}
            {/*   title="中奖概率(%)" */}
            {/*   cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>} */}
            {/* /> */}
            {/* <Table.Column */}
            {/*   title="每日发放限额" */}
            {/*   cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>} */}
            {/* /> */}
            <Table.Column
              title="奖品图"
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
          </Table>
        </FormItem>
        <FormItem label="活动监控">
          {formData.endActivity === 1 ? '全部奖品发完强制结束活动' : '全部奖品发完不强制结束活动'}
        </FormItem>
        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && (
            <div className={styles.container}>
              {formData.skuList?.map((sku, index) => {
                return (
                  <div className={styles.skuContainer}>
                    <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                    <div>
                      <div className={styles.skuName}>{sku.skuName}</div>
                      <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                      <div className={styles.price}>¥ {sku.jdPrice}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </FormItem>
        <FormItem label="分享标题">{formData.shareTitle}</FormItem>
        <FormItem label="图文分享图片">
          <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
        </FormItem>
        <FormItem label="京口令分享图片">
          <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
        </FormItem>
        <FormItem label="小程序分享图片">
          <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
        </FormItem>
        <FormItem label="规则内容">
          <Input.TextArea className={styles.wordBreak} value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
