import React, { useReducer, useEffect, useState } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Input, Tab } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
  handleLevelActiveKey: (levelActiveKey: string) => void;
}

export default ({ defaultValue, value, onChange, handleLevelActiveKey }: Props) => {
  const [activeKey, setActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setActiveKey(data);
    handleLevelActiveKey(data);
  };
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <div className={styles.base}>
        <div>
          <Tab activeKey={activeKey} defaultActiveKey="1" onChange={handleTabChange}>
            <Tab.Item title="粉卡" key="1" />
            <Tab.Item title="银卡" key="2" />
            <Tab.Item title="金卡" key="3" />
          </Tab>
        </div>
        {activeKey === '1' && (
          <LzPanel title="不满足页" subTitle="此页面展示不满足领取条件页面主图">
            <Form {...formLayout} className={styles.form}>
              <Form.Item label="页面主图">
                <Grid.Row>
                  <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                    <LzImageSelector
                      width={750}
                      value={formData.failedBgPink}
                      onChange={(failedBgPink) => {
                        setForm({ failedBgPink });
                      }}
                    />
                  </Form.Item>
                  <Form.Item style={{ marginBottom: 0 }}>
                    <div className={styles.tip}>
                      <p>图片尺寸：推荐宽度750px、高度1800px</p>
                      <p>图片大小：不超过1M</p>
                      <p>图片格式：JPG、JPEG、PNG、GIF</p>
                    </div>
                    <div>
                      <Button
                        type="primary"
                        text
                        onClick={() => {
                          setForm({ failedBgPink: defaultValue?.failedBgPink });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </Form.Item>
                </Grid.Row>
              </Form.Item>
              <Form.Item label="按钮跳转链接" required>
                <Input
                  value={formData.failedLinkPink}
                  onChange={(failedLinkPink) => setForm({ failedLinkPink })}
                  className={styles.popupLink}
                  placeholder="请输入跳转链接"
                />
                <div className={styles.tip}>此链接为点击【立即进店解锁更多惊喜】按钮所需跳转链接</div>
              </Form.Item>
            </Form>
          </LzPanel>
        )}
        {activeKey === '2' && (
          <LzPanel title="不满足页" subTitle="此页面展示不满足领取条件页面主图">
            <Form {...formLayout} className={styles.form}>
              <Form.Item label="页面主图">
                <Grid.Row>
                  <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                    <LzImageSelector
                      width={750}
                      value={formData.failedBgSilver}
                      onChange={(failedBgSilver) => {
                        setForm({ failedBgSilver });
                      }}
                    />
                  </Form.Item>
                  <Form.Item style={{ marginBottom: 0 }}>
                    <div className={styles.tip}>
                      <p>图片尺寸：推荐宽度750px、高度1800px</p>
                      <p>图片大小：不超过1M</p>
                      <p>图片格式：JPG、JPEG、PNG、GIF</p>
                    </div>
                    <div>
                      <Button
                        type="primary"
                        text
                        onClick={() => {
                          setForm({ failedBgSilver: defaultValue?.failedBgSilver });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </Form.Item>
                </Grid.Row>
              </Form.Item>
              <Form.Item label="按钮跳转链接" required>
                <Input
                  value={formData.failedLinkSilver}
                  onChange={(failedLinkSilver) => setForm({ failedLinkSilver })}
                  className={styles.popupLink}
                  placeholder="请输入跳转链接"
                />
                <div className={styles.tip}>此链接为点击【立即进店解锁更多惊喜】按钮所需跳转链接</div>
              </Form.Item>
            </Form>
          </LzPanel>
        )}
        {activeKey === '3' && (
          <LzPanel title="不满足页" subTitle="此页面展示不满足领取条件页面主图">
            <Form {...formLayout} className={styles.form}>
              <Form.Item label="页面主图">
                <Grid.Row>
                  <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                    <LzImageSelector
                      width={750}
                      value={formData.failedBgGold}
                      onChange={(failedBgGold) => {
                        setForm({ failedBgGold });
                      }}
                    />
                  </Form.Item>
                  <Form.Item style={{ marginBottom: 0 }}>
                    <div className={styles.tip}>
                      <p>图片尺寸：推荐宽度750px、高度1800px</p>
                      <p>图片大小：不超过1M</p>
                      <p>图片格式：JPG、JPEG、PNG、GIF</p>
                    </div>
                    <div>
                      <Button
                        type="primary"
                        text
                        onClick={() => {
                          setForm({ failedBgGold: defaultValue?.failedBgGold });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </Form.Item>
                </Grid.Row>
              </Form.Item>
              <Form.Item label="按钮跳转链接" required>
                <Input
                  value={formData.failedLinkGold}
                  onChange={(failedLinkGold) => setForm({ failedLinkGold })}
                  className={styles.popupLink}
                  placeholder="请输入跳转链接"
                />
                <div className={styles.tip}>此链接为点击【立即进店解锁更多惊喜】按钮所需跳转链接</div>
              </Form.Item>
            </Form>
          </LzPanel>
        )}
      </div>
    </div>
  );
};
