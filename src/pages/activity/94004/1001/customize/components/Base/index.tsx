import React, { useReducer, useEffect, useState } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Tab } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 7,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
  handleLevelActiveKey: (levelActiveKey: string) => void;
}

export default ({ defaultValue, value, onChange, handleLevelActiveKey }: Props) => {
  const [levelActiveKey, setLevelActiveKey] = useState('1');
  const handleTabChange = (data) => {
    setLevelActiveKey(data);
    handleLevelActiveKey(data);
  };
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <div>
        <Tab activeKey={levelActiveKey} defaultActiveKey="1" onChange={handleTabChange}>
          <Tab.Item title="粉卡" key="1" />
          <Tab.Item title="银卡" key="2" />
          <Tab.Item title="金卡" key="3" />
        </Tab>
      </div>
      {levelActiveKey === '1' && (
        <LzPanel title="活动主页" subTitle="此页面展示活动主页的标题、主图、赠品图、限量份数等">
          <Form {...formLayout} className={styles.form}>
            <Form.Item label="页面主图">
              <Grid.Row>
                <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                  <LzImageSelector
                    width={750}
                    value={formData.pageBgPink}
                    onChange={(pageBgPink) => {
                      setForm({ pageBgPink });
                    }}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }}>
                  <div className={styles.tip}>
                    <p>图片尺寸：推荐宽度750px、高度1800px</p>
                    <p>图片大小：不超过1M</p>
                    <p>图片格式：JPG、JPEG、PNG、GIF</p>
                  </div>
                  <div>
                    <Button
                      type="primary"
                      text
                      onClick={() => {
                        setForm({ pageBgPink: defaultValue?.pageBgPink });
                      }}
                    >
                      重置
                    </Button>
                  </div>
                </Form.Item>
              </Grid.Row>
            </Form.Item>
          </Form>
        </LzPanel>
      )}
      {levelActiveKey === '2' && (
        <LzPanel title="活动主页" subTitle="此页面展示活动主页的标题、主图、赠品图、限量份数等">
          <Form {...formLayout} className={styles.form}>
            <Form.Item label="页面主图">
              <Grid.Row>
                <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                  <LzImageSelector
                    width={750}
                    value={formData.pageBgSilver}
                    onChange={(pageBgSilver) => {
                      setForm({ pageBgSilver });
                    }}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }}>
                  <div className={styles.tip}>
                    <p>图片尺寸：推荐宽度750px、高度1800px</p>
                    <p>图片大小：不超过1M</p>
                    <p>图片格式：JPG、JPEG、PNG、GIF</p>
                  </div>
                  <div>
                    <Button
                      type="primary"
                      text
                      onClick={() => {
                        setForm({ pageBgSilver: defaultValue?.pageBgSilver });
                      }}
                    >
                      重置
                    </Button>
                  </div>
                </Form.Item>
              </Grid.Row>
            </Form.Item>
          </Form>
        </LzPanel>
      )}
      {levelActiveKey === '3' && (
        <LzPanel title="活动主页" subTitle="此页面展示活动主页的标题、主图、赠品图、限量份数等">
          <Form {...formLayout} className={styles.form}>
            <Form.Item label="页面主图">
              <Grid.Row>
                <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                  <LzImageSelector
                    width={750}
                    value={formData.pageBgGold}
                    onChange={(pageBgGold) => {
                      setForm({ pageBgGold });
                    }}
                  />
                </Form.Item>
                <Form.Item style={{ marginBottom: 0 }}>
                  <div className={styles.tip}>
                    <p>图片尺寸：推荐宽度750px、高度1800px</p>
                    <p>图片大小：不超过1M</p>
                    <p>图片格式：JPG、JPEG、PNG、GIF</p>
                  </div>
                  <div>
                    <Button
                      type="primary"
                      text
                      onClick={() => {
                        setForm({ pageBgGold: defaultValue?.pageBgGold });
                      }}
                    >
                      重置
                    </Button>
                  </div>
                </Form.Item>
              </Grid.Row>
            </Form.Item>
          </Form>
        </LzPanel>
      )}
    </div>
  );
};
