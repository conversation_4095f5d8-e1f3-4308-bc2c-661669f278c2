import React, { useEffect, useReducer, useState } from 'react';
import { CHILD_INFO, ChildInfo } from '../../../util';
import { Balloon, Button, DatePicker2, Form, Grid, Input, Message } from '@alifd/next';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import { activityEditDisabled, calcDateDiff } from '@/utils';
import LzImageSelector from '@/components/LzImageSelector';
import styles from '../../style.module.scss';

interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
  constructor(skuId: number) {
    this.jdPrice = '0';
    this.seq = 0;
    this.skuId = skuId;
    this.skuMainPicture =
      '//img10.360buyimg.com/imgzone/jfs/t1/44645/27/24207/24564/659d00d9F0467f8c5/ef658035745cc059.png';
    this.skuName = '钻光夜乳12ml';
  }
}

interface Props {
  editValue: ChildInfo | null;
  onChange: (data: ChildInfo) => void;
  onCancel: () => void;
}

const FormItem = Form.Item;
export const prizeFormLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 7,
  },
  colon: true,
};
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;

export default ({ editValue, onChange, onCancel }: Props) => {
  const [edit, setEdit] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || CHILD_INFO);

  // 编辑时，当前促销活动状态，0: 未开始，1: 进行中，2: 已结束
  const [actStatus, setActStatus] = useState(0);
  const [inputSkuList, setInputSkuList] = useState('');

  const onDataRangeChange = (rangeDate): void => {
    setEdit({
      rangeDate,
      promotionStartTime: format.formatDateTimeDayjs(rangeDate[0]),
      promotionEndTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };

  const submit = (values: any, errors: any) => {
    if (!edit.giftPoster) {
      Message.error('请上传赠品海报');
      return;
    }
    if (!edit.giftSkuList.length) {
      Message.error('请选择赠品SKU');
      return;
    }
    !errors && onChange(edit);
  };
  useEffect(() => {
    if (activityEditDisabled()) {
      if (editValue?.promotionEndTime && dayjs().isAfter(editValue.promotionEndTime)) {
        setActStatus(2);
        console.log('已结束');
      } else if (editValue?.promotionStartTime && dayjs().isAfter(editValue.promotionStartTime)) {
        setActStatus(1);
        console.log('进行中');
      } else {
        setActStatus(0);
        console.log('未开始');
      }
    }
  }, [editValue]);

  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            此促销活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };

  // 处理用户输入SKU
  const checkInputSku = (val) => {
    const regex = /^[0-9,]*$/;
    // 使用正则表达式进行验证
    if (!regex.test(val)) {
      Message.error('只允许输入数字及英文逗号');
      return;
    }
    if (val.match(/,,/)) {
      Message.error('禁止连续输入两个及以上的逗号');
      return;
    }
    const skuArr = val.split(',');

    // 检查是否有超过 20 位数字的 SKU
    const isSkuTooLong = skuArr.some((skuId) => skuId.length > 15);
    if (isSkuTooLong) {
      Message.error('SKU 限制为 15 位');
      return;
    }
    if (skuArr.length > 10) {
      Message.error('最多添加10个SKU');
      return;
    }
    const giftSkuList: Sku[] = [];
    skuArr.forEach((skuId) => {
      if (skuId) {
        giftSkuList.push(new Sku(parseInt(skuId, 10)));
      }
    });
    setInputSkuList(val);
    setEdit({ giftSkuList });
  };

  useEffect(() => {
    if (editValue) {
      setEdit(editValue);
      setInputSkuList(editValue.giftSkuList.map((sku) => sku.skuId).join(','));
    }
  }, []);

  return (
    <div>
      <Form {...prizeFormLayout}>
        <FormItem label="促销活动时间" required requiredMessage="请选择促销活动时间">
          <RangePicker
            disabled={activityEditDisabled() && actStatus !== 0}
            className="w-300"
            name="rangeDate"
            inputReadOnly
            format={dateFormat}
            hasClear={false}
            showTime
            value={edit.rangeDate}
            onChange={onDataRangeChange}
            disabledDate={(date) => {
              return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
            }}
          />
          <DateRangeDuration rangeDate={edit.rangeDate || [new Date(edit.startTime), new Date(edit.endTime)]} />
          <div className={styles.tip}>
            请填写令牌所绑定的总价促销活动的时间，务必与官方后台设置的时间保持一致（若配置多期，那么第一期的开始时间和最后一期的结束时间务必在官方后台设置的时间范围内，否则将会有客诉风险）
            <Balloon
              v2
              trigger={
                <Button text type={'primary'}>
                  查看官方设置示例
                </Button>
              }
              triggerType="hover"
              closable={false}
              popupClassName={styles.balloon}
            >
              <div>
                <img
                  style={{ width: '550px' }}
                  src="//img10.360buyimg.com/imgzone/jfs/t1/236908/9/3618/20376/655ac236F33b191b9/2c6436c9c2493e17.png"
                  alt=""
                />
              </div>
            </Balloon>
          </div>
        </FormItem>
        <FormItem label="赠品海报" required>
          <Grid.Row>
            <FormItem style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={750}
                // height={1037}
                value={edit.giftPoster}
                onChange={(giftPoster) => {
                  setEdit({ giftPoster });
                }}
              />
            </FormItem>
            <FormItem style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>图片尺寸：宽度750px，推荐高度1037px</p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG、GIF</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setEdit({ giftPoster: CHILD_INFO?.giftPoster });
                  }}
                >
                  重置
                </Button>
              </div>
            </FormItem>
          </Grid.Row>
        </FormItem>
        <Form.Item label="赠品SKU" required requiredMessage="请输入赠品SKU">
          <Input.TextArea
            name="rules"
            value={inputSkuList}
            placeholder="请输入令牌所绑定的总价促销活动的赠品SKU，多个以英文逗号分隔"
            maxLength={170}
            style={{ width: '300px' }}
            showLimitHint
            hasBorder
            rows={8}
            className="form-input-ctrl"
            onChange={checkInputSku}
            disabled={activityEditDisabled() && actStatus !== 0}
          />
          <div className={styles.tip}>
            1. 添加令牌所绑定的总价促销活动的赠品SKU，最多添加10个；
            <div>
              <span style={{ color: 'red' }}>2. 不要填写影子分身SKU，否则会造成用户刷单。</span>
            </div>
          </div>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
    </div>
  );
};
