import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface ChildInfo {
  // 期数
  num: number;
  // 促销活动时间
  rangeDate: [Dayjs, Dayjs] | null;
  promotionStartTime: string;
  promotionEndTime: string;
  // 赠品海报
  giftPoster: string;
  // 赠品SKU
  giftSkuList: any[];
  // 是否曝光商品 0否 1是
  exposeProducts: number;
  // 曝光商品
  skuList: any[];
}

export interface CustomValue {
  pageBg: string;
  // 活动主图
  actBg: string;
  // 第二阶段活动KV图
  actBg2: string;
  // 页面背景颜色
  actBgColor: string;
  // 按钮字体颜色
  btnColor: string;
  // 按钮背景图
  btnImg: string;
  giftImg: string;
  step: string;
  popupLink: string;
  linkUrl: string;
  // 攻略图
  popupImg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs] | [string, string];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  reservationAmountShow: number;
  crowdBag: any;
  totalProbability: number;
  prizeList: any[];
  childList: ChildInfo[]; // 根据实际情况，可能需要定义奖品的类型
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  isExposure: number;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  skuList: any[];
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '',
  // 活动主图
  actBg: '',
  // 第二阶段活动KV图
  actBg2: '',
  // 页面背景颜色
  actBgColor: '',
  // 按钮字体颜色
  btnColor: '',
  // 按钮背景图
  btnImg: '',
  giftImg: '',
  step: '',
  popupLink: '',
  // 已锁权你去下单的跳转链接
  linkUrl: '',
  // 攻略图
  popupImg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `0.01元锁权-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: ['', ''],
    // 活动开始时间
    startTime: '',
    // 活动结束时间
    endTime: '',
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 预定所需金额
    reservationAmountShow: 0.01,
    // 中奖总概率
    totalProbability: 0,
    // 奖品列表
    prizeList: [],
    // 促销活动列表
    childList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '超级优惠，等你来袭！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    isExposure: 1,
    gradeLabel: [],
    crowdBag: null,
    // 商品列表
    skuList: [],
  };
};

export const CHILD_INFO: ChildInfo = {
  // 期数
  num: 0,
  // 促销活动时间
  rangeDate: null,
  promotionStartTime: '',
  promotionEndTime: '',
  // 赠品海报
  giftPoster: '',
  // 赠品SKU
  giftSkuList: [],
  // 是否曝光商品 0否 1是
  exposeProducts: 1,
  // 曝光商品
  skuList: [],
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
const hasPrize = (childList: ChildInfo[]): boolean => {
  if (!childList.length) {
    Message.error('请设置促销活动');
    return false;
  }
  return true;
};

export const checkActivityData = (childList: ChildInfo[], formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择令牌
  if (!formData.prizeList.length) {
    Message.error('请选择令牌');
    return false;
  }
  // 没有选择促销活动
  if (!hasPrize(childList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 活动时间在令牌有效期内
  if (
    formData.prizeList.length &&
    (dayjs(formData.startTime).isBefore(dayjs(formData.prizeList[0].beginTime)) ||
      dayjs(formData.endTime).isAfter(dayjs(formData.prizeList[0].endTime)))
  ) {
    Message.error('促销活动时间应在令牌有效期内');
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  return true;
};
