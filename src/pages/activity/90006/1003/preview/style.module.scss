.preview {
  .value {
    display: flex;
    align-items: center;
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;

  .skuContainer {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;

    .skuImg {
      width: 80px;
      height: 80px;
      margin-right: 8px;
    }

    .skuName {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}

