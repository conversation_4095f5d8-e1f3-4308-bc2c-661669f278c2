/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData } from '@/pages/activity/21003/2001/util';
// import format from '@/utils/format';
// import { checkActivityData } from '../../../util';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  /**
   * 自动生成规则说明
   */
  //   const autoCreateRuleDesc = async (): Promise<void> => {
  //     const isValidateData: boolean = checkActivityData(formData);
  //     if (!isValidateData) {
  //       return;
  //     }
  //     const rules = `1.参与人群
  // 首次加入CPB肌肤之钥京东自营官方旗舰店会员且该会员历史上未成功购买过正装商品，并在活动规定期间购买任意正装商品且单笔订单实付满1000元，可获得首购礼包一份（尝鲜盒/入会礼等试用装不计入在正装产品内，下单后退货用户不可参与）；
  // 2. 活动时间
  // 领取时间：${format.formatDateTimeDayjs(formData.startTime)}~${format.formatDateTimeDayjs(formData.endTime)}
  // 使用时间：${format.formatDateTimeDayjs(formData.startTime)}~${format.formatDateTimeDayjs(formData.endTime)}
  // 每个ID限领取【1】份，一共2500份，先到先得，送完即止；
  // 3.礼赠内容
  // CPB肌肤之钥-钻光夜乳12mL（本赠品为中小样，无销售价格）；
  // 4.领取方式
  // 第一步：在活动页面成功申领礼品后（以申领页面显示结果为准），到购物车结算时展示；
  // 第二步：勾选待购买的正装产品并点击“换促销”和“重新领取”赠品，预售商品需要在付尾款时前往购物车勾选该礼赠；
  // 第三步：成功付款正装产品后，礼品将随正装订单发出。`;
  //     setData({ rules });
  //     field.setErrors({ rules: '' });
  //   };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            {/* <Button */}
            {/*  type="primary" */}
            {/*  className="table-cell-btn" */}
            {/*  onClick={autoCreateRuleDesc} */}
            {/*  style={{ marginRight: '15px' }} */}
            {/*  text */}
            {/* > */}
            {/*  自动生成规则说明 */}
            {/* </Button> */}
            <div style={{ color: 'red', fontSize: '12px', display: 'flex' }}>
              <div>提示：</div>
              <div>
                <div>
                  （1）请商家务必输入活动规则，并检查活动规则信息内容与活动实际情况是否相符，若存在不符情况请修改后再发布活动；
                </div>
                <div>
                  （2）建议规则内显示奖品名称、奖品发放总量及奖品金额/价值等奖品相关信息的明确性，以免引起客诉；
                </div>
                <div>（3）若活动进行中修改了活动参数，请同步修改活动规则，以免引起客诉。</div>
              </div>
            </div>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
