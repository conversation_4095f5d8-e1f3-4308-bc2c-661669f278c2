/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Button, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {
  checkActivityData,
  FormLayout,
  generateMembershipString,
  PageData,
  PrizeInfo,
} from '@/pages/activity/90009/1001/util';
import format from '@/utils/format';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  checkForm: () => boolean;
}

export default ({ onChange, defaultValue, value, sRef, checkForm }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const generatePrizeString = () => {
    const prizeList = formData.prizeList.filter(
      (e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与',
    );
    const strList = prizeList.map((item, index): string => {
      return `（${index + 1}）${item.prizeName}: 共${item.sendTotalCount}份，单份奖品价值${Number(
        item.unitPrice,
      ).toFixed(2)}元`;
    });
    return strList.join('\n');
  };
  const generatePartake = () => {
    if (formData.partake) {
      return `每日${formData.partakeStartTime}~${formData.partakeEndTime}`;
    }
    return '';
  };

  /**
   * 获取前置订单开始时间
   */
  const getfirstOrderStartTime = () => {
    const day = formData.days;
    // 创建原始时间的副本
    const originalStartTime = new Date(formData.orderRestrainStartTime);
    // 创建一个新的日期对象，以避免修 改原始时间
    const firstOrderStartTime = new Date(originalStartTime);
    // 执行日期操作，减去天数
    firstOrderStartTime.setDate(originalStartTime.getDate() - day);
    return format.formatDateTimeDayjs(firstOrderStartTime);
  };

  //   /**
  //    * 自动生成规则说明
  //    */
  //   const autoCreateRuleDesc = async (): Promise<void> => {
  //     if (!checkForm()) {
  //       return;
  //     }
  //     // // 判断奖品列表是否存在有效奖品
  //     const prizeList = formData.prizeList.filter(
  //       (e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与',
  //     );
  //     const isValidateData: boolean = checkActivityData(prizeList, formData);
  //     if (!isValidateData) {
  //       return;
  //     }
  //     const rules = `1. 活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
  //       formData.endTime,
  //     )}；${generatePartake()}
  // 2. 参与规则：
  // （1）${getfirstOrderStartTime()}至${format.formatDateTimeDayjs(
  //       formData.orderRestrainStartTime,
  //     )}时间段内有订单，且订单总金额满足${formData.beforeOrderAmount}元；
  // （2）在${format.formatDateTimeDayjs(formData.orderRestrainStartTime)}至${format.formatDateTimeDayjs(
  //       formData.orderRestrainEndTime,
  //     )}时间段内下单（订单为“【${formData.orderRestrainStatus === 1 ? '已完成' : '已付款'}】”状态）且${
  //       formData.orderStrokeCount === 1 ? '单' : `大于等于${formData.orderStrokeStatus}`
  //     }笔订单商品金额满${formData.orderRestrainAmount}元即可参与；
  // 3.参与活动商品：${
  //       formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2 ? '本店指定商品' : '本店全部商品'
  //     }；每人活动内可领取1次；奖品数量有限，先到先得；
  // 4.满足条件参与领取后，待订单完成后，系统自动发放奖励；
  // （注意：如果参与活动商品是预售商品，需要订单支付尾款且满足活动设置规则才可以参与活动。）
  // 5.活动时间内未能完成的订单将视为放弃，不予发放奖励；
  // 6.符合条件的订单领取奖励成功后，不会被计算到下次领奖条件中；
  // 7.领取后不代表获得奖品，奖品以实际发放情况为主；
  // 8.预售订单需支付尾款后才能参与此活动；
  // 9.若用户存在刷奖等恶意行为，一经发现将取消抽奖资格（如奖品已经发放，有权追回奖品）；
  // 10.活动对象：无门槛；
  // 11. 奖品说明：
  // ${generatePrizeString()}
  // 注：奖品发放形式：
  // 实物奖：
  // （1）中奖客户信息收集：页面弹屏提示中奖客户提供收货地址、号码和奖项；（超过1小时未填写对应收货信息，视为放弃）；
  // （2）实物奖采取寄送方式发放，获奖用户需在开奖后埴写姓名、联系电话、详细地址信息。如因用户原因无法联系上，即奖品作废不再补发，或如用户未填写真实有效的信息或填写收货信息不详，均视为放弃奖品；
  // 虚拟产品奖：
  // （1）红包、优惠券、京豆、积分、E卡，预计会在24小时内发放到京东账户中，使用期限、商品范围、使用规则等以官方券面展示信息为主准，您可以在“京东APP-我的”中查看；
  // （2）PLUS会员、爱奇艺会员卡、网易云音乐、优酷视频、喜马拉雅、樊登读书、酷狗音乐等月卡，年卡中奖后需手动兑换；
  // 【活动参与主体资格】
  // （1）每位自然人用户仅能使用一个京东账号参与活动，WX号、QQ、京东账号、手机号码、收货地址等任一信息一致或指向同一用户的，视为同一个用户，则第一个参与本活动的账号参与结果有效，其余账号参与结果均视为无效。
  // （2） 若发现同一位用户使用不同账号重复参与活动，承办方有权取消其参与资格。
  // 【注意事项】
  // （1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利。
  // （2）如遇不可抗力（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的），商家有权取消、修改或暂停本活动。
  // （3）是否获得优惠以活动发布者后台统计结果为准。
  // （4）因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新。
  // `;
  //     setData({ rules });
  //     field.setErrors({ rules: '' });
  //   };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <div style={{ color: 'red', fontSize: '12px', display: 'flex' }}>
              <div>提示：</div>
              <div>
                <div>
                  （1）请商家务必输入活动规则，并检查活动规则信息内容与活动实际情况是否相符，若存在不符情况请修改后再发布活动；
                </div>
                <div>
                  （2）建议规则内显示奖品名称、奖品发放总量及奖品金额/价值等奖品相关信息的明确性，以免引起客诉；
                </div>
                <div>（3）若活动进行中修改了活动参数，请同步修改活动规则，以免引起客诉。</div>
              </div>
            </div>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
