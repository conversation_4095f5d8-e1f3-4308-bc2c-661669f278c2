import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, NumberPicker, Radio } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderRestrainRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };

  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    // const { orderStartTime, orderEndTime } = formData;
    const orderStartTime = val[0];
    const orderEndTime = val[1];
    if (orderStartTime && orderEndTime) {
      const diff = dayjs(orderEndTime).diff(dayjs(orderStartTime), 'day');
      if (diff > 60) {
        callback('订单结算时间间隔不大于60天');
      } else {
        callback();
      }
    } else {
      callback('请选择订单结算周期');
    }
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  return (
    <div>
      <LzPanel title="消费订单设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <>
            <FormItem label=" " colon={false}>
              <div className={styles.tip}>
                注：客户可在领奖时间段内领取奖品，超过领奖截止时间不可领奖
                <br />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;领奖开始时间不得早于订单结算截止时间7天后，领奖截止时间需在领奖开始时间1日后
                <br />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;由于京东接口原因，订单获取时间可能产生延迟
              </div>
            </FormItem>
            <FormItem
              label="订单结算周期"
              required
              requiredMessage="请选择订单结算周期时间"
              validator={validateOrderTime}
            >
              <RangePicker
                className="w-300"
                name="orderRestrainRangeData"
                inputReadOnly
                format={dateFormat}
                hasClear={false}
                showTime
                value={
                  formData.orderRestrainRangeData || [
                    new Date(formData.orderStartTime),
                    new Date(formData.orderEndTime),
                  ]
                }
                onChange={onDataRangeChange}
              />
            </FormItem>
            <FormItem label="订单价格结算方式" required>
              <RadioGroup value={formData.orderPriceType} onChange={(orderPriceType) => setData({ orderPriceType })}>
                <Radio value={1}>实付金额</Radio>
                {/* <Radio value={2}>累计金额</Radio> */}
              </RadioGroup>
              <div className={styles.tip}>注：“实付金额”即按订单的实际付款金额作为订单金额计算标准</div>
            </FormItem>
            <FormItem label="订单结算状态" required>
              <RadioGroup value={formData.orderStatus} onChange={(orderStatus) => setData({ orderStatus })}>
                <Radio value={1}>订单完成</Radio>
              </RadioGroup>
              <div className={styles.tip}>注：收货完成的订单可计入计算</div>
            </FormItem>
            <FormItem label="订单金额计算方式" required requiredMessage="请选择订单金额计算方式">
              <RadioGroup
                value={formData.priceCalculateType}
                onChange={(priceCalculateType) => setData({ priceCalculateType })}
              >
                <Radio value={1}>最高单笔金额</Radio>
                <Radio value={2}>累计金额</Radio>
              </RadioGroup>
              <div className={styles.tip}>
                注：“累计金额”，将结算周期内，全部订单的累计金额是否超过评奖基准额度作为评判标准
                <br />
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;“最高单笔金额”，将结算周期内，单笔最高金额是否超过评奖基准额度作为评判标准
              </div>
            </FormItem>
            <FormItem
              label="获奖最低额度限制"
              required
              requiredMessage="请选择获奖最低额度限制"
              extra={<div className="next-form-item-help">注：按所选订单结算方式，可领取奖品的最低金额</div>}
            >
              <RadioGroup
                value={formData.minOrderPriceType}
                onChange={(minOrderPriceType) => setData({ minOrderPriceType })}
              >
                <Radio value={1}>不限制</Radio>
                <Radio value={2}>限制</Radio>
              </RadioGroup>
              {formData.minOrderPriceType === 2 && (
                <div className={styles.panel}>
                  <Form.Item
                    name="minOrderPrice"
                    required
                    requiredTrigger="onBlur"
                    requiredMessage="请输入获奖最低额度限制"
                    style={{ margin: 0 }}
                  >
                    <NumberPicker
                      value={formData.minOrderPrice}
                      onChange={(minOrderPrice: number) => setData({ minOrderPrice })}
                      type="inline"
                      min={1}
                      precision={2}
                      max={9999999}
                      className={styles.number}
                    />
                    元
                  </Form.Item>
                </div>
              )}
            </FormItem>

            {/* <Form.Item */}
            {/*  label="最大获奖次数" */}
            {/*  required */}
            {/*  extra={<div className="next-form-item-help">注：限制活动周期内用户最大中奖次数</div>} */}
            {/* > */}
            {/*  <RadioGroup */}
            {/*    value={formData.winLotteryLimit} */}
            {/*    onChange={(winLotteryLimit: number) => setData({ winLotteryLimit })} */}
            {/*  > */}
            {/*    <Radio id="1" value={1}> */}
            {/*      不限制 */}
            {/*    </Radio> */}
            {/*    <Radio id="2" value={2}> */}
            {/*      限制 */}
            {/*    </Radio> */}
            {/*  </RadioGroup> */}
            {/*  {formData.winLotteryLimit === 2 && ( */}
            {/*    <div className={styles.panel}> */}
            {/*      <Form.Item */}
            {/*        name="winLotteryNum" */}
            {/*        required */}
            {/*        requiredTrigger="onBlur" */}
            {/*        requiredMessage="请输入最大获奖次数" */}
            {/*        style={{ margin: 0 }} */}
            {/*      > */}
            {/*        <NumberPicker */}
            {/*          value={formData.winLotteryNum} */}
            {/*          onChange={(winLotteryNum: number) => setData({ winLotteryNum })} */}
            {/*          type="inline" */}
            {/*          min={1} */}
            {/*          max={9999999} */}
            {/*          className={styles.number} */}
            {/*        /> */}
            {/*        次 */}
            {/*      </Form.Item> */}
            {/*    </div> */}
            {/*  )} */}
            {/* </Form.Item> */}
          </>
        </Form>
      </LzPanel>
    </div>
  );
};
