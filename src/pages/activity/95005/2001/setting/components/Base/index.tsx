import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled, calcDateDiff, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import LzThreshold from '@/components/LzThreshold';
import { Form, Input, DatePicker2, Field } from '@alifd/next';
import dayjs from 'dayjs';
import format from '@/utils/format';
import constant from '@/utils/constant';

const FormItem = Form.Item;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      rangeDate,
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  // 获取持续时间
  const DateRangeDuration = ({ rangeDate }) => {
    const duration: string = calcDateDiff(rangeDate);
    return (
      <span style={{ fontSize: '12px', marginLeft: '5px' }}>
        {
          <span>
            活动持续<span style={{ color: 'red' }}>{duration || '不足1小时'}</span>
          </span>
        }
      </span>
    );
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  useEffect(() => {
    if (formData.endTime < formData.receiveStartTime) {
      setData({ receiveStartTime: '' });
    }
    if (formData.endTime < formData.signEndTime) {
      setData({ signEndTime: '' });
    }
  }, [formData.endTime]);

  return (
    <div>
      <LzPanel title="活动基本信息">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="活动名称" required requiredMessage="请输入活动名称" disabled={false}>
            <Input
              value={formData.activityName}
              placeholder="请输入活动名称"
              name="activityName"
              maxLength={20}
              showLimitHint
              className="w-300"
              onChange={(activityName) => setData({ activityName })}
            />
          </FormItem>
          <FormItem
            label="活动时间"
            required
            requiredMessage="请选择活动时间"
            extra={<div className="next-form-item-help">注：活动时间需设置在软件的订购有效期内</div>}
          >
            <RangePicker
              className="w-300"
              name="rangeDate"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
              }}
            />
            <DateRangeDuration
              rangeDate={formData.rangeDate || [new Date(formData.startTime), new Date(formData.endTime)]}
            />
          </FormItem>
          <FormItem label="活动门槛" required>
            <LzThreshold formData={formData} field={field} setData={setData} apply={[1]} applyGrade={[]} />
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
