/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 设置模板
 */
import React from 'react';
import { CustomValue } from '../util';
import Unrecognized from './components/Unrecognized';
import VerifiedOff from './components/VerifiedOff';
import { Tab } from '@alifd/next';
import Popup from './components/Popup';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  showPrizePopup: (key: string) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { target, defaultValue, value, handleChange, showPrizePopup } = props;
  // 选中的tab
  const [drawerTabIndex, setDrawerTabIndex] = React.useState('1');

  console.log('target', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab
        onChange={(key): void => {
          setDrawerTabIndex(key);
          showPrizePopup(key);
        }}
        activeKey={drawerTabIndex}
      >
        <Tab.Item title="未核销页" key="1">
          <Unrecognized {...eventProps} />
        </Tab.Item>
        <Tab.Item title="已核销页" key="2">
          <VerifiedOff {...eventProps} />
        </Tab.Item>
        <Tab.Item title="中奖弹窗" key="3">
          <Popup {...eventProps} />
        </Tab.Item>
      </Tab>
    </div>
  );
};
