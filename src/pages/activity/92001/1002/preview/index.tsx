/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useEffect, useReducer, useState } from 'react';
import { Form, Table, Input, Button, Tab } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}
const stageMapList = {
  F0: '无日期备孕期至宝宝出生后24个月内',
  'F2-N': '6-12个月',
  F3: '10-36个月',
};

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [skuVisible, setSkuVisible] = useState(false);
  formItemLayout.labelAlign = labelAlign;

  useEffect(() => {
    formData.seriesList.forEach((item) => {
      item.seriesSkuList.forEach((sku) => {
        sku.seriesName = item.seriesName;
      });
    });
  }, []);
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{formData.threshold === 0 ? '无门槛' : '店铺会员'}</FormItem>
        <FormItem label="SKU列表">
          <Button
            onClick={() => {
              setSkuVisible(true);
            }}
          >
            查看SKU
          </Button>
        </FormItem>
        <FormItem label="系列源列表">
          <Table.StickyLock dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
            <Table.Column title="系列名" lock={'left'} dataIndex="seriesName" width={120} />
            <Table.Column
              lock={'left'}
              title="阶段"
              dataIndex="stageId"
              width={120}
              cell={(_, index, row) => <span>{stageMapList[row.stageId]}</span>}
            />
            <Table.Column
              title="系列图"
              width={120}
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.seriesImg} alt="" />}
            />
            <Table.Column title="奖品名称" width={120} dataIndex="prizeName" />
            <Table.Column title="商品Code" width={120} dataIndex="productCode" />
            <Table.Column title="机制ID" width={120} dataIndex="nuTagId" />
            <Table.Column title="SKU ID" width={120} dataIndex="skuId" />

            <Table.Column
              title="奖品图"
              width={220}
              cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
            />
            <Table.Column
              title="发放份数"
              width={120}
              cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
            />
          </Table.StickyLock>
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        <Tab>
          {formData.seriesList.map((item, index) => {
            return (
              <Tab.Item title={item.seriesName}>
                <Table dataSource={formData.seriesList[index].seriesSkuList} fixedHeader>
                  <Table.Column title="系列名" dataIndex="seriesName" />
                  <Table.Column title="SKUID" dataIndex="skuId" />
                </Table>
              </Tab.Item>
            );
          })}
        </Tab>
      </LzDialog>
    </div>
  );
};
