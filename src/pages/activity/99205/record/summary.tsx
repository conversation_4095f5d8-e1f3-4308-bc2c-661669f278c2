import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import RecordSummary from './components/RecordSummary';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="babyCare升级礼活动领取记录汇总">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="领取记录" key="1">
            <RecordSummary />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
