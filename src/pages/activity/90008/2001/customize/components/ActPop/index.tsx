import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Input } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 8,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel
        title="活动弹窗"
        subTitle="注意：链接须包含 .isvjcloud.com、.isvjd.com或.jd.com；只能以http://,https://开头"
      >
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="领取成功-弹窗">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={516}
                  height={572}
                  value={formData.receivedSussessPopup}
                  onChange={(receivedSussessPopup) => {
                    setForm({ receivedSussessPopup });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：516px*572px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ receivedSussessPopup: defaultValue?.receivedSussessPopup });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="领取成功弹窗链接" required requiredMessage="请输入领取成功弹窗链接">
            <Input
              style={{ width: '300px' }}
              value={formData.receivedSussessPopupLink}
              name="receivedSussessPopupLink"
              placeholder="请输入领取成功弹窗链接"
              onChange={(receivedSussessPopupLink) => {
                setForm({ receivedSussessPopupLink });
              }}
            />
          </Form.Item>
          <Form.Item label="您已领取过-弹窗">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={516}
                  height={572}
                  value={formData.receivedPopup}
                  onChange={(receivedPopup) => {
                    setForm({ receivedPopup });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：516px*572px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ receivedPopup: defaultValue?.receivedPopup });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="您已经领取过弹窗链接" required requiredMessage="请输入您已经领取过弹窗链接">
            <Input
              style={{ width: '300px' }}
              value={formData.receivedPopupLink}
              name="receivedPopupLink"
              placeholder="请输入您已经领取过弹窗链接"
              onChange={(receivedPopupLink) => {
                setForm({ receivedPopupLink });
              }}
            />
          </Form.Item>
          <Form.Item label="您在其他渠道领取过-弹窗">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={516}
                  height={572}
                  value={formData.receivedOtherPopup}
                  onChange={(receivedOtherPopup) => {
                    setForm({ receivedOtherPopup });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：516px*572px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ receivedOtherPopup: defaultValue?.receivedOtherPopup });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="其他渠道已领取弹窗链接" required requiredMessage="请输入其他渠道已领取弹窗链接">
            <Input
              style={{ width: '300px' }}
              value={formData.receivedOtherPopupLink}
              name="receivedOtherPopupLink"
              placeholder="请输入其他渠道已领取弹窗链接"
              onChange={(receivedOtherPopupLink) => {
                setForm({ receivedOtherPopupLink });
              }}
            />
          </Form.Item>
          <Form.Item label="等级不足-弹窗">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={516}
                  height={572}
                  value={formData.gradePopup}
                  onChange={(gradePopup) => {
                    setForm({ gradePopup });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：516px*572px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ gradePopup: defaultValue?.gradePopup });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="等级不足弹窗链接" required requiredMessage="请输入等级不足弹窗挑转链接">
            <Input
              style={{ width: '300px' }}
              value={formData.gradePopupLink}
              name="gradePopupLink"
              placeholder="请输入等级不足弹窗挑转链接"
              onChange={(gradePopupLink) => {
                setForm({ gradePopupLink });
              }}
            />
          </Form.Item>
          <Form.Item label="活动规则-弹窗">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={516}
                  height={572}
                  value={formData.ruleBg}
                  onChange={(ruleBg) => {
                    setForm({ ruleBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：516px*572px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ ruleBg: defaultValue?.ruleBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
