.container {
  display: flex;
  margin-top: 15px;
  position: relative;
}
.preview {
  width: 320px;
  position: sticky;
  top: 0;
  .phone {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/157306/20/30830/40313/63357010E889bd1a5/8b9ee272dfa9ba95.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    width: 894px;
    height: 1686px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    transform: scale(0.33333);
    transform-origin: left top;


    .share {
      width: 250px;
      height: 524px;
      background: rgba(0, 0, 0, .5);
      display: flex;
      justify-content: center;
    }
  }
}
.qrcode {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  position: absolute;
  top: 560px;
  left: 100px;

  span {
    margin-top: 10px;
  }
}
.operation {
  flex: 1;
}
