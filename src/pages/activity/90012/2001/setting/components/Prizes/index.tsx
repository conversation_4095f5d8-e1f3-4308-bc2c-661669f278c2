/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2024-08-31 11:19
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, NumberPicker, Input, Grid, Message, Icon } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import { activityEditDisabled, deepCopy, isDisableSetPrize } from '@/utils';
import { FormLayout, StepList, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import styles from '@/pages/activity/90012/2001/setting/style.module.scss';

interface Props {
  value: StepList | undefined;
  // sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<StepList>) => void;
  onDelete: () => void;
  stepList: StepList[];
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default (props) => {
  const { sRef, onChange, value, onDelete, stepList } = props;
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  // 暂存累计/连续签到天数
  const [signDaySto, setSignDaySto] = useState('');
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    if (formData.stepPrizeList.length >= 2) {
      const itemPrizeKey = data.prizeKey;
      const isFind = formData.stepPrizeList.find((item) => item.prizeKey === itemPrizeKey);
      if (isFind) {
        Message.error('同系列奖品不可重复设置');
        return;
      }
    }
    // 更新指定index 奖品信息
    if (tableName === 'prizeDay') {
      formData.prizeDay[target] = data;
    } else if (tableName === 'stepPrizeList') {
      formData.stepPrizeList[target] = { ...data, potNum: signDaySto };
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.stepPrizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.stepPrizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 1 - prizeListLength; i++) {
      list.push(deepCopy(PRIZE_INFO));
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ stepPrizeList: list.length ? list : formData.stepPrizeList });
  }, []);

  // useImperativeHandle(sRef, (): { submit: () => null } => ({
  //   submit: () => {
  //     console.log(898);
  //     let err: object | null = null;
  //     field.validate((errors: Object[]): void => {
  //       console.log('errors', errors);
  //       err = errors;
  //     });
  //     return err;
  //   },
  // }));
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="阶梯及奖品设置">
        {stepList.length > 1 && (
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => {
              onDelete();
            }}
          >
            <Icon type="ashbin" style={{ color: '#0958d9', float: 'right' }} />
          </div>
        )}
        <Form {...formItemLayout} field={field}>
          <FormItem required label="阶梯名称">
            <FormItem required requiredMessage="请输入阶梯名称">
              <Input
                value={formData.stepName}
                onChange={(stepName) => {
                  setData({ stepName });
                }}
                placeholder="请输入阶梯名称"
                name="activityName"
                maxLength={20}
                showLimitHint
                className="w-300"
              />
            </FormItem>
          </FormItem>
          <FormItem required label="阶梯门槛">
            <FormItem required requiredMessage="请输入阶梯门栏">
              <NumberPicker
                min={1}
                max={9999999}
                step={1}
                type="inline"
                value={formData.stepThreshold}
                onChange={(stepThreshold) => {
                  setData({ stepThreshold });
                }}
                placeholder="请输入阶梯门槛"
                name="stepThreshold"
              />
              元
            </FormItem>
          </FormItem>
          <FormItem required label="奖品列表">
            <FormItem>
              <Table dataSource={formData.stepPrizeList} style={{ marginTop: '15px' }}>
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                  dataIndex="prizeType"
                />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    if (row.prizeType === 1) {
                      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                    } else {
                      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                    }
                  }}
                />
                <Table.Column
                  title="发放份数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => (
                    <div>{row.prizeName ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                  )}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem disabled={isDisableSetPrize(formData.stepPrizeList, index)}>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            setSignDaySto(formData.stepPrizeList[index].potNum);
                            let row = formData.stepPrizeList[index];
                            if (row.prizeName === '') {
                              row = null;
                            }
                            setEditValue(row);
                            setTarget(index);
                            setTableName('stepPrizeList');
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                        {formData.stepPrizeList.length > 1 && (
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              Dialog.confirm({
                                v2: true,
                                title: '提示',
                                centered: true,
                                content: '确认删除该奖品？',
                                onOk: () => {
                                  formData.stepPrizeList.splice(index, 1);
                                  setData(formData);
                                },
                                onCancel: () => console.log('cancel'),
                              } as any);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        )}
                      </FormItem>
                    )}
                  />
                )}
              </Table>
            </FormItem>
            {!activityEditDisabled() && (
              <FormItem>
                <Button
                  disabled={formData.stepPrizeList.length >= 10}
                  type="primary"
                  onClick={() => {
                    formData.stepPrizeList.push(deepCopy(PRIZE_INFO));
                    setData(formData);
                  }}
                >
                  +添加奖品（{formData.stepPrizeList.length}/10）
                </Button>
              </FormItem>
            )}
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
          typeList={[3]}
          defaultTarget={3}
        />
      </LzDialog>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="	罐数" dataIndex="potNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
