import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, Field, DatePicker2, Message, Radio, NumberPicker, Table, Button } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { PageData, FormLayout } from '../../../util';
import { activityEditDisabled, isDisableSetPrize } from '@/utils';
import dayjs from 'dayjs';
import styles from '@/pages/activity/95003/2001/setting/style.module.scss';
import constant from '@/utils/constant';
import format from '@/utils/format';
import LzToolTip from '@/components/LzToolTip';

const RadioGroup = Radio.Group;
const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>, string) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  // 时间校验
  const validateLuckyDrawTime = (rule, val, callback): void => {
    const drawStartTime = val[0];
    const drawEndTime = val[1];
    if (drawStartTime && drawEndTime) {
      callback();
    } else if (dayjs(drawEndTime).diff(dayjs(drawStartTime), 'day') > 60) {
      callback('抽奖时间区间不得超过60天');
    } else if (drawStartTime == drawEndTime) {
      callback('抽奖时间的开始时间和结束时间不能相同');
    } else {
      callback('请选下单时间');
    }
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (luckyDrawTimeRangeData): void => {
    // 时间不能超过60天
    if (dayjs(luckyDrawTimeRangeData[1]).diff(dayjs(luckyDrawTimeRangeData[0]), 'day') > 60) {
      Message.error('抽奖时间区间不得超过60天');
      return;
    }
    setData({
      luckyDrawTimeRangeData,
      drawStartTime: format.formatDateTimeDayjs(luckyDrawTimeRangeData[0]),
      drawEndTime: format.formatDateTimeDayjs(luckyDrawTimeRangeData[1]),
    });
  };
  const addPrize = () => {
    formData.drawTimesList.push({
      orderPrice:
        formData.drawTimesList.length > 0
          ? Math.max(...formData.drawTimesList.map((item: any) => item.orderPrice)) + 100
          : 100,
      drawNum:
        formData.drawTimesList.length > 0
          ? Math.max(...formData.drawTimesList.map((item: any) => item.drawNum)) + 1
          : 1,
      sort: formData.drawTimesList.length + 1,
    });
    setData(formData);
  };

  const orderPriceChange = (data, index): boolean | void => {
    // 更新指定index 奖品信息
    const newFormData = { ...formData };
    newFormData.drawTimesList[index].orderPrice = data;
    setData(newFormData);
  };
  const chanceChange = (data, index): boolean | void => {
    // 更新指定index 奖品信息
    const newFormData = { ...formData };
    newFormData.drawTimesList[index].drawNum = data;
    setData(newFormData);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data }, 'task');
  };
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="抽奖规则设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <Form.Item
            label="抽奖时间"
            required
            requiredMessage="请选抽奖时间"
            validator={validateLuckyDrawTime}
            extra={<div className="next-form-item-help">注：抽奖时间区间不得超过60天</div>}
          >
            <RangePicker
              className="w-300"
              name="luckyDrawTimeRangeData"
              inputReadOnly
              format={dateFormat}
              hasClear={false}
              showTime
              value={
                formData.luckyDrawTimeRangeData || [new Date(formData.drawStartTime), new Date(formData.drawEndTime)]
              }
              onChange={onDataRangeChange}
              disabledDate={(date) => {
                return (
                  date.valueOf() < dayjs().subtract(1, 'day').valueOf() ||
                  date.valueOf() > dayjs().add(60, 'day').valueOf()
                );
              }}
            />
          </Form.Item>

          <Form.Item
            label="最大抽奖次数"
            required
            extra={
              <div className="next-form-item-help">注：限制活动周期内用户最大抽奖次数，为避免客诉，需谨慎设置</div>
            }
          >
            <RadioGroup value={formData.drawLimit} onChange={(drawLimit: number) => setData({ drawLimit })}>
              <Radio id="1" value={1}>
                不限制
              </Radio>
              <Radio id="2" value={2}>
                限制
              </Radio>
            </RadioGroup>
            {formData.drawLimit === 2 && (
              <div className={styles.panel}>
                <Form.Item
                  name="drawNumMax"
                  required
                  requiredTrigger="onBlur"
                  requiredMessage="请输入最大抽奖次数"
                  style={{ margin: 0 }}
                >
                  <NumberPicker
                    value={formData.drawNum}
                    onChange={(drawNum: number) => setData({ drawNum })}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  次
                </Form.Item>
              </div>
            )}
          </Form.Item>

          <Form.Item
            label="最大获奖次数"
            required
            extra={<div className="next-form-item-help">注：限制活动周期内用户最大中奖次数</div>}
          >
            <RadioGroup
              value={formData.winLotteryLimit}
              onChange={(winLotteryLimit: number) => setData({ winLotteryLimit })}
            >
              <Radio id="1" value={1}>
                不限制
              </Radio>
              <Radio id="2" value={2}>
                限制
              </Radio>
            </RadioGroup>
            {formData.winLotteryLimit === 2 && (
              <div className={styles.panel}>
                <Form.Item
                  name="winLotteryNum"
                  required
                  requiredTrigger="onBlur"
                  requiredMessage="请输入最大获奖次数"
                  style={{ margin: 0 }}
                >
                  <NumberPicker
                    value={formData.winLotteryNum}
                    onChange={(winLotteryNum: number) => setData({ winLotteryNum })}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  次
                </Form.Item>
              </div>
            )}
          </Form.Item>
          <Form.Item label="新增抽奖次数" required>
            <Form.Item>
              <RadioGroup
                name="threshold"
                value={formData.drawTimesType}
                onChange={(drawTimesType) => {
                  const newData = [
                    {
                      orderPrice: 100,
                      drawNum: 1,
                    },
                  ];
                  setData({ drawTimesList: newData, drawTimesType });
                }}
              >
                <Radio value={1}>固定金额倍数</Radio>
                <Radio value={2}>自定义阶梯</Radio>
              </RadioGroup>
            </Form.Item>
            <div className="next-form-item-help">
              注：按不同消费金额设置奖项等级，请按消费金额从低到高排列，最多可设置3级
              <LzToolTip content="每级获得的获奖次数为用户可抽奖的总次数，如设置为消费满100元获得1次抽奖机会， 满200元获得2次抽奖机会，用户购买满200元时，实际获得2次抽奖机会" />
            </div>
            {formData.drawTimesType === 1 && (
              <Form.Item
                label={false}
                colon={false}
                required
                asterisk={false}
                requiredMessage="请选择活动门槛"
                name="supportLevels"
              >
                <div style={{ display: 'flex', lineHeight: '28px' }}>
                  累计消费每满 &nbsp;
                  <Form.Item name="orderPrice" required requiredTrigger="onBlur" requiredMessage="请输入累计消费金额">
                    <NumberPicker
                      min={1}
                      max={9999999}
                      precision={0}
                      type="inline"
                      value={formData.drawTimesList[0]?.orderPrice}
                      onChange={(v: number) => {
                        orderPriceChange(v, 0);
                      }}
                    />
                    &nbsp;
                  </Form.Item>
                  <span>元，可获得</span>
                  <Form.Item name="drawNum" required requiredTrigger="onBlur" requiredMessage="请输入赠送次数">
                    <NumberPicker
                      min={1}
                      max={9999999}
                      precision={0}
                      type="inline"
                      value={formData.drawTimesList[0]?.drawNum}
                      onChange={(v: number) => {
                        chanceChange(v, 0);
                      }}
                    />
                    &nbsp;
                  </Form.Item>
                  次抽奖机会
                </div>
              </Form.Item>
            )}
            {formData.drawTimesType === 2 && (
              <Form.Item label={false} required>
                <Table dataSource={formData.drawTimesList} style={{ marginTop: 10 }}>
                  <Table.Column
                    title="阶梯等级"
                    dataIndex="prizeName"
                    cell={(_, index, row) => <div>{index + 1}级</div>}
                  />
                  <Table.Column
                    title="累计消费金额(元)"
                    cell={(_, index, row) => {
                      return (
                        <div>
                          <Form.Item
                            name={`orderPrice-${index}`}
                            style={{ marginTop: '5px' }}
                            required
                            requiredMessage="请填写累计消费金额"
                          >
                            <NumberPicker
                              onChange={(v: number) => {
                                orderPriceChange(v, index);
                              }}
                              className={styles.formNumberPicker}
                              type="inline"
                              min={1}
                              max={9999999}
                              precision={0}
                              defaultValue={1}
                              key={formData.drawTimesList[index]}
                              value={formData.drawTimesList[index].orderPrice}
                              disabled={activityEditDisabled()}
                            />
                          </Form.Item>
                        </div>
                      );
                    }}
                  />

                  <Table.Column
                    title="抽奖机会(次)"
                    cell={(_, index, row) => {
                      return (
                        <div>
                          <Form.Item
                            name={`drawNum-${index}`}
                            style={{ marginTop: '5px' }}
                            required
                            requiredMessage="请填写抽奖机会"
                          >
                            <NumberPicker
                              onChange={(v: number) => {
                                chanceChange(v, index);
                              }}
                              className={styles.formNumberPicker}
                              type="inline"
                              min={1}
                              max={9999999}
                              precision={0}
                              defaultValue={1}
                              key={formData.drawTimesList[index]}
                              value={formData.drawTimesList[index].drawNum}
                              disabled={activityEditDisabled()}
                            />
                          </Form.Item>
                        </div>
                      );
                    }}
                  />
                  {!activityEditDisabled() && (
                    <Table.Column
                      title="操作"
                      width={130}
                      cell={(val, index, _) => (
                        <Form.Item
                          style={{ marginBottom: '0' }}
                          disabled={isDisableSetPrize(formData.drawTimesList, index)}
                        >
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              formData.drawTimesList.splice(index, 1);
                              setData(formData);
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        </Form.Item>
                      )}
                    />
                  )}
                </Table>
                <Button
                  type={'primary'}
                  disabled={activityEditDisabled() || formData.drawTimesList.length >= 3}
                  onClick={addPrize}
                  style={{ marginTop: 10 }}
                >
                  添加设置({formData.drawTimesList.length}/3)
                </Button>
              </Form.Item>
            )}
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
