import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import {
  // checkActivityData,
  FormLayout,
  // generateMembershipString,
  PageData,
  // PrizeInfo,
} from '@/pages/activity/95003/2001/util';
// import format from '@/utils/format';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
  checkForm: () => boolean;
}

export default ({ onChange, defaultValue, value, sRef, checkForm }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // const generatePrizeString = () => {
  //   const prizeList = formData.prizeList.filter(
  //     (e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与',
  //   );
  //   const strList = prizeList.map((item, index): string => {
  //     return `（${index + 1}）${item.prizeName}: 共${item.sendTotalCount}份，单份奖品价值${Number(
  //       item.unitPrice,
  //     ).toFixed(2)}元`;
  //   });
  //   return strList.join('\n');
  // };
  // const generatePartake = () => {
  //   if (formData.partake) {
  //     return `每日${formData.partakeStartTime}~${formData.partakeEndTime}`;
  //   }
  //   return '';
  // };

  /**
   * 自动生成规则说明
   */
  //   const autoCreateRuleDesc = async (): Promise<void> => {
  //     if (!checkForm()) return;
  //     // // 判断奖品列表是否存在有效奖品
  //     const prizeList = formData.prizeList.filter(
  //       (e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与',
  //     );
  //     const isValidateData: boolean = checkActivityData(prizeList, formData);
  //     if (!isValidateData) {
  //       return;
  //     }
  //     const rules = `1. 活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
  //       formData.endTime,
  //     )}；${generatePartake()}
  // 2. 参与规则：在${format.formatDateTimeDayjs(formData.orderStartTime)}至${format.formatDateTimeDayjs(
  //       formData.orderEndTime,
  //     )}时间段内下单（订单为“【${formData.orderStatus === 1 ? '已完成' : '已付款'}】”状态）且单笔订单商品金额满${
  //       formData.orderRestrainAmount
  //     }元即可参与；
  // 3.活动对象：${generateMembershipString(formData, 'string')}；
  // 4. 参与活动商品：${formData.orderSkuisExposure === 1 ? '本店指定商品' : '本店全部商品'}；
  // （注意：如果参与活动商品是预售商品，需要订单支付尾款且满足活动设置规则才可以参与活动）
  // 5.奖品说明：
  // ${generatePrizeString()}
  // 6.请在活动结束时间${format.formatDateTimeDayjs(
  //       formData.endTime,
  //     )}前，确认收货（即订单为“已完成”状态），如订单尚处在待发货、未签收、未取件、等非我方因素导致的异常状态，则取消中奖资格；
  // 7. 满足下单需求的会员需及时填写赠品收货信息；
  // 8. 满足条件参与领取后，待订单完成后，系统自动发放奖励；
  // 9.礼品数量有限，先到先得，存在因奖品发放完毕而与奖品擦肩而过的情况；
  // 10.抽中后不代表获得奖品，奖品以实际发放情况为主；
  // 11.因客户原因导致的恶意退款，取消赠品获取资格；
  // 12.虚拟奖品将在完成收货后30分钟内自动发放；
  // 13.实物奖品需填写收货地址后联系商家进行发货；
  // 14.活动高峰期间订单校验会有所延迟，请耐心等待稍后再试；
  // 15.如果是实物奖项，请于1小时内填写地址，否则将视为放弃机会；
  // 16.因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新；
  //     `;
  //     setData({ rules });
  //     field.setErrors({ rules: '' });
  //   };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              className="form-input-ctrl"
            />
          </FormItem>
          {/* <FormItem label=" " colon={false}> */}
          {/*  <Button */}
          {/*    type="primary" */}
          {/*    className="table-cell-btn" */}
          {/*    onClick={autoCreateRuleDesc} */}
          {/*    style={{ marginRight: '15px' }} */}
          {/*    text */}
          {/*  > */}
          {/*    自动生成规则说明 */}
          {/*  </Button> */}
          {/*  <span style={{ color: 'red', fontSize: '12px' }}> */}
          {/*    提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。 */}
          {/*  </span> */}
          {/* </FormItem> */}
        </Form>
      </LzPanel>
    </div>
  );
};
