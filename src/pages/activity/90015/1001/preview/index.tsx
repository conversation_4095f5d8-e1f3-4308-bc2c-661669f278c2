import React, { useEffect, useReducer, useState } from 'react';
import { Button, Form, Input, Table } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import format from '@/utils/format';
import dayjs from 'dayjs';
import SkuList from '@/components/SkuList';
import LzDialog from '@/components/LzDialog';
import { getLiveOrders } from '@/api/v90015';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}
export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  const [liveOrderVisible, setLiveOrderVisible] = useState(false);
  const [orderSkuListVisible, setOrderSkuListVisible] = useState(false);
  const [dialogLiveOrderList, setDialogLiveOrderList] = useState<any[]>([]);

  useEffect(() => {
    const id: string = defaultValue.id;
    console.log(id);
    if (id) {
      getLiveOrders({ activityId: id }).then((res) => {
        setDialogLiveOrderList(res);
      });
    } else {
      setDialogLiveOrderList(formData.liveOrderList);
    }
  }, []);
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${dayjs(formData.rangeDate[0]).format('YYYY-MM-DD HH:mm:ss')}至${dayjs(
          formData.rangeDate[1],
        ).format('YYYY-MM-DD HH:mm:ss')}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && <SkuList skuList={formData.exposureSkuList} />}
        </FormItem>
        <FormItem label="订单类型">
          {formData.orderType === 0 && <div>全店</div>}
          {formData.orderType === 1 && <div>直播间订单</div>}
        </FormItem>
        {formData.orderType === 1 && (
          <>
            <FormItem label="直播间链接">{formData.liveLink}</FormItem>
            <FormItem label="直播间订单">
              <Button
                onClick={() => {
                  console.log(formData.liveOrderList);
                  setLiveOrderVisible(true);
                }}
              >
                查看直播间订单
              </Button>
            </FormItem>
          </>
        )}
        <FormItem label="订单商品">
          {formData.orderSkuisExposure === 0 && (
            <div>
              全部商品<span style={{ fontSize: '12px', color: 'gray', marginTop: '15px' }}>(不含虚拟商品)</span>
            </div>
          )}
          {(formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2) && (
            <>
              {formData.orderSkuisExposure === 1 && <div>指定商品</div>}
              {/* {formData.orderSkuisExposure === 2 && <div>排除商品</div>} */}
              <SkuList skuList={formData.orderSkuList} />
            </>
          )}
        </FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderRangeData[0],
        )}至${format.formatDateTimeDayjs(formData.orderRangeData[1])}`}</FormItem>
        {formData.ladderList.map((item, index) => {
          return (
            <>
              <FormItem label="阶梯等级">{index + 1}级</FormItem>
              <FormItem label="金额">{formData.ladderList[index].moneyNum}（累计金额）</FormItem>
              <FormItem label="阶梯名称">{formData.ladderList[index].ladderName}</FormItem>
              <FormItem label="阶梯封面">
                <img src={formData.ladderList[index].ladderImg} style={{ height: 100 }} alt="" />
              </FormItem>
              <FormItem label="设置阶梯奖励">
                <Table dataSource={item.ladderPrizeList} style={{ marginTop: '15px' }}>
                  <Table.Column title="奖品名称" dataIndex="prizeName" />
                  <Table.Column
                    title="奖项类型"
                    cell={(v, index2, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                    dataIndex="prizeType"
                  />
                  <Table.Column
                    title="单位数量"
                    cell={(v, index2, row) => {
                      if (row.prizeType === 1) {
                        return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                      } else {
                        return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                      }
                    }}
                  />
                  <Table.Column title="价值(元)" dataIndex="unitPrice" />
                  <Table.Column title="发放份数" dataIndex="sendTotalCount" />
                  <Table.Column
                    title="奖品图"
                    dataIndex="prizeImg"
                    cell={(_, index2, row) => <img src={row.prizeImg} style={{ height: 50 }} alt="" />}
                  />
                </Table>
              </FormItem>
            </>
          );
        })}
        <FormItem label="领取限制">{formData.receiveLimit === 0 ? '单次领取' : '多次领取'}</FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      <LzDialog
        title={false}
        visible={liveOrderVisible}
        footer={false}
        onClose={() => setLiveOrderVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {formData.liveOrderList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={dialogLiveOrderList} fixedHeader>
              <Table.Column title="用户pin" dataIndex="encryptPin" />
              <Table.Column title="订单号" dataIndex="orderId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
