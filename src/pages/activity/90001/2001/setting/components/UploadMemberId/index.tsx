/*
 * @Author: liuyan
 * @Date: 2024-08-14 11:52:01
 * @Description: 上传memberId
 */
import { Button, Dialog, Message, Table, Upload } from '@alifd/next';
import { config } from 'ice';
import React, { useState } from 'react';
import CONST from '@/utils/constant';
import { activityEditDisabled } from '@/utils';
import LzDialog from '@/components/LzDialog';
import { getMemberId } from '@/api/v90001';

export default function UploadMemberId({ onChange, activityKey }) {
  const [fileList, setFileList] = useState<File[]>([]);
  const [visible, setVisible] = useState(false);
  const [memberList, setMemberList] = useState([]);

  const getMemberIds = async () => {
    const res = await getMemberId({ activityKey });
    const data = res.map((item) => {
      return {
        memberId: item,
      };
    });
    setMemberList(data);
    setVisible(true);
  };

  return (
    <>
      {!activityKey && (
        <Upload
          action={`${config.baseURL}/90001/importMemberIdExcel`}
          method="post"
          headers={{
            token: localStorage.getItem(CONST.LZ_SSO_TOKEN),
            prd: localStorage.getItem(CONST.LZ_SSO_PRD),
          }}
          value={fileList}
          limit={1}
          listType="text"
          accept=".xls,.xlsx"
          onChange={(info) => {
            if (info.length) {
              if (info[0].size > 5 * 1024 * 1024) {
                Message.error('文件大小不能超过5M');
                return;
              }
            } else {
              onChange('');
            }
            setFileList(info);
          }}
          onError={(res) => {
            if (res.state === 'error') {
              if (res.response?.message) {
                Message.error(res.response?.message);
              } else {
                Message.error('文件错误，请上传正确的文件');
              }
            }
          }}
          onSuccess={(res) => {
            console.log(res);
            if (res.response.code === 200) {
              console.log(res, '上传数据');
              onChange(res.response.data);
              Dialog.success({
                title: '导入结果',
                content: (
                  <div>
                    <p>导入成功</p>
                  </div>
                ),
                onOk: () => {
                  console.log('导入成功');
                },
              });
            } else if (res.response?.message) {
              setFileList([]);
              Message.error(res.response?.message);
            } else {
              setFileList([]);
              Message.error('文件错误，请上传正确的文件');
            }
          }}
          style={{ marginBottom: 10 }}
        >
          <div className="next-upload-drag">
            <p className="next-upload-drag-icon">
              <Button type="primary" disabled={activityEditDisabled()}>
                上传memberID
              </Button>
            </p>
            <p className="next-upload-drag-hint">支持xls、xlsx类型的文件</p>
          </div>
        </Upload>
      )}
      {!!activityKey && (
        <div>
          {!activityEditDisabled() && (
            <Button
              type="primary"
              onClick={() => {
                setFileList([]);
                onChange('');
              }}
            >
              重新上传
            </Button>
          )}
          <Button onClick={getMemberIds}>查看memberId</Button>
        </div>
      )}
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <Table dataSource={memberList} style={{ marginTop: 20 }}>
          <Table.Column title="memberId" dataIndex="memberId" />
        </Table>
      </LzDialog>
    </>
  );
}
