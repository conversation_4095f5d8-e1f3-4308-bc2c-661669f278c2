/**
 * Author: liangyu
 * Date: 2023-06-06 15:48
 * Description:
 */

import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Button, Field, Input } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { checkActivityData, FormLayout, PageData } from '@/pages/activity/90001/1001/util';
import format from '@/utils/format';
import { getShop } from '@/utils/shopUtil';

const { shopName } = getShop();

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const generateChanceString = (): string => {
    const timeStr = `付定金时间：${format.formatDateTimeDayjs(
      formData.payEarnestStartTime,
    )}至${format.formatDateTimeDayjs(formData.payEarnestEndTime)}；`;
    const timeStr1 = `付尾款时间：${format.formatDateTimeDayjs(
      formData.payBalanceStartTime,
    )}至${format.formatDateTimeDayjs(formData.payBalanceEndTime)}；`;
    const result: string = timeStr + timeStr1;
    return result ? `${timeStr}\n${timeStr1}` : `${timeStr}\n${timeStr1}`;
  };
  const generatePrizeString = () => {
    const timeStr = `${format.formatDateTimeDayjs(formData.payEarnestStartTime)}至${format.formatDateTimeDayjs(
      formData.payEarnestEndTime,
    )}`;
    const timeStr1 = `${format.formatDateTimeDayjs(formData.payBalanceStartTime)}至${format.formatDateTimeDayjs(
      formData.payBalanceEndTime,
    )}`;
    const strList = formData.giveawayList.map((item, index): string => {
      return `奖励${index + 1}：\n在${shopName}购买${
        item.skuList[0].skuName
      }，支付预售定金时间在${timeStr}时间段内，支付尾款时间在${timeStr1}时间段内，且支付订单排名在前${
        item.prizeList[0].sendTotalCount
      }名，在订单完成后可领取${item.prizeList[0].prizeName}。`;
    });
    return strList.join('\n');
  };

  /**
   * 自动生成规则说明
   */
  const autoCreateRuleDesc = async (): Promise<void> => {
    // // 判断奖品列表是否存在有效奖品
    const giveaList = formData.giveawayList;
    const isValidateData: boolean = checkActivityData(giveaList, formData);
    if (!isValidateData) {
      return;
    }
    const rules = `活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
      formData.endTime,
    )}；
${generateChanceString()}
活动玩法：前N名付款有礼
${generatePrizeString()}
注意事项：
(1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虛假交易、扰乱系统、实施网络攻击等） 参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格，如给商家造成损失的，商家将保留向违规用户继续追索的权利;
(2）如遇不可抗力 (包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的），商家有权取消、修改或暂停本活动；
(3）活动主办方不对参与者网络及活动平台的稳定性及安全性负责，并不对因网络传输原因而导致参与者提交的信息错误或延误承担任何责任。所有参与者完全自愿选择参加本次活动；
(4)  为了遵守相关法律规定和监管要求，我们將对您输入的内容进行自动化检测。请确保您输入的内容不包含违法违规内容。
(5）大促期间，因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新。`;
    setData({ rules });
    field.setErrors({ rules: '' });
  };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <Button
              type="primary"
              className="table-cell-btn"
              onClick={autoCreateRuleDesc}
              style={{ marginRight: '15px' }}
              text
            >
              自动生成规则说明
            </Button>
            <span style={{ color: 'red', fontSize: '12px' }}>
              提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。
            </span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
