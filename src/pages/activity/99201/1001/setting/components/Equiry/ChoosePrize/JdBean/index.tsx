import { Radio, Form, Button, NumberPicker, Input, Dialog, Grid, Field, Message } from '@alifd/next';
import React, { useState, useReducer, useEffect } from 'react';
import Plan from './components/Plan';
import active from '../assets/active.png';
import notActive from '../assets/not-active.png';
import delIcon from '../assets/del-icon.png';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { activityEditDisabled, numRegularCheckInt } from '@/utils';
import format from '@/utils/format';
import { prizeFormLayout } from '@/components/ChoosePrize';

interface ComponentProps {
  [propName: string]: any;
}

const PropertyJdBean = ({
  editValue,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasOrderPrice = false,
  height,
  width,
  prizeNameLength,
  formData,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/176585/24/10488/6916/60a4cb50E562734ab/f9ab956ec09a4146.png';
  const planImg = require('../assets/2.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 2,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  const field: Field = Field.useField();
  // 发放份数/单次发放量
  const setData = (data: any): void => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): boolean | void => {
    if (prizeData.prizeName.length > 10) {
      Message.error(`奖品名称长度不能大于10，请修改`);
      return false;
    }
    if (prizeData.unitCount * prizeData.sendTotalCount > prizeData.quantityRemain) {
      Message.error(`发放京豆数不能大于京豆余量【${prizeData.quantityRemain}】，请重新设置`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    // if (hasProbability && +prizeData.probability <= 0) {
    //   Message.error(`中奖概率必须大于0`);
    //   return false;
    // }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100%`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.prizeName = '';
    setPrizeData(data);
  };
  const onSubmit = (resource: any) => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.planId;
    resource.prizeName = resource.planName.substring(0, prizeNameLength);
    setPrizeData(resource);
    setWinJdShow(false);
    field.setErrors({ prizeKey: '', prizeName: '' });
  };
  return (
    <div className={styles.PropertyJdBean}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item required requiredMessage="请选择京豆计划" style={{ paddingTop: '15px' }}>
          {<Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />}
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
              <img className={styles.prizeBg} src={prizeData.planStatus === 2 ? active : notActive} alt="" />
              {!activityEditDisabled() && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ width: 210 }}>
                <p>计划名称：{prizeData.planName}</p>
                <p>创建时间：{format.formatDateTimeDayjs(prizeData.createTime)}</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>计划编号：{prizeData.planId}</p>
                <p>
                  生效时间：{format.formatDateTimeDayjs(prizeData.startDate)}至
                  {format.formatDateTimeDayjs(prizeData.endDate)}
                </p>
                <p>京豆余量：{prizeData.quantityRemain}</p>
              </div>
            </div>
          )}
        </Form.Item>
        <div style={{ color: 'red' }}>
          注：请不要将京豆计划用于其他活动或直接发放，否则可能会导致领取奖品失败，进而产生客诉。
        </div>
        <Form.Item
          style={{ marginTop: '20px' }}
          label="单次发放量"
          required
          requiredMessage="请输入单次发放量"
          validator={numRegularCheckInt}
          disabled={activityEditDisabled()}
        >
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={999999999}
            step={1}
            name="unitCount"
            placeholder="请输入单次发放量"
            value={prizeData.unitCount}
            onChange={(unitCount: any) => {
              setData({
                unitCount,
                unitPrice: unitCount ? unitCount / 100 : 0,
                prizeName: unitCount && `${unitCount}京豆`,
              });
              field.setErrors({ prizeName: '' });
            }}
          />
          京豆
        </Form.Item>
        <Form.Item label="单份价值" required>
          <NumberPicker className={styles.formNumberPicker} type="inline" disabled value={prizeData.unitPrice} />元
        </Form.Item>
        {hasOrderPrice && (
          <Form.Item label="获奖消费额度" required requiredMessage="请输入获奖消费额度">
            <NumberPicker
              className={styles.formNumberPicker}
              onChange={(orderPrice: any) => setData({ orderPrice })}
              placeholder="请输入获奖消费额度"
              name="orderPrice"
              type="inline"
              min={0}
              max={9999999}
              precision={2}
              value={prizeData.orderPrice}
            />
            元
          </Form.Item>
        )}
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数">
          <NumberPicker
            className={styles.formNumberPicker}
            placeholder="请输入发放份数"
            type="inline"
            min={1}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              placeholder="请输入中奖概率"
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}

            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType}
                onChange={(dayLimitType: number) => setData({ dayLimitType, dayLimit: dayLimitType === 1 ? 0 : '' })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    placeholder="请输入每日限额"
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择京豆计划"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};

export default PropertyJdBean;
