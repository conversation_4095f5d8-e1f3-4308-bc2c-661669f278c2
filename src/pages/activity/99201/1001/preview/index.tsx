import React, { useReducer } from 'react';
import { Form, Input, Table } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE, AUTO_STOP_TEXT, MEMBER_GIVE_LIMIT_TEXT } from '../util';
import styles from './style.module.scss';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="兑换设置">
          <div>
            <FormItem label={'奖品列表：按等级设置'}>
              <>
                {formData.gradeLabel.map((item, index) => {
                  return (
                    <>
                      <div className={styles['grade-label-text']}>{item}</div>
                      <Table
                        dataSource={formData.prizeListMap[formData.supportLevels.split(',')[index]]}
                        style={{ margin: '15px 0px 20px' }}
                      >
                        <Table.Column title="位置" cell={(_, index1) => <div>{index1 + 1}</div>} />
                        <Table.Column title="奖品名称" dataIndex="prizeName" />
                        <Table.Column
                          title="奖品类型"
                          cell={(_, index1, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                          dataIndex="prizeType"
                        />
                        <Table.Column
                          title="单位数量"
                          cell={(_, index, row) => <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>}
                        />
                        <Table.Column
                          title="发放份数"
                          cell={(_, index1, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                        />
                        <Table.Column
                          title="单份价值(元)"
                          cell={(_, index, row) => (
                            <div>
                              {row.unitPrice === 0
                                ? row.unitPrice
                                : row.unitPrice
                                ? Number(row.unitPrice).toFixed(2)
                                : ''}
                            </div>
                          )}
                        />
                        <Table.Column
                          title="奖品图"
                          cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                        />
                      </Table>
                    </>
                  );
                })}
              </>
            </FormItem>
            <div className={styles['point-box']}>
              <span className={styles['title-color']}>自动停止:</span>
              {formData.openStatus == 1 ? <span>{AUTO_STOP_TEXT[formData.autoStop]}</span> : '关闭'}
            </div>
            <div>
              <span className={styles['title-color']}>领取次数:</span>
              <span>每个等级只能领取一次</span>
            </div>
            <div>
              <span className={styles['title-color']}>会员发放范围:</span>
              <span>{MEMBER_GIVE_LIMIT_TEXT[formData.grantScope]}</span>
            </div>
          </div>
        </FormItem>
        {/* <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem> */}
        {/* {formData.shareStatus !== 0 && ( */}
        {/*  <> */}
        {/*    <FormItem label="分享标题">{formData.shareTitle}</FormItem> */}
        {/*    <FormItem label="图文分享图片"> */}
        {/*      <img src={formData.h5Img} style={{ width: '200px' }} alt="" /> */}
        {/*    </FormItem> */}
        {/*    <FormItem label="京口令分享图片"> */}
        {/*      <img src={formData.cmdImg} style={{ width: '200px' }} alt="" /> */}
        {/*    </FormItem> */}
        {/*    <FormItem label="小程序分享图片"> */}
        {/*      <img src={formData.mpImg} style={{ width: '200px' }} alt="" /> */}
        {/*    </FormItem> */}
        {/*  </> */}
        {/* )} */}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
