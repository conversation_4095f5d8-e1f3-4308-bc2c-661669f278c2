/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import { activityEditDisabled, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.prizeBizList[1].prizeList[target] = data;
    // 计算总概率
    formData.totalProbability2 = formData.prizeBizList[1].prizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability2 * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.prizeBizList[1].prizeList.splice(target, 1, PRIZE_INFO);
      formData.totalProbability2 = formData.prizeBizList[1].prizeList
        .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
        .reduce((val, total) => {
          return val + Number(total.probability);
        }, 0);
      return false;
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.prizeBizList[1].prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.prizeBizList[1].prizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 8 - prizeListLength; i++) {
      list.push(PRIZE_INFO);
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    formData.prizeBizList[1].prizeList = list.length ? list : formData.prizeBizList[1].prizeList;
    // setData({ prizeList: list.length ? list : formData.prizeBizList[1].prizeList });
    setData({ prizeBizList: formData.prizeBizList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="奖池2奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem isPreview label="单次中奖总概率">
            {formData.totalProbability2.toFixed(3)}%
          </FormItem>
          <FormItem label="奖品列表" required>
            <Table dataSource={formData.prizeBizList[1].prizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>
                    {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                      ? row.unitPrice
                      : row.unitPrice
                      ? Number(row.unitPrice).toFixed(2)
                      : ''}
                  </div>
                )}
              />
              <Table.Column
                title="中奖概率(%)"
                cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>}
              />
              <Table.Column
                title="每日发放限额"
                cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              {!activityEditDisabled() && (
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <FormItem disabled={isDisableSetPrize(formData.prizeBizList[1].prizeList, index)}>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.prizeBizList[1].prizeList[index];
                          if (row.prizeName === '谢谢参与') {
                            row = null;
                          }
                          setEditValue(row);
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                      </Button>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (_.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空该奖品？',
                              onOk: () => {
                                formData.prizeBizList[1].prizeList.splice(index, 1, PRIZE_INFO);
                                formData.totalProbability2 = formData.prizeBizList[1].prizeList
                                  .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                                  .reduce((v, total) => {
                                    return v + Number(total.probability);
                                  }, 0);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        <i className={`iconfont icon-shanchu`} />
                      </Button>
                      {formData.prizeBizList[1].prizeList.length > 0 && index > 0 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            formData.prizeBizList[1].prizeList.splice(
                              index - 1,
                              1,
                              ...formData.prizeBizList[1].prizeList.splice(index, 1, formData.prizeBizList[1].prizeList[index - 1]),
                            );
                            setData(formData);
                          }}
                        >
                          <i className={`iconfont icon-iconjiantou-35`} />
                        </Button>
                      )}
                      {formData.prizeBizList[1].prizeList.length > 0 && index < formData.prizeBizList[1].prizeList.length - 1 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            formData.prizeBizList[1].prizeList.splice(
                              index,
                              1,
                              ...formData.prizeBizList[1].prizeList.splice(index + 1, 1, formData.prizeBizList[1].prizeList[index]),
                            );
                            setData(formData);
                          }}
                        >
                          <i className={`iconfont icon-iconjiantou-34`} />
                        </Button>
                      )}
                    </FormItem>
                  )}
                />
              )}
            </Table>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize formData={formData} editValue={editValue} onChange={onPrizeChange} onCancel={onCancel} />
      </LzDialog>
    </div>
  );
};
