import React, { useEffect, useState } from 'react';
import { <PERSON>oon, Button, Dialog, Field, Form, Input, Message, NumberPicker, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataAddDrawChanceList, dataAddDrawChance } from '@/api/v99206';
import Utils, { deepCopy, getParams } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const fieldAddDraw = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [addDrawChanceVisible, setAddDrawChanceVisible] = useState(false);
  const [drawChance, setDrawChance] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    setPage({});
    dataAddDrawChanceList(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  // 增加抽奖机会弹框确定
  const addDrawChanceOpen = () => {
    fieldAddDraw.validate((errors, values) => {
      if (!errors) {
        const addDrawInfo = fieldAddDraw.getValues();
        addDrawInfo.activityId = getParams('id');
        setLoading(true);
        dataAddDrawChance(addDrawInfo)
          .then(() => {
            Message.success('添加成功');
            setAddDrawChanceVisible(false);
            const formValue: any = field.getValues();
            loadData({ ...formValue, ...defaultPage });
          })
          .catch((e) => {
            Message.error(e.message);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    });
  };
  // 增加抽奖机会弹框取消
  const addDrawChanceClose = () => {
    setAddDrawChanceVisible(false);
  };

  const checkDrawChance = (rule, value, callback) => {
    if (!value) {
      callback('请输入抽奖次数');
    } else if (value <= 0) {
      callback('抽奖次数必须大于0');
    } else {
      callback();
    }
  };

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button
          type="primary"
          onClick={() => {
            setAddDrawChanceVisible(true);
          }}
          style={{ marginLeft: '10px' }}
        >
          添加抽奖机会
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column
          title="用户昵称"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户pin"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.pin ? Utils.mask(row.pin) : '-'}
                  {row.pin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.pin).then(() => {
                          Message.success('用户pin已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="添加时间"
          dataIndex="winningTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.addTime)}</div>}
        />
        <Table.Column title="订单号" dataIndex="orderNo" />
        <Table.Column title="增加抽奖机会" dataIndex="drawChance" />
        <Table.Column
          title="原因"
          cell={(value, index, data) => (
            <div>
              <Balloon
                v2
                triggerType="hover"
                align={'t'}
                trigger={
                  <div style={{ width: 120, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                    {data.reason}
                  </div>
                }
                closable={false}
              >
                {data.reason}
              </Balloon>
            </div>
          )}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />

      <Dialog
        v2
        title="增加抽奖机会"
        visible={addDrawChanceVisible}
        autoFocus
        onClose={addDrawChanceClose}
        footer={false}
      >
        <Form field={fieldAddDraw} colon labelAlign={'top'}>
          <Form.Item name="sort" label="奖池id" required requiredMessage="请输入奖池id">
            <NumberPicker style={{ width: '100%' }} placeholder="奖池id:1、2、3" />
          </Form.Item>
          <Form.Item name="orderNo" label="订单号" required requiredMessage="请输入订单号">
            <NumberPicker style={{ width: '100%' }} placeholder="订单号" />
          </Form.Item>
          <Form.Item name="encryptPin" label="用户pin" required requiredMessage="请输入用户pin">
            <Input name="encryptPin" placeholder="用户pin" trim />
          </Form.Item>
          <Form.Item
            name="drawChance"
            label="赠送次数"
            required
            requiredMessage="请输入抽奖次数"
            validator={checkDrawChance}
            maxLength={4}
          >
            <Input
              name="drawChance"
              placeholder="赠送次数"
              value={drawChance}
              onChange={(val: any) => {
                const _val = val.replace(/[^\d]/g, '');
                if (Number(_val) > 10) {
                  Message.warning('赠送次数不能超过10次');
                  setDrawChance('10');
                } else {
                  setDrawChance(_val);
                }
              }}
            />
          </Form.Item>
          <Form.Item name="reason" label="原因" required requiredMessage="请输入原因">
            <Input.TextArea trim name="reason" placeholder="原因" />
          </Form.Item>
        </Form>
        <div style={{ float: 'right' }}>
          <Button type="primary" disabled={loading} onClick={addDrawChanceOpen}>
            确认
          </Button>
          <Button onClick={addDrawChanceClose} style={{ marginLeft: '10px' }}>
            取消
          </Button>
        </div>
      </Dialog>
    </div>
  );
};
