import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import format from '../../../../utils/format';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

export interface CustomValue {
  kv: string;
  gitft1: string;
  gitft2: string;
  step: string;
  ruleBk: string;
  lockBtn: string;
  hadLockBtn: string;
  lockFalseBtn: string;
  lockFinishBtn: string;
  lockNoStart: string;
  lockHadEnd: string;
  skuBK: string;
  goBuyBtn: string;
  goShopBtn: string;
  successBk: string;
  thresholdBg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  type: number;
  peopleNum: number;
  rank: string;
}

export interface PageData {
  activityName: string; // 活动名称
  shopName: string; // 店铺名称
  rangeDate: string[]; // 日期区间（不提交）
  startTime: string; // 活动开始时间
  endTime: string; // 活动结束时间
  threshold: number; // 活动门槛（不提交）
  supportLevels: string; // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
  limitJoinTimeType: number; // 限制入会时间 0：不限制；1：入会时间
  joinTimeRange: any[]; // 入会时间范围
  joinStartTime: string; // 入会开始时间
  joinEndTime: string; // 入会结束时间
  lockStartTime: string; // 锁权开始时间
  lockEndTime: string; // 锁权结束时间
  orderStartTime: string; // 订单开始时间
  orderEndTime: string; // 订单结束时间
  distributeStartTime: string; // 发奖开始时间
  distributeEndTime: string; // 发奖结束时间
  lockAmount: number; // 锁权金额
  receiveType: number; // 0同类型不限制1同类型限制领取一次
  sixPrizeOpen: boolean; // 6罐奖品开关
  twPrizeOpen: boolean; // 12罐奖品开关
  sixPrizeImg: string; // 6罐奖品背景图
  twPrizeImg: string; // 12罐奖品背景图
  sixPrizeList: any[]; // 6罐礼品
  twPrizeList: any[]; // 12罐礼品
  giftSkuList: any[]; // 活动商品
  endActivity: number; // 奖品发完结束活动 0-否 1-是
  shareStatus: number; // 是否开启分享
  shareTitle: string; // 分享标题
  cmdImg: string; // 分享图片-cmd
  h5Img: string; // 分享图片-h5
  mpImg: string; // 分享图片-mp
  rules: string; // 活动规则
  templateCode: string; // 模板代码
  gradeLabel: string[]; // 等级标签
  crowdBag: any; // 人群包
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '令牌',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  kv: '//img10.360buyimg.com/imgzone/jfs/t1/279108/13/27982/340725/6811c63bF4cbc8700/74a62ce10451f222.png',
  gitft1: '//img10.360buyimg.com/imgzone/jfs/t1/296306/12/1294/289768/6819d0ffF3e41bc19/7b8303c7bd1b7218.png',
  gitft2: '//img10.360buyimg.com/imgzone/jfs/t1/294999/10/3364/60933/6819d100F4abb15ac/92fe0f4a10e05cfc.png',
  step: '//img10.360buyimg.com/imgzone/jfs/t1/273224/34/29493/72701/6811c63dF136a025d/d2adc91a40d0edc8.png',
  ruleBk: '//img10.360buyimg.com/imgzone/jfs/t1/283064/23/16251/28756/67f36515Fca4a144e/4b426c36a3cc3dfa.png',
  lockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/277640/23/13451/19645/67ea1002F11fe1267/6837f249ec351f99.png',
  hadLockBtn: '//img10.360buyimg.com/imgzone/jfs/t1/209580/23/42412/14162/665482f0Fb93bd43a/37e816c4c4c38a03.png',
  lockFalseBtn: '//img10.360buyimg.com/imgzone/jfs/t1/231894/31/19337/19165/66558f31F556f7d58/eb46b9c4c0a4838e.png',
  lockFinishBtn: '//img10.360buyimg.com/imgzone/jfs/t1/193910/10/46280/14780/66546f57Febb8f4ca/4bb2d66df814ca2c.png',
  lockNoStart: '//img10.360buyimg.com/imgzone/jfs/t1/186507/24/46943/10695/665d854eF1c92b82a/94f5a3c077ac2e49.png',
  lockHadEnd: '//img10.360buyimg.com/imgzone/jfs/t1/227316/34/19792/10483/665d80beF4aaaefaf/af4c2191c1bbc78c.png',
  skuBK: '//img10.360buyimg.com/imgzone/jfs/t1/271687/28/28088/20617/6811c63dF383661cd/c1428c00bacba9f4.png',
  goBuyBtn: '//img10.360buyimg.com/imgzone/jfs/t1/237758/25/16559/4662/6655b69aF93978327/6b8a66110fbf4ddc.png',
  goShopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/272155/19/12641/23461/67ea1361F1b127826/20dcca90e516a532.png',
  successBk: '//img10.360buyimg.com/imgzone/jfs/t1/171777/30/46572/23666/665e82b1Fa303832a/b6386eee122fb6c0.png',
  thresholdBg: '//img10.360buyimg.com/imgzone/jfs/t1/204029/35/42959/26694/6655a9aaF08efa147/48ee85ea92f9064d.png',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};
// 活动设置默认数据

// 活动设置默认数据
export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  type: 0, // 0门槛奖品 1排行榜奖品
  peopleNum: 1,
  rank: '', // 获奖名次 默认0
};

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `1元锁权-${dayjs().format('YYYY-MM-DD')}`,
    // 店铺名称
    shopName: getShop().shopName,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',

    lockAmount: 1, // 锁权金额
    lockStartTime: dayjs().add(0, 'day').format('YYYY-MM-DD 00:00:00'), // 锁权开始时间
    lockEndTime: '', // 锁权结束时间
    orderStartTime: '', // 下单开始时间
    orderEndTime: '', // 下单结束时间
    distributeStartTime: '', // 发奖开始时间
    distributeEndTime: dayjs().add(30, 'day').format('YYYY-MM-DD 23:59:59'), // 发奖结束时间
    receiveType: 0, // 0同类型不限制1同类型限制领取一次
    // 6罐礼品是否开启
    sixPrizeOpen: true,
    // 12罐礼品是否开启
    twPrizeOpen: true,
    // 6罐礼品图
    sixPrizeImg: '',
    // 12罐礼品图
    twPrizeImg: '',
    sixPrizeList: [], // 6罐礼品
    twPrizeList: [], // 12罐礼品
    giftSkuList: [], // 活动商品

    endActivity: 0, // 奖品发完结束活动 0-否 1-是

    // 是否开启分享
    shareStatus: 0,
    // 分享标题
    shareTitle: '会员首购领好礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (formData.giftSkuList.length === 0) {
    Message.error('请上传活动商品');
    return false;
  }
  if (formData.sixPrizeOpen && formData.sixPrizeList[0].prizeType === 0) {
    Message.error('请选择6罐礼品');
    return false;
  }
  if (formData.twPrizeOpen && formData.twPrizeList[0].prizeType === 0) {
    Message.error('请选择12罐礼品');
    return false;
  }
  if (formData.sixPrizeList.length && !arePrizesValid(formData.sixPrizeList, formData)) {
    // 奖品时间与活动时间冲突
    return false;
  }
  // 奖品时间与活动时间冲突
  if (formData.twPrizeList.length && !arePrizesValid(formData.twPrizeList, formData)) {
    return false;
  }
  if (dayjs(formData.lockStartTime).add(1, 'day').valueOf() >= dayjs(formData.lockEndTime).valueOf()) {
    Message.error('锁权时间需大于1天');
    return false;
  }
  if (dayjs(formData.orderStartTime).add(1, 'day').valueOf() >= dayjs(formData.orderEndTime).valueOf()) {
    Message.error('下单时间需大于1天');
    return false;
  }
  if (dayjs(formData.distributeStartTime).add(1, 'day').valueOf() >= dayjs(formData.distributeEndTime).valueOf()) {
    Message.error('发奖时间需大于1天');
    return false;
  }
  return true;
};
