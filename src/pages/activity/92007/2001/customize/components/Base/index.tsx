import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Radio } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import LzColorPicker from '@/components/LzColorPicker';

const RadioGroup = Radio.Group;
const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 6,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel title="活动主页">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="页面背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.kv}
                  onChange={(kv) => {
                    setForm({ kv });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度480/790/1334px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ kv: defaultValue?.kv });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="页面背景图"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={750} */}
          {/*         value={formData.pageBg} */}
          {/*         onChange={(pageBg) => { */}
          {/*           setForm({ pageBg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：宽度750px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ pageBg: defaultValue?.pageBg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="页面背景颜色">
            <LzColorPicker value={formData.actBgColor} onChange={(actBgColor) => setForm({ actBgColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ actBgColor: defaultValue?.actBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item> */}
          <Form.Item label="6罐图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={740}
                  height={450}
                  value={formData.gitft1}
                  onChange={(gitft1) => {
                    setForm({ gitft1 });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：740*450px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ gitft1: defaultValue?.gitft1 });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="12罐图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={740}
                  height={450}
                  value={formData.gitft2}
                  onChange={(gitft2) => {
                    setForm({ gitft2 });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：740*450px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ gitft2: defaultValue?.gitft2 });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="步骤图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={740}
                  height={410}
                  value={formData.step}
                  onChange={(step) => {
                    setForm({ step });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：740*410px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ step: defaultValue?.step });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="规则图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  height={355}
                  value={formData.ruleBk}
                  onChange={(ruleBk) => {
                    setForm({ ruleBk });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：750*355px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ ruleBk: defaultValue?.ruleBk });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
