/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle, useState } from 'react';
import { Form, Field, Radio, Table, Button, Dialog, Message, Switch } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData, PRIZE_INFO } from '../../../util';
// import LzPrize from './LzPrize';
import { activityEditDisabled, deepCopy } from '@/utils';
import { PRIZE_TYPE } from '../../../util';
import ChoosePrize from '@/components/ChoosePrizeForDZ';
import LzDialog from '@/components/LzDialog';
import LzImageSelector from '@/components/LzImageSelector';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑的奖品列表key
  const [prizeListKey, setPrizeListKey] = useState('');
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    const sixPrizeList: any = formData.sixPrizeList;
    const twPrizeList: any = formData.twPrizeList;
    if (!formData.sixPrizeList.length) {
      for (let i = 0; i < 1; i++) {
        sixPrizeList.push(deepCopy(PRIZE_INFO));
      }
    }
    if (!formData.twPrizeList.length) {
      for (let i = 0; i < 1; i++) {
        twPrizeList.push(deepCopy(PRIZE_INFO));
      }
    }
    setFormData({ sixPrizeList, twPrizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => object | null } => ({
    submit: () => {
      console.log('prize=========');
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  const onPrizeChange = (data): boolean | void => {
    if (activityEditDisabled() && data.prizeName !== '谢谢参与') {
      if (data.sendTotalCount < defaultValue[prizeListKey][target].sendTotalCount) {
        Message.error(`发放份数不能小于${defaultValue[prizeListKey][target].sendTotalCount}份`);
        return false;
      }
    }
    // 更新指定index 奖品信息
    data.probability = 1;
    formData[prizeListKey][target] = data;
    if (data.prizeType === 11) {
      formData.giftSkuList = data.skuList;
    }
    setData(formData);
    setVisible(false);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="6罐奖品" required>
            <Switch
              disabled={activityEditDisabled() || !formData.twPrizeOpen}
              autoWidth
              defaultChecked={formData.sixPrizeOpen}
              onChange={(sixPrizeOpen) => {
                setData({
                  sixPrizeOpen,
                  sixPrizeList: sixPrizeOpen ? [deepCopy(PRIZE_INFO)] : [],
                });
              }}
            />
          </FormItem>
          {formData.sixPrizeOpen && (
            <>
              <FormItem label="6罐奖品设置" required>
                <Table dataSource={formData.sixPrizeList}>
                  <Table.Column
                    title="奖品名称"
                    dataIndex="prizeName"
                    cell={(_, index, row) => <div>{row.prizeName}</div>}
                  />
                  <Table.Column
                    title="奖品类型"
                    cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                    dataIndex="prizeType"
                  />
                  <Table.Column
                    title="单位数量"
                    cell={(_, index, row) => {
                      if (row.prizeType === 1) {
                        return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                      } else {
                        return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                      }
                    }}
                  />
                  <Table.Column
                    title="单份价值(元)"
                    cell={(_, index, row) => (
                      <div>
                        {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                          ? row.unitPrice
                          : row.unitPrice
                          ? Number(row.unitPrice).toFixed(2)
                          : ''}
                      </div>
                    )}
                  />
                  <Table.Column
                    title="发放份数"
                    cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                  />
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem style={{ marginBottom: '0' }}>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            let row = formData.sixPrizeList[index];
                            if (!row.prizeName) {
                              row = null;
                            }
                            setEditValue(row || null);
                            setTarget(index);
                            setPrizeListKey('sixPrizeList');
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                      </FormItem>
                    )}
                  />
                </Table>
              </FormItem>
            </>
          )}

          <FormItem label="12罐奖品" required>
            <Switch
              autoWidth
              defaultChecked={formData.twPrizeOpen}
              disabled={activityEditDisabled() || !formData.sixPrizeOpen}
              onChange={(twPrizeOpen) => {
                setData({
                  twPrizeOpen,
                  twPrizeList: twPrizeOpen ? [deepCopy(PRIZE_INFO)] : [],
                });
              }}
            />
          </FormItem>
          {formData.twPrizeOpen && (
            <>
              <FormItem label="12罐奖品设置" required>
                <Table dataSource={formData.twPrizeList}>
                  <Table.Column
                    title="奖品名称"
                    dataIndex="prizeName"
                    cell={(_, index, row) => <div>{row.prizeName}</div>}
                  />
                  <Table.Column
                    title="奖品类型"
                    cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                    dataIndex="prizeType"
                  />
                  <Table.Column
                    title="单位数量"
                    cell={(_, index, row) => {
                      if (row.prizeType === 1) {
                        return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                      } else {
                        return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                      }
                    }}
                  />
                  <Table.Column
                    title="单份价值(元)"
                    cell={(_, index, row) => (
                      <div>
                        {PRIZE_TYPE[row.prizeType] && row.unitPrice === 0
                          ? row.unitPrice
                          : row.unitPrice
                          ? Number(row.unitPrice).toFixed(2)
                          : ''}
                      </div>
                    )}
                  />
                  <Table.Column
                    title="发放份数"
                    cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                  />
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem style={{ marginBottom: '0' }}>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            let row = formData.twPrizeList[index];
                            if (!row.prizeName) {
                              row = null;
                            }
                            setEditValue(row || null);
                            setTarget(index);
                            setPrizeListKey('twPrizeList');
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                      </FormItem>
                    )}
                  />
                </Table>
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          hasLimit={false}
          hasProbability={false}
          typeList={[3]}
          defaultTarget={3}
          onChange={onPrizeChange}
          onCancel={onCancel}
          defaultEditValue={defaultValue[prizeListKey || 'sixPrizeList'][target] as any}
          width={500}
          height={500}
          prizeNameLength={25}
        />
      </LzDialog>
    </div>
  );
};
