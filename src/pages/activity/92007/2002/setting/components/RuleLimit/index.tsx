import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, DatePicker2, Field, Message, NumberPicker } from '@alifd/next';
import constant from '@/utils/constant';
import { activityEditDisabled, validateActivityThreshold } from '@/utils';
import { FormLayout, PageData } from '../../../util';

const FormItem = Form.Item;

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));
  return (
    <div>
      <LzPanel title="历史订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="校验历史订单天数" name="beforeDay" required requiredMessage="请输入校验历史订单天数">
            <NumberPicker
              onChange={(beforeDay) => setData({ beforeDay })}
              type="inline"
              max={9999999}
              min={1}
              value={formData.beforeDay}
            />{' '}
            天
          </FormItem>
          <FormItem label="生日月前订单笔数" name="beforeOrderNum" required requiredMessage="请输入生日月前订单笔数">
            <NumberPicker
              onChange={(beforeOrderNum) => setData({ beforeOrderNum })}
              type="inline"
              max={9999999}
              min={1}
              value={formData.beforeOrderNum}
            />{' '}
            笔
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
