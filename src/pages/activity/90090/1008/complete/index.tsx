/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 14:40
 * Description: 活动完成
 */
import React from 'react';
import styles from './index.module.scss';
import QRCode from 'qrcode.react';
import { config } from 'ice';
import { Button, Message } from '@alifd/next';
import Utils, { getActivityParams } from '@/utils';
import { getShop } from '@/utils/shopUtil';

const size = 148;
const imageSettings: {
  src: string;
  height: number;
  width: number;
  excavate: boolean;
} = {
  src: require('@/assets/images/jd-logo.webp'),
  height: size / 7.4,
  width: size / 7.4,
  excavate: false,
};
const { shopId } = getShop();
export default (props) => {
  const { history, activityId } = props;
  // 获取活动类型 & 模板ID
  const [activityType, code] = getActivityParams();
  return (
    <div className={styles.complete}>
      <div className={styles.content}>
        <div className={styles.qrcode}>
          <QRCode
            size={size}
            value={`${config.previewUrl}/${activityType}/${code}/?shopId=${shopId}&activityMainId=${activityId}`}
            imageSettings={imageSettings}
          />
          <span>京东APP扫码查看活动</span>
        </div>
        <div className={styles.tip}>
          <div>
            <img
              alt=""
              src="//img10.360buyimg.com/imgzone/jfs/t1/248164/40/15854/27533/66a84f62F959cdbd6/9e8977d409f836c6.png"
            />
            <span>活动设置完成了！</span>
          </div>
          <Button
            onClick={async () => {
              await Utils.copyText(
                `${config.previewUrl}/${activityType}/${code}/?shopId=${shopId}&activityMainId=${activityId}`,
              );
              Message.success('链接已经复制到剪切板');
            }}
            type="primary"
          >
            复制活动链接
          </Button>
        </div>
      </div>
      <div className={styles.warning}>
        <div>活动创建完成后可进行以下操作：</div>
        <div>
          活动预览：京东APP扫描上方二维码，查看活动实际效果，如要修改，可进入活动管理页面修改活动
          <span className={styles.link} onClick={() => history.push('/activity')}>
            去管理活动 &gt;
          </span>
        </div>
        <div>流量预估：活动在投放前要预估活动流量，以免活动流量过高导致异常，预估活动流量后应提前48小时报备客服</div>
      </div>
    </div>
  );
};
