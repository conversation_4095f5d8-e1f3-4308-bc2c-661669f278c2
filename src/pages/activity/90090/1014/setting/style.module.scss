.setting {
  margin-top: 15px;
}

.panel {
  box-sizing: border-box;
  background: #f5f7f9;
  padding: 15px;
  border: 1px solid #d7dde4;
  min-width: 350px;
}

.number {
  margin: 0 10px;
}

.tip {
  font-size: 12px;
  color: gray;
  margin-top: 15px;
}

.tips {
  font-size: 12px;
}

.orderTypes {
  display: inline-block;
  margin-left: 10px;

  .order {
    position: relative;
    display: inline-block;
    width: 55px;
    height: 20px;
    border-radius: 10px;
    line-height: 20px;
    text-align: center;
    background-color: #c0e8fe;
    color: #18A7F7;
    margin-right: 10px;

    &:not(:last-child)::after {
      content: '';
      display: inline-block;
      height: 1px;
      width: 10px;
      background-color: #18A7F7;
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .orderGray {
    background-color: #f0f0f0;
    color: #999999;

    &:not(:last-child)::after {
      content: '';
      display: inline-block;
      height: 1px;
      width: 10px;
      background-color: #f0f0f0;
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  margin-top: 15px;

  .skuContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;
    position: relative;

    &:hover {
      .del {
        display: block;
      }
    }

    .skuImg {
      width: 60px;
      height: 60px;
    }

    .skuName {
      max-width: 120px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}

.del {
  position: absolute;
  right: -5px;
  top: -5px;
  line-height: 16px;
  color: #9CA7B6;
  display: none;
  cursor: pointer;
}

