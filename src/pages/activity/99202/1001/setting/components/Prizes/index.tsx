/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message, NumberPicker } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from './ChoosePrize';
import { activityEditDisabled, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 追加弹窗
  const [zjVisible, setZjVisible] = useState(false);
  //追加奖品数量
  const [addPrizeNum, setAddPrizeNum] = useState(1);
  //追加后奖品总数量
  const [addAfterPrizeNum, setAddAfterPrizeNum] = useState(0); 
  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.prizeList[target] = data;
    // 计算总概率
    formData.totalProbability = formData.prizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+formData.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      formData.prizeList.splice(target, 1, PRIZE_INFO);
      return false;
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.prizeList];
    // 设置一个默认奖品
    // if (!prizeListLength) {
    //   list.push(PRIZE_INFO);
    // }
    // 如果奖品列表为空 说明：奖品列表已经够1 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够1 使用补齐后的list
    setData({ prizeList: list.length ? list : formData.prizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };
  // 追加奖品数量变化
  const addPrizeNumChange = (value: number): void => {
    setAddPrizeNum(value);
    setAddAfterPrizeNum(formData.prizeList[target].sendTotalCount + value);
  }; 
  const savePrizeNum = (): void => {
    const prizeList = [...formData.prizeList];
    prizeList[target].sendTotalCount = addAfterPrizeNum;
    setData({ prizeList });
    setZjVisible(false);
    setTimeout(() => {
      setAddPrizeNum(1);
    }, 1000);
  };
  const cancelZjVisible = (): void => {
    setAddPrizeNum(1);
    setZjVisible(false);
  };
  return (
    <div>
      <LzPanel title="奖励设置">
        <Form {...formItemLayout} field={field}>
          <FormItem
            label="奖励列表"
            required
          // extra={
          //   <div className="next-form-item-help">
          //     提示：用户订单满足上述条件后获得领奖资格，领取奖励后，待订单完成，系统将自动发放奖励；领取上限为1份/人
          //   </div>
          // }
          >
            <Button
              style={{ marginBottom: '20px' }}
              disabled={formData.prizeList.length >= 1}
              type="primary"
              onClick={() => {
                if (formData.prizeList.length >= 1) {
                  Message.error('只能添加1个奖品!')
                  return;
                }
                setVisible(true);
              }}
            >
              +添加奖励({formData.prizeList.length}/1)
            </Button>
            <Table dataSource={formData.prizeList}>
              {/* <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} /> */}
              <Table.Column title="奖品名称" dataIndex="prizeName" />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else if (row.prizeType === 4) {
                    return <div>{row.unitCount ? `${row.unitCount}积分` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              {/* <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
              /> */}
              {/* <Table.Column */}
              {/*   title="中奖概率(%)" */}
              {/*   cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>} */}
              {/* /> */}
              {/* <Table.Column */}
              {/*   title="每日发放限额" */}
              {/*   cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>} */}
              {/* /> */}
              <Table.Column
                title="奖品图片"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              {!activityEditDisabled() && (
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <FormItem style={{ marginBottom: '0' }} disabled={isDisableSetPrize(formData.prizeList, index)}>
                      <Button
                        text
                        type="primary"
                        disabled
                        onClick={() => {
                          let row = formData.prizeList[index];
                          setEditValue(row);
                          setTarget(index);
                          setZjVisible(true);
                          setAddAfterPrizeNum(formData.prizeList[index].sendTotalCount + 1);
                        }}
                      >
                        追加
                      </Button>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          let row = formData.prizeList[index];
                          setEditValue(row);
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          if (_.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认清空该奖品？',
                              onOk: () => {
                                formData.prizeList.splice(index, 1);
                                formData.totalProbability = formData.prizeList
                                  .reduce((v, total) => {
                                    return v + Number(total.probability);
                                  }, 0);
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }
                        }}
                      >
                        删除
                      </Button>
                    </FormItem>
                  )}
                />
              )}
              {activityEditDisabled() && (
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <FormItem style={{ marginBottom: '0' }} disabled={isDisableSetPrize(formData.prizeList, index)}>
                      <Button
                        text
                        type="primary"
                        disabled={formData.prizeList[index].prizeType == 1}
                        onClick={() => {
                          let row = formData.prizeList[index];
                          setEditValue(row);
                          setTarget(index);
                          setZjVisible(true);
                          setAddAfterPrizeNum(formData.prizeList[index].sendTotalCount + 1);
                        }}
                      >
                        追加
                      </Button>
                    </FormItem>
                  )}
                />
              )}
            </Table>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasLimit={false}
          defaultTarget={1}
          typeList = {[1, 3, 4, 901, 902]}
          hasProbability={false}
        />
      </LzDialog>
      <LzDialog
        title={'追加奖品'}
        visible={zjVisible}
        footer={[
          <Button key="cancel" onClick={() => cancelZjVisible()}>
            取消
          </Button>,
          <Button key="confirm" type="primary" style={{ marginRight: '10px' }} onClick={() => savePrizeNum()}>
            保存
          </Button>,
        ]}
        onClose={() => cancelZjVisible()}
        style={{ width: '670px' }}
      >
        <Form {...formItemLayout} field={field}>
          <FormItem
            style={{ lineHeight: '28px' }}
            label="奖品名称:">
            {editValue?.prizeName}
          </FormItem>
          <FormItem
            label="本次追加数量:"
            required
            extra={
            <div className="next-form-item-help"
            style={{ color: '#ec0909' }}
            >
              最大可追加数量2000
            </div>
          }>
            <NumberPicker
              value={addPrizeNum}
              onChange={addPrizeNumChange}
              type="inline"
              min={1}
              max={2000}
            />
          </FormItem>
          <FormItem
            label="追加后数量:"
            style={{ lineHeight: '28px' }}>
            {addAfterPrizeNum}份
          </FormItem>
        </Form>

      </LzDialog>
    </div>
  );
};
