/**
 * Author: <PERSON><PERSON><PERSON>e
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  potNum: string;
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export interface seriesSkuList {
  potNum: string;
  skuId: string;
}

export interface SeriesList {
  seriesName: string;
  seriesPic: string;
  seriesPrizeList: PrizeInfo[];
  seriesSkuList: seriesSkuList[];
  seriesUrl: string;
}

export interface CustomValue {
  pageBg?: string; // 主页背景图
  actBg?: string; // 主页背景色
  actBgColor?: string;
  shopNameColor?: string; // 店铺名称颜色
  btnColor?: string; // 按钮字体颜色
  btnBg?: string; // 按钮背景颜色
  btnBorderColor?: string; // 按钮边框颜色
  acIntroductionBg?: string; // 活动介绍
  seriesGoodsBg?: string; // 系列图
  seriesBoxBkHead?: string; // 系列盒子头部
  seriesBoxBkFooter?: string; // 系列盒子底部
  seriesBoxBkBody?: string; // 系列盒子中部
  buyBtnBg?: string; // 继续集罐
  exchangeBtn?: string; // 兑换按钮
  winnersBg?: string; // 帮光商品背景
  cmdImg?: string; // 命令图片
  h5Img?: string; // h5图片
  mpImg?: string; // 小程序图片
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  prizeDay: PrizeInfo[];
  seriesList: SeriesList[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
  orderFilterToggle: number;
  orderStatus: string;
  orderRangeDate: [];
  diffSeriesReceiveLimit: boolean;
  sameSeriesReceiveLimit: boolean;
  perReceiveLimit: boolean;
  perReceiveCount: number;
  receiveTimeType: number;
  isExposure: number;
  exposureSkuList: any[];
  receivePointRangeDate: [Dayjs, Dayjs];
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '',
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/234187/39/15741/85325/661c8222F596096d5/32cad7ce52d43ee1.png', // 主页背景图
  actBgColor: '#f6e1ae', // 主页背景色
  shopNameColor: '#72421f', // 店铺名称颜色
  btnColor: '#72421f', // 按钮字体颜色
  btnBg: '#ffefc5', // 按钮背景颜色
  btnBorderColor: '#dc9d57', // 按钮边框颜色
  acIntroductionBg:
    'https://img10.360buyimg.com/imgzone/jfs/t1/236485/38/15539/84444/661c8224Fc86cd536/29db49844c004578.png', // 活动背景
  seriesGoodsBg:
    'https://img10.360buyimg.com/imgzone/jfs/t1/149666/32/42717/46182/661c8223F74f6ee23/e0c2158174fdc23a.png', // 系列图
  seriesBoxBkHead: '//img10.360buyimg.com/imgzone/jfs/t1/138612/33/42854/19737/6615f6faF0ecdb368/e647914f6b922868.png',
  seriesBoxBkFooter: '//img10.360buyimg.com/imgzone/jfs/t1/225791/24/16092/2271/6615f6f8F1edd7b8a/ab8c21c4adce2dc7.png',
  seriesBoxBkBody: '//img10.360buyimg.com/imgzone/jfs/t1/204141/28/41500/76465/6615f6f9Fb2cc96f1/35939b2ce3a6e6d9.png',
  buyBtnBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/183774/23/42582/7645/6615114aF3215b691/ead252530ccc7427.png', // 继续集罐按钮
  exchangeBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/191145/33/40745/4516/6615114aFe6ad325a/72e3159873772eef.png', // 兑换按钮
  winnersBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/198727/40/18336/5096/619b80b9Eafb4de86/6e8012f498dd80d1.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/236504/22/12170/148366/661c8c4dFc43a9fec/bf03de321da78916.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  const now = (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00');
  const after30 = (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59');
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `集罐赢好礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [now, after30],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(now),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(after30),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 每日签到奖品
    prizeDay: [],
    // 系列及奖品列表
    seriesList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '集罐赢好礼，超多惊喜大奖等你来领！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    orderFilterToggle: 1,
    orderRangeDate: [],
    orderStatus: 'FINISH',
    diffSeriesReceiveLimit: false,
    sameSeriesReceiveLimit: false,
    perReceiveLimit: false,
    perReceiveCount: 0,
    receiveTimeType: 0,
    receivePointRangeDate: [now, after30],
    // 是否开启曝光
    isExposure: 1,
    // 商品列表
    exposureSkuList: [],
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  potNum: '',
  ifPlan: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

// 领奖开始时间校验
const checkAwardStartTime = (awardStartTime, formData) => {
  // const isBefore: boolean = dayjs(awardStartTime).isBefore(formData.startTime);
  if (dayjs(awardStartTime).valueOf() < dayjs(formData.rangeDate[0].valueOf()).valueOf()) {
    Message.error('领奖开始时间需大于等于活动开始时间');
    return false;
  }
  return true;
};
// 领奖结束时间校验After
const checkAwardEndTime = (awardEndTime, formData) => {
  console.log('领奖结束时间', dayjs(awardEndTime).valueOf());
  console.log('活动结束时间', dayjs(formData.endTime).valueOf());
  console.log('活动结束时间数组', dayjs(formData.rangeDate[1]).valueOf());

  // const isAfter: boolean = dayjs(awardEndTime).isBefore(awardEndTime);
  if (dayjs(awardEndTime).valueOf() > dayjs(formData.rangeDate[1].valueOf()).valueOf()) {
    Message.error('领奖结束时间需小于等于活动结束时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (formData: PageData): boolean => {
  for (let i = 0; i < formData.seriesList.length; i++) {
    const { seriesPrizeList } = formData.seriesList[i];
    if (!seriesPrizeList.length) {
      Message.error(`请填写${formData.seriesList[i].seriesName}下的奖品信息`);
      return false;
    }
    for (let j = 0; j < seriesPrizeList.length; j++) {
      if (!seriesPrizeList[j].prizeName) {
        Message.error(`请填写${formData.seriesList[i].seriesName}下的奖品信息`);
        return false;
      }
      if (!isPrizeValid(seriesPrizeList[j], formData)) {
        return false;
      }
    }
  }
  return true;
};

const hasPrizeDay = (formData: PageData): boolean => {
  for (const item of formData.prizeDay) {
    if (!item.prizeName) {
      Message.error('请设置每日签到奖励');
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  // if (!formData.prizeList.length) {
  //   Message.error('请设置奖品');
  //   return false;
  // }
  // for (const index in formData.prizeList) {
  //   console.log(index);
  //   const item = formData.prizeList[index];
  //   if (!item.prizeName) {
  //     Message.error('请设置奖品');
  //     return false;
  //   }
  // }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择每日签到奖品
  if (!hasPrizeDay(formData)) {
    return false;
  }
  // 没有选择奖品累计/连续奖品设置
  if (!hasPrize(formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 未上传系列信息
  if (!formData.seriesList.length) {
    Message.error('请上传系列数据');
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(formData)) {
    return false;
  }
  // 领奖开始时间异常
  if (!checkAwardStartTime(formData.receivePointRangeDate[0], formData)) {
    return false;
  }
  // 领奖结束时间异常
  if (!checkAwardEndTime(formData.receivePointRangeDate[1], formData)) {
    return false;
  }
  return true;
};
