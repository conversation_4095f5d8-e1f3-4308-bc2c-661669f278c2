import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  signDay: string;
  orderPrice?: number;
}

export interface CustomValue {
  actBg: string; // 主页背景色
  cmdImg: string; // 命令图片
  h5Img: string; // h5图片
  mpImg: string; // 小程序图片
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  prizeList: PrizeInfo[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];

  autoStop: boolean;
  memberApplyLimit: number;
  baseCondition: number;
  joiningTime: number;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/234201/38/21429/122901/66a06cbaF9f6f3f95/0a033ee8d01dc447.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/223251/20/37551/187602/66a06d72Faa3b6880/ad146ff545be61a1.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/233009/27/20980/139107/66a06d7dFe817099e/5701b5a373f2d2b0.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/228822/13/23123/21007/66a06d61F20110d30/77a7fa8f6cfd9d42.png',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `${dayjs().month() + 1}月会员试用`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 活动结束生成人群包
    // crowdPackage: 1,
    threshold: 0,
    prizeList: [],
    // 是否开启分享
    shareStatus: 1, // 1开启 0关闭
    // 分享标题
    shareTitle: '会员试用，有机会获得大奖哦！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],

    autoStop: false,
    memberApplyLimit: 1, // 1活动期间可申请一次
    baseCondition: 1, // 1不限制 2按会员等级 3按入会时间
    joiningTime: 1, // 1活动期间入会 2活动以前入会
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  signDay: '',
  ifPlan: 0,
  // 获奖消费额度
  orderPrice: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.baseCondition === 2) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }

  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  if (!formData.prizeList.length) {
    Message.error('请设置试用商品');
    return false;
  }
  for (const index in formData.prizeList) {
    console.log(index);
    const item = formData.prizeList[index];
    if (!item.prizeName) {
      Message.error('请设置试用商品');
      return false;
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择奖品累计/连续奖品设置
  if (!hasPrize(formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }

  return true;
};
