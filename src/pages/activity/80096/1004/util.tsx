/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import format from '@/utils/format';
import { getParams } from '@/utils';
import { Message } from '@alifd/next';
import Preview from '@/components/LzCrowdBag/preview';
import CONST from '@/utils/constant';
import { getShop } from '@/utils/shopUtil';

const { shopName } = JSON.parse(localStorage.getItem(CONST.LZ_CURRENT_SHOP) as string);

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  disableShopName: number;
  // 文字颜色
  shopNameColor: string;
}
export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number | string;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  stepAmount: number;
  key?: string;
}
export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  isExposure: number;
  skuList: any[];
  orderSkuList: any[];
  orderRangeData: [Dayjs, Dayjs];
  isAllOrderSku: number;
  customerLimit: number;
  priceType: number;
  split: number;
  receiveLimit: number;
  customerLimitDay: string | number;
  prizeList: PrizeInfo[];
  crowdBag: any;
  gradeLabel: any;
  shopName: string;
}

interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  shopNameColor: '',
  disableShopName: 0,
  // 活动主图
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/225018/23/11405/211120/6593bcc1Fad77d809/a99b4c840f215f50.png',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '#cd3a2c',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/229813/25/10578/76959/6593bcc6F530ebe60/5087fb2b6c43ec0f.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/232663/18/9044/12308/6593bcc7F67021cf8/146508ec836842d0.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/230477/32/10477/88198/6593bcc6F56ee1368/eb50a9cbe792fc66.png',
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: '',
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  // 金额
  stepAmount: 0,
};
// 活动设置默认数据
export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 基本信息
    // 活动名称
    activityName: `下单阶梯有礼-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 分享设置
    // 分享标题
    shareTitle: '下单即有机会赢取好礼，快来看看吧！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 是否曝光商品 0否 1是
    isExposure: 0,
    // 曝光商品
    skuList: [],
    // 阶梯奖励
    prizeList: [PRIZE_INFO, PRIZE_INFO],
    // 价格类型
    priceType: 0,
    // 拆单 1是 2否
    split: 1,
    // 下单时间
    orderRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 是否全部商品 1 全部商品 0 指定商品
    isAllOrderSku: 1,
    // 新老客限制 0不限制 1新客 2老客
    customerLimit: 0,
    // 限制天数
    customerLimitDay: 1,
    // 领取限制 1 单次领取 2 多次领取
    receiveLimit: 2,
    // 订单商品
    orderSkuList: [],
    // 活动规则
    rules: '',
    templateCode: '',
    crowdBag: null,
    gradeLabel: [],
    shopName,
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};
// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `品牌会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};

// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit' && getParams('status') === '2';
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.rangeDate[0]).isAfter(dayjs(start));
    if (!isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}有效期开始时间应小于活动开始时间`);
      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.rangeDate[1]).isBefore(dayjs(end));
    if (!isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}有效期结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};
const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (!prizeList.filter((e) => e.prizeImg).length) {
    Message.error('请设置阶梯奖励');
    return false;
  }
  return true;
};
const checkStepAmount = (prizeList: PrizeInfo[]): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!prizeList[i].stepAmount) {
      Message.error(`请设置阶梯 ${i + 1} 级的累计金额！`);
      return false;
    }
    if (prizeList[i + 1] && prizeList[i + 1].stepAmount <= prizeList[i].stepAmount) {
      Message.error(`阶梯 ${i + 2} 级的累计金额应大于阶梯 ${i + 1} 级的累计金额！`);
      return false;
    }
  }
  return true;
};

const hasExposeSku = (formData: PageData): boolean => {
  if (!formData.skuList.length) {
    Message.error('请设置曝光商品');
    return false;
  }
  return true;
};

const hasOrderSku = (formData: PageData): boolean => {
  if (!formData.orderSkuList.length) {
    Message.error('请设置订单商品');
    return false;
  }
  return true;
};

const hasLimitDay = (formData: PageData): boolean => {
  if (!formData.customerLimitDay) {
    Message.error('请设置新老客限制天数');
    return false;
  }
  return true;
};
const hasUnCompletePrize = (prizeList: PrizeInfo[]): boolean => {
  const list = prizeList.filter((e) => !e.prizeType);
  if (list.length) {
    Message.error(`请完善阶梯奖励的奖品信息`);
    return false;
  }
  return true;
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: Dayjs, name: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error(`${name}开始时间应大于当天时间`);
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: Dayjs, endTime: Dayjs, name: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error(`${name}结束时间应大于${name}开始时间`);
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error(`${name}结束时间应大于当前时间`);
    return false;
  }
  return true;
};
const isOrderTime = (formData: PageData): boolean => {
  const activityStart = dayjs(formData.rangeDate[0]);
  const activityEnd = dayjs(formData.rangeDate[1]);
  const orderStart = dayjs(formData.orderRangeData[0]);
  const orderEnd = dayjs(formData.orderRangeData[1]);

  // Check if order start time is the same as or within the activity time range
  console.log();
  if (!orderStart.isSame(activityStart) && orderStart.isBefore(activityStart)) {
    Message.error('下单时间不得超出活动时间范围');
    return false;
  }

  // Check if order end time is the same as or within the activity time range
  if (!orderEnd.isSame(activityEnd) && orderEnd.isAfter(activityEnd)) {
    Message.error('下单时间不得超出活动时间范围');
    return false;
  }

  // Check if order end time is after or the same as order start time
  if (!orderEnd.isAfter(orderStart) || orderEnd.isSame(orderStart)) {
    Message.error('下单结束时间应大于下单开始时间');
    return false;
  }

  return true;
};

const hasOrderTime = (formData: PageData): boolean => {
  if (!formData.orderRangeData[0] || !formData.orderRangeData[1]) {
    Message.error(`请设置下单时间`);
    return false;
  }
  return true;
};
// export const checkActivityData = (formData: PageData): boolean => {
//   // 编辑直接通过，不走校验
//   if (isProcessingEditType()) {
//     return true;
//   }
//   // 开始时间异常
//   if (!isStartTimeValid(formData.rangeDate[0], '活动')) {
//     return false;
//   }
//   // 结束时间异常
//   if (!isEndTimeValid(formData.rangeDate[0], formData.rangeDate[1], '活动')) {
//     return false;
//   }
//   // 没有曝光商品
//   if (formData.isExposure && !hasExposeSku(formData)) {
//     return false;
//   }
//
//   if (!hasOrderTime(formData)) {
//     Message.error(`请设置下单时间`);
//     return false;
//   }
//   // 开始时间异常
//   if (!isStartTimeValid(dayjs(formData.orderRangeData[0]), '下单')) {
//     return false;
//   }
//   // 下单时间异常
//   if (!isOrderTime(formData)) {
//     return false;
//   }
//   // 没有订单商品
//   if (!formData.isAllOrderSku && !hasOrderSku(formData)) {
//     return false;
//   }
//
//   // 新老客限制
//   if (formData.customerLimit && !hasLimitDay(formData)) {
//     return false;
//   }
//   // 没有选择奖品
//   if (!hasPrize(formData.prizeList)) {
//     return false;
//   }
//   // 存在未填写详情的奖品
//   if (!hasUnCompletePrize(formData.prizeList)) {
//     return false;
//   }
//   // 奖品阶梯等级校验 stepAmount必须存在且递增！
//   if (!checkStepAmount(formData.prizeList)) {
//     return false;
//   }
//   // 奖品时间与活动时间冲突
//   if (!arePrizesValid(formData.prizeList, formData)) {
//     return false;
//   }
//   return true;
// };
export const checkActivityData = (formData: PageData): boolean => {
  if (isProcessingEditType()) {
    return true;
  }

  const validations = [
    // () => isStartTimeValid(formData.rangeDate[0], '活动'),
    () => isEndTimeValid(formData.rangeDate[0], formData.rangeDate[1], '活动'),
    () => !formData.isExposure || hasExposeSku(formData),
    () => hasOrderTime(formData),
    () => isStartTimeValid(dayjs(formData.orderRangeData[0]), '下单'),
    () => isOrderTime(formData),
    () => formData.isAllOrderSku || hasOrderSku(formData),
    () => !formData.customerLimit || hasLimitDay(formData),
    () => hasPrize(formData.prizeList),
    () => hasUnCompletePrize(formData.prizeList),
    () => checkStepAmount(formData.prizeList),
    () => arePrizesValid(formData.prizeList, formData),
  ];

  for (const isValid of validations) {
    if (!isValid()) {
      return false;
    }
  }

  return true;
};
