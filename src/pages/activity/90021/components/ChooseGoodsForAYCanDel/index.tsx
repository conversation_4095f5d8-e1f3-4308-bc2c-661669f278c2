import React, { useEffect, useImperativeHandle, useState } from 'react';
import { Button, Tab, Icon } from '@alifd/next';
import SkuSelector from './components/SkuSelector';
// import SkuExport from './components/SkuExport';
import SelectedProduct from './components/SelectedProduct';
import LzDialog from '@/components/LzDialog';
import SkuImport from '@/components/ChooseGoods/components/SkuImport';
import LineImport from '@/components/ChooseGoods/components/LineImport';
import { activityEditDisabled, deepCopy } from '@/utils';
import constant from '@/utils/constant';

const emptyFunction = () => {};

interface Props {
  defaultValue?: any[];
  value?: any[];
  disabled?: boolean;
  max?: number;
  onChange?: (data: any) => void;
  mode?: string;
  sRef?: React.Ref<{}>;
  seriesIndex?: number;
  seriesItemPrizeIndex?: number;
}

export default (props: Props) => {
  const {
    defaultValue,
    disabled,
    value,
    max = 0,
    onChange = emptyFunction,
    mode = 'multiple',
    sRef,
    seriesIndex,
    seriesItemPrizeIndex,
  } = props;
  const [visible, setVisible] = useState(false);
  const [skus, setSkus] = useState(deepCopy(defaultValue) || []);
  const [selectList, setSelectList] = useState([]);
  const [filterList, setFilterList] = useState([]);
  const [selectQuantity, setSelectQuantity] = useState(0);
  const [activeKey, setActiveKey] = useState<string>('1');

  const onChooseGoodsClick = () => {
    setSelectList(skus);
    setSelectQuantity(skus.length);
    setVisible(true);
  };
  const deleteSku = () => {
    setSelectList([]);
    setSelectQuantity(0);
    onChange({ seriesIndex, seriesItemPrizeIndex });
  };
  const onCloseDialog = () => {
    setVisible(false);
  };

  const onSelectedRow = (data) => {
    data.forEach((val, idx) => {
      val.seq = idx;
      val.seriesIndex = seriesIndex;
      val.seriesItemPrizeIndex = seriesItemPrizeIndex;
    });
    onChange(data);
    setSkus(data);
    setVisible(false);
  };

  // 对数组进行去重
  const setList = (data: any) => {
    const uniqueMap = new Map();
    data.forEach((item) => {
      // 以id作为Map的键，因为id是唯一标识符
      uniqueMap.set(item.skuId, item);
    });
    // 将Map转换回数组
    return Array.from(uniqueMap.values());
  };

  // 获取选择商品
  const getSelectList = (data) => {
    const list: any = setList(data);
    setSelectList(list);
    setSelectQuantity(list.length);
  };

  // 获取上传数据
  const uploadList = (data) => {
    const list: any = setList(data);
    setSelectList(list);
    setFilterList(list);
    setSelectQuantity(list.length);
  };

  // 获取选择商品
  const getHandleResult = (data) => {
    const list: any = setList(data);
    setSelectList(list);
    setSelectQuantity(list.length);
    setFilterList(list);
  };

  useEffect(() => {
    setSkus(value || []);
  }, [value]);
  // 通过Ref触发表单校验
  useImperativeHandle(sRef, () => ({
    changeDialogVisible: () => {
      onChooseGoodsClick();
    },
  }));
  return (
    <span>
      <>
        <Button type="primary" disabled={disabled} onClick={onChooseGoodsClick}>
          <Icon type="add" />
          添加商品{max > 0 ? (skus && skus.length > 0 ? `（${skus.length}/${max}）` : `（0/${max}）`) : ''}
        </Button>
        {skus.length > 0 && (
          <Button disabled={disabled} onClick={deleteSku}>
            <Icon type="delete" />
            删除商品({skus.length})
          </Button>
        )}
      </>
      {/* 选择选择商品弹窗 */}
      <LzDialog
        title={false}
        className="lz-dialog-medium"
        style={{ width: 921 }}
        visible={visible}
        onClose={onCloseDialog}
        onCancel={onCloseDialog}
        footer={false}
      >
        <div style={{ maxHeight: '650px' }}>
          <Tab size="small" activeKey={activeKey} onChange={(key) => setActiveKey(key)}>
            <Tab.Item key="1" title="选择商品">
              <SkuSelector
                max={max}
                defaultValue={defaultValue}
                value={skus}
                filterList={filterList}
                getSelectList={getSelectList}
                confirm={onSelectedRow}
                cancel={onCloseDialog}
                mode={mode}
              />
            </Tab.Item>
            {!activityEditDisabled() && (
              <Tab.Item key="2" title="导入商品">
                {/* <SkuExport */}
                {/*  max={max} */}
                {/*  value={skus} */}
                {/*  filterList={filterList} */}
                {/*  uploadList={uploadList} */}
                {/*  confirm={onSelectedRow} */}
                {/*  cancel={onCloseDialog} */}
                {/* /> */}
                <SkuImport
                  max={max}
                  selectList={selectList}
                  uploadList={uploadList}
                  cancel={onCloseDialog}
                  confirm={onSelectedRow}
                />
              </Tab.Item>
            )}
            {!activityEditDisabled() && localStorage.getItem(constant.LZ_SSO_PRD) === 'crm' && (
              <Tab.Item key="3" title="导入产品线">
                <LineImport max={max} uploadList={uploadList} cancel={onCloseDialog} confirm={onSelectedRow} />
              </Tab.Item>
            )}
            <Tab.Item key="4" title={`已选商品 (${selectQuantity}件)`}>
              <SelectedProduct
                max={max}
                selectList={selectList}
                getHandleResult={getHandleResult}
                confirm={onSelectedRow}
                cancel={onCloseDialog}
              />
            </Tab.Item>
          </Tab>
        </div>
      </LzDialog>
    </span>
  );
};
