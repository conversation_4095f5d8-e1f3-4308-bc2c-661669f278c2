/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Table, Input } from '@alifd/next';
import { formItemLayout, generateMembershipString, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{generateMembershipString(formData)}</FormItem>
        <FormItem label="参与者生成人群包">{formData.crowdPackage ? '开启' : '关闭'}</FormItem>
        <FormItem label="每日签到奖励">
          <FormItem>{formData.dayStatus ? '开启' : '关闭'}</FormItem>
          {!!formData.dayStatus && (
            <FormItem isPreview={false}>
              <Table dataSource={formData.prizeDay} style={{ marginTop: '15px' }}>
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => (
                    <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                      {PRIZE_TYPE[row.prizeType]}
                    </div>
                  )}
                />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }}
                />
                <Table.Column
                  title="发放份数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="单份价值(元)"
                  cell={(_, index, row) => <div>{row.unitPrice ?? Number(row.unitPrice).toFixed(2)}</div>}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
              </Table>
            </FormItem>
          )}
        </FormItem>
        <FormItem label="累计/连续奖品设置">
          <FormItem>{formData.signType === 1 ? '累计签到' : '连续签到'}</FormItem>
          <FormItem isPreview={false}>
            <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
              <Table.Column
                title={formData.signType === 1 ? '累计签到天数' : '连续签到天数'}
                cell={(_, index, row) => <div>{row.signDay}天</div>}
              />
              <Table.Column title="奖品名称" dataIndex="prizeName" />
              <Table.Column
                title="奖品类型"
                cell={(_, index, row) => (
                  <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                    {PRIZE_TYPE[row.prizeType]}
                  </div>
                )}
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ?? Number(row.unitPrice).toFixed(2)}</div>}
              />
              <Table.Column
                title="奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
            </Table>
          </FormItem>
        </FormItem>
        <FormItem label="分享标题">{formData.shareTitle}</FormItem>
        <FormItem label="图文分享图片">
          <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
        </FormItem>
        <FormItem label="京口令分享图片">
          <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
        </FormItem>
        <FormItem label="小程序分享图片">
          <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
        </FormItem>
        <FormItem label="规则内容">
          <Input.TextArea className={styles.wordBreak} value={formData.rules} />
        </FormItem>
      </Form>
    </div>
  );
};
