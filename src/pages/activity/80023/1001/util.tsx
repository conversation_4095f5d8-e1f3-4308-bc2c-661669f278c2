/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  unitCount: number;
  unitPrice: number | string;
  sendTotalCount: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  signDay: string;
}

export interface CustomValue {
  pageBg: string; // 主页背景图
  actBg: string; // 主页背景色
  actBgColor: string;
  shopNameColor: string; // 店铺名称颜色
  btnColor: string; // 按钮字体颜色
  btnBg: string; // 按钮背景颜色
  btnBorderColor: string; // 按钮边框颜色
  calendarBg: string; // 日历背景
  signInBeforeIcon: string; // 签到前图标
  signInAfterIcon: string; // 签到后图标
  signInBeforeBt: string; // 签到前按钮图片
  signInAfterBt: string; // 签到后按钮图片
  cmdImg: string; // 命令图片
  h5Img: string; // h5图片
  mpImg: string; // 小程序图片
  disableShopName: number;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  crowdPackage: number;
  dayStatus: number;
  prizeDay: PrizeInfo[];
  signType: number;
  prizeList: PrizeInfo[];
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  crowdBag: any;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '',
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/139876/9/21637/89337/619f33f3E28ef638d/7aa9c2fad2d8275b.png', // 主页背景图
  actBgColor: '#ee1e71', // 主页背景色
  shopNameColor: '#ffffff', // 店铺名称颜色
  btnColor: '#f2270c', // 按钮字体颜色
  btnBg: '#ffffff', // 按钮背景颜色
  btnBorderColor: '#ffc92e', // 按钮边框颜色
  calendarBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/198715/27/18448/42960/619f374bEbb72bec9/c412f142307a03a1.png', // 日历背景
  signInBeforeIcon:
    'https://img10.360buyimg.com/imgzone/jfs/t1/223285/7/5167/15890/61b0271cE16dd7be9/b8f98b8011ac676f.png', // 签到前图标
  signInAfterIcon:
    'https://img10.360buyimg.com/imgzone/jfs/t1/223285/7/5167/15890/61b0271cE16dd7be9/b8f98b8011ac676f.png', // 签到后图标
  signInBeforeBt:
    'https://img10.360buyimg.com/imgzone/jfs/t1/172976/9/21476/7997/619f374bE2035ed41/3102d3117caefd3b.png', // 签到前按钮图片
  signInAfterBt: 'https://img10.360buyimg.com/imgzone/jfs/t1/96157/2/25042/1910/62ff374bE1c0afa45/67f8d7e958ae2e55.png', // 签到后按钮图片
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/212375/20/4955/131242/6195b46eE738e16d0/a49ae787d4a39708.jpg',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/212375/20/4955/131242/6195b46eE738e16d0/a49ae787d4a39708.jpg',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/212375/20/4955/131242/6195b46eE738e16d0/a49ae787d4a39708.jpg',
  disableShopName: 0,
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `日历签到-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 活动结束生成人群包
    crowdPackage: 1,
    // 开启每日签到
    dayStatus: 0,
    // 每日签到奖品
    prizeDay: [],
    // 累计连续签到
    signType: 1,
    // 累计连续签到奖品
    prizeList: [],
    // 分享标题
    shareTitle: '每天来签到，每天送好礼！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: '',
  // 发放份数
  sendTotalCount: 0,
  // 签到天数
  signDay: '',
  ifPlan: 0,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `品牌会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  if (prize.prizeType === 1) {
    for (let j = 0; j < prize.couponPrizeList?.length; j++) {
      //   优惠券开始时间
      const couponStart = prize.couponPrizeList[j].startTime;
      const couponEnd = prize.couponPrizeList[j].endTime;
      if (couponStart && couponEnd) {
        const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(couponStart));
        if (isStart) {
          Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

          return false;
        }
        // 奖品的结束时间间是否大于活动的结束时间
        const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(couponEnd));
        if (isEnd) {
          Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
          return false;
        }
      }
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

const hasPrizeDay = (formData: PageData): boolean => {
  if (!formData.dayStatus) {
    return true;
  }
  for (const item of formData.prizeDay) {
    if (!item.prizeName) {
      Message.error('请设置每日签到奖励');
      return false;
    }
  }
  return true;
};

const hasPrize = (formData: PageData): boolean => {
  if (!formData.prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  for (const index in formData.prizeList) {
    console.log(index);
    const item = formData.prizeList[index];
    if (!item.prizeName) {
      Message.error('请设置奖品');
      return false;
    }
    if (Number(index)) {
      if (item.signDay <= formData.prizeList[Number(index) - 1].signDay) {
        Message.error(formData.signType === 1 ? '累计签到天数应逐级递增' : '连续签到天数应逐级递增');
        return false;
      }
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择每日签到奖品
  if (!hasPrizeDay(formData)) {
    return false;
  }
  // 没有选择奖品累计/连续奖品设置
  if (!hasPrize(formData)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(formData.prizeList, formData)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(formData.prizeDay, formData)) {
    return false;
  }
  return true;
};
