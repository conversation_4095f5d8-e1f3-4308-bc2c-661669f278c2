/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useReducer, useEffect, useImperativeHandle } from 'react';
import { Field } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { PageData, PRIZE_INFO, PrizeInfo } from '../../../util';
import CpbPrize from '../../../components/CpbPrize';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.prizeList];
    // 补齐奖品数到3
    for (let i = 0; i < 3 - prizeListLength; i++) {
      list.push(PRIZE_INFO);
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ prizeList: list.length ? list : formData.prizeList });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));

  return (
    <div>
      <LzPanel title="奖品设置">
        <CpbPrize formData={formData} onChange={(data) => setData(data)} />
      </LzPanel>
    </div>
  );
};
