/**
 * 大转盘抽奖数据报表
 */
import React, { useState } from 'react';
import { Button, Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from '@/pages/activity/92008/record/components/WinRecord';
import LotteryRecord from '@/pages/activity/92008/record/components/LotteryRecord';
import exportCombinedLogs from '@/utils/exportAll';
import { dataLotteryLogExport, dataWinningLogExport } from '@/api/v92008';
import AddDrawChance from '@/pages/activity/92008/record/components/AddDrawChance';
import { getParams } from '@/utils';
import { getShopId } from '@/utils/shopUtil';
import LzDocGuide from '@/components/LzDocGuide';

// const actList = ['1829042628868087809', '1839544518227570689', '1851544797771804674'];
// const shopList = ['1000003179', '1000001934', '734259', '1000076024', '1000002746'];

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="下单抽奖数据报表" actions={<LzDocGuide />}>
        <Button
          onClick={() => {
            exportCombinedLogs([dataLotteryLogExport, dataWinningLogExport], '下单抽奖数据报表');
          }}
          style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }}
        >
          导出全部
        </Button>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="抽奖记录" key="1">
            <LotteryRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinRecord />
          </Tab.Item>
          <Tab.Item title="增加抽奖机会" key="3">
            <AddDrawChance />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
