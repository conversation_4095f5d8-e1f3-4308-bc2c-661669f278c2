.preview {

  border-radius: 8.5px;
  overflow: hidden;
  .empty {
    width: 100%;
    height: 126px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #fff;
    color: #000;

  }
  .previewItem {
    position: relative;
    &:not(:last-child) {
      margin-bottom: 10px;
    }

    img {
      width: 100%;
      vertical-align: middle;
      -webkit-user-drag: none;
    }
    .hotZone {
      position: absolute;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
    }
  }

}
.addBtn {
  width: 100%;
  margin-bottom: 10px;
  margin-top: -5px;
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  .container {
    padding: 15px;
    background: #F4F6F9;
    border-radius: 5px;
  }
  .MemberContainer {
    @extend .container;
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
    .imgUpload {
      display: flex;

      .tip {
        margin-top: 10px;
      }

      .removeHotZone {
        margin-top: 4px;
        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
    .setting {
      margin-left: 15px;
      .btn  {
        width: 300px;
      }
      .urlContainer {
        width: 300px;
        height: 28px;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border-radius: 5px;
        margin-top: 8px;
        position: relative;
        .url {
          width: 100%;
          display: flex;
          overflow-x: scroll;
          white-space: nowrap;
        }
        .url::-webkit-scrollbar {
          display: none;
        }

        i {
          position: absolute;
          right: -17px;
          top: 5px;
          cursor: pointer;
          font-size: 12px;
        }
      }
    }
  }
  .hotContainer {
    padding: 15px;
     background: #F4F6F9;
    border-radius: 5px;
    .imgUpload {
      display: flex;

      .removeHotZone {
        margin-top: 4px;
        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
    .setting {
      margin-left: 15px;
      .btn  {
        width: 300px;
      }
      .urlContainer {
        width: 300px;
        height: 28px;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border-radius: 5px;
        margin-top: 8px;
        position: relative;
        .url {
          width: 100%;
          display: flex;
          overflow-x: scroll;
          white-space: nowrap;
        }
        .url::-webkit-scrollbar {
          display: none;
        }

        i {
          position: absolute;
          right: -17px;
          top: 5px;
          cursor: pointer;
          font-size: 12px;
        }
      }
    }

  }
}
