import React, { useImperative<PERSON><PERSON>le, useRef, useReducer, useState } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 消费订单设置
import Order from './components/Order';
// 奖品信息
import SendInfo from './components/Send';
// 发放设置
import PrizesInfo from './components/Prizes';
// 分享设置
import ShareInfo from './components/Share';
// 活动规则
import RulesInfo from './components/Rules';
// 报名设置
import ApplicationSetting from './components/ApplicationSetting';
import { PageData, CustomValue } from '../util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}

interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [renderBase] = useState(true);
  const [renderPrize, setRenderPrize] = useState(formData.threshold !== 1);

  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    if (activityInfo.supportLevels) {
      setTimeout(() => {
        setRenderPrize(true);
      });
    }
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  // const crowdRef = useRef<{ submit: () => void | null }>(null);
  const orderdRef = useRef<{ submit: () => void | null }>(null);
  const regSettingRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);
  const prizesRef = useRef<{ submit: () => void | null }>(null);
  const signRef = useRef<{ submit: () => void | null }>(null);
  const applicationSettingRef = useRef<{ submit: () => void | null }>(null);

  const checkForm = () => {
    const events: any[] = [
      baseRef.current!,
      applicationSettingRef.current,
      signRef.current,
      regSettingRef.current,
      prizesRef.current,
      shareRef.current,
      orderdRef.current,
    ];
    return events.every((item, index: number): boolean => {
      const result = events[index].submit();
      if (result) {
        return false;
      }
      return true;
    });
  };

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current!,
        // crowdRef.current,
        signRef.current,
        prizesRef.current,
        shareRef.current,
        rulesRef.current,
        applicationSettingRef.current,
        orderdRef.current,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  return (
    <div className={styles.setting}>
      {renderBase && <BaseInfo sRef={baseRef} {...settingProps} />}
      <Order sRef={orderdRef} {...settingProps} />
      <PrizesInfo sRef={prizesRef} {...settingProps} />
      <ApplicationSetting sRef={applicationSettingRef} {...settingProps} />
      {renderPrize && <SendInfo sRef={signRef} {...settingProps} />}
      <ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} />
      <RulesInfo sRef={rulesRef} {...settingProps} checkForm={checkForm} />
    </div>
  );
};
