.list {
  display: flex;
  justify-content: space-between;

  .content {
    flex: 0.45;
    background-color: #f0f2f5;
    padding: 20px;
  }

  .days {
    font-size: 18px;

    span {
      font-size: 36px;
      margin-right: 5px;
    }
  }
}

.calendarRest {
  :global {
    .next-calendar2-cell-selected .next-calendar2-cell-inner {
      background: transparent !important;
      color: #585d66 !important;

      &:hover {
        background: transparent !important;
      }
    }
  }
}

.restDay {
  background: #1677ff;
  color: #fff;
  border-radius: 2px;
}

.avatarUrl {
  width: 30px;
  height: 30px;
  object-fit: contain;
  border-radius: 50%;
  vertical-align: middle;
  margin-right: 10px;
}
