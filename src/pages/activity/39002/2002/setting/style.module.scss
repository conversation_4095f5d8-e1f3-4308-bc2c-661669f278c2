.setting {
  margin-top: 15px;
}

.panel {
  box-sizing: border-box;
  background: #f5f7f9;
  padding: 15px;
  border: 1px solid #d7dde4;
  min-width: 350px;
}

.number {
  margin: 0 10px;
}

.tip {
  font-size: 12px;
  color: gray;
  margin-top: 15px;
}

.number {
  margin: 0 10px;
}

.orderTypes {
  display: inline-block;
  margin-left: 10px;

  .order {
    position: relative;
    display: inline-block;
    width: 55px;
    height: 20px;
    border-radius: 10px;
    line-height: 20px;
    text-align: center;
    background-color: #c0e8fe;
    color: #18A7F7;
    margin-right: 10px;

    &:not(:last-child)::after {
      content: '';
      display: inline-block;
      height: 1px;
      width: 10px;
      background-color: #18A7F7;
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .orderGray {
    background-color: #f0f0f0;
    color: #999999;

    &:not(:last-child)::after {
      content: '';
      display: inline-block;
      height: 1px;
      width: 10px;
      background-color: #f0f0f0;
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.tips {
  font-size: 12px;
}

.point-input {
  margin-right: 5px;
  width: 130px !important;
}

.exchange-input {
  margin-right: 5px;
  width: 150px !important;
}

.cycle-select {
  margin-right: 5px;
  width: 130px;
}

.select-time {
  width: 100px;
}

.select-time-line {
  margin: 0px 5px;
}

.form-item-style {
  height: 30px;
  line-height: 30px;
}

.choose-promotion-tips {
  margin-top: 6px;
  color: #ff3333;

  .choose-promotion-tips-line2 {
    margin: 5px 0px 0px 36px;
  }
}

.help-icon-style {
  margin-left: 2px;
  line-height: 15px;
}

.input-width {
  width: 200px !important;
}

.input-width1 {
  width: 500px !important;
}

.point-unit {
  margin: 0px 5px;
  height: 28px;
  line-height: 28px;
}

.unit-style {
  margin: 0px 5px;
}

.ballon-img {
  width: 900px;
}

.goods-img {
  margin-right: 5px;
  width: 60px;
  height: 60px;
}

.flex-box {
  display: flex;
}

.point-list-box {
  padding: 10px 20px 5px;
  width: 300px;
  background-color: #f5f7f9;
  border-radius: 5px;

  .point-list-input {
    margin: 0px 5px;
    width: 150px !important;
  }
}

.exchange-limit {
  margin: 0px 5px;
  width: 90px !important;
}

.formItemInline {
  display: inline-block !important;
  margin-bottom: 0 !important;
  vertical-align: top;
}
