import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Select, Balloon, Icon, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataExchangeLog, dataExchangeLogExport, dataExchangeUploadPin, dataUpdataOrder } from '@/api/v39002';
import Utils, { deepCopy, downloadExcel, getParams, isPopShop } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import { appHistory } from '@ice/stark-app';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import styles from './style.module.scss';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const RECEIVE_STATE = [
  { label: '全部', value: '' },
  { label: '领取成功', value: 1 },
  { label: '领取失败', value: 0 },
];
const toYlList = () => {
  appHistory.push('/crowd/own/list/manage');
};

const toJDList = () => {
  appHistory.push('/crowd/list/manage');
};
export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogData, setDialogData] = useState<any>([]);

  const [packVisible, setPackVisible] = useState(false);
  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
    console.log({ ...formValue, ...defaultPage }, '---------------');
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.shopType = isPopShop() ? 0 : 1;
    dataExchangeLog(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    formValue.shopType = isPopShop() ? 0 : 1;
    dataExchangeLogExport(formValue).then((data: any) => downloadExcel(data, '积分兑换记录'));
  };
  // 更新订单状态
  const upDateOrderStatus = () => {
    dataUpdataOrder({ activityId: getParams('id') })
      .then(() => {
        const formValue: any = field.getValues();
        loadData({ ...formValue, ...defaultPage });
      })
      .catch((e) => {
        Message.error(e.message);
      });
  };
  const encryptStr = (pin: string): string => {
    const first = pin.slice(0, 1);
    const last = pin.slice(-1);
    return `${first}****${last}`;
  };
  const copyText = async (text: string) => {
    await Utils.copyText(text);
    Message.success('已成功复制至剪切板');
  };

  const showDetail = (data) => {
    console.log(data, 'data');
    setDialogData(data);
    setDialogVisible(true);
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="领取时间">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={constant.DATE_FORMAT_TEMPLATE} />
        </FormItem>
        <FormItem name="status" label="结果状态">
          <Select dataSource={RECEIVE_STATE} followTrigger defaultValue="" />
        </FormItem>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button disabled={!tableData.length} onClick={upDateOrderStatus}>
          更新订单状态
        </Button>
        <Button disabled={!tableData.length} onClick={exportData} style={{ marginLeft: '10px' }}>
          导出
        </Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成云鹿人群&nbsp;
          <Balloon v2 trigger={<Icon type="help" size="small" />} triggerType="hover" align="bl" closable={false}>
            <div>
              <div style={{ marginBottom: '5px' }}>1.云鹿人群均以当前的查询条件进行生成;</div>
              <div style={{ marginBottom: '5px', lineHeight: '20px' }}>
                2.生成云鹿人群后，如需进行短信营销，请在
                <span onClick={toYlList} style={{ cursor: 'pointer', color: '#1677ff' }}>
                  云鹿人群包列表
                </span>
                中奖云鹿人群转换为京东人群再发送短信;转换后的人群可在
                <span onClick={toJDList} style={{ cursor: 'pointer', color: '#1677ff' }}>
                  京东人群包列表
                </span>
                中查看并管理。
              </div>
            </div>
          </Balloon>
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column
          width={120}
          title="用户昵称"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div>
              {data.nickName ? encryptStr(data.nickName) : '-'}
              {data.nickName && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    copyText(data.nickName);
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column
          width={120}
          title="用户pin"
          dataIndex="encryptPin"
          cell={(value, index, data) => (
            <div>
              {data.encryptPin ? encryptStr(data.encryptPin) : '-'}
              {data.encryptPin && (
                <span
                  className={`iconfont icon-fuzhi ${styles.copy}`}
                  onClick={() => {
                    copyText(data.encryptPin);
                  }}
                />
              )}
            </div>
          )}
        />
        <Table.Column width={200} title="活动名称" dataIndex="activityName" />
        <Table.Column width={140} title="权益名称" dataIndex="rightsName" />
        <Table.Column
          width={120}
          title="订单详情"
          dataIndex="orderList"
          cell={(value, index) => (
            <Button type="primary" text onClick={() => showDetail(value)}>
              查看详情
            </Button>
          )}
        />
        <Table.Column
          title="领取时间"
          width="200px"
          dataIndex="createTime"
          cell={(value, index, data) => (
            <div>{data.createTime ? format.formatDateTimeDayjs(data.createTime) : '-'}</div>
          )}
        />
        <Table.Column title="兑换份数" dataIndex="exchangeNum" width="140px" />
        <Table.Column width={200} title="会员等级（领取时）" dataIndex="memberLevel" />
        <Table.Column title="结果状态" dataIndex="receiveStatus" width="140px" />
        <Table.Column title="详情" dataIndex="msg" width="140px" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />

      <LzDialog
        title="生成云鹿人群"
        className="lz-dialog-mini"
        visible={packVisible}
        footer={false}
        onCancel={() => setPackVisible(false)}
        onClose={() => setPackVisible(false)}
      >
        <LzGenerateCrowdBag
          dataUploadPin={dataExchangeUploadPin}
          formValue={field.getValues()}
          cancel={() => setPackVisible(false)}
        />
      </LzDialog>
      <LzDialog
        visible={dialogVisible}
        footer={false}
        onCancel={() => setDialogVisible(false)}
        onClose={() => setDialogVisible(false)}
      >
        <div>
          <Table dataSource={dialogData} style={{ marginTop: '15px' }}>
            <Table.Column title="订单号" dataIndex="orderId" />
            <Table.Column title="订单结束时间" dataIndex="orderFinishTime" />
            <Table.Column title="订单状态" dataIndex="orderStatus" />
            <Table.Column title="订单金额" dataIndex="orderAmount" />
            <Table.Column title="订单价" dataIndex="payPrice" />
          </Table>
        </div>
      </LzDialog>
    </div>
  );
};
