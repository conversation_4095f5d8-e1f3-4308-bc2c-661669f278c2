.preview {
  .value {
    display: flex;
    align-items: center;
  }
}
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  .skuContainer {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;

    .skuImg {
      width: 80px;
      height: 80px;
      margin-right: 8px;
    }
    .skuName {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .skuId {
      color: lightgray;
      font-size: 12px;
    }
    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}

.headerTitle {
  display: flex;
  height: 40px;
  align-items: center;
  background: #f0f2f5;
  padding-left: 15px;

  > span {
    margin-right: 8px;
  }
}

.part1 {
  display: flex;
  justify-content: flex-start;
}

.part1_p1 {
  display: -webkit-box;
  width: 100%;
  margin: 0;
  overflow: hidden;
  line-height: 1.5 !important;
  text-align: left;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.part1_p2 {
  margin: 0;
  color: #8e97a2;
  text-align: left;
}


.coupon {
  display: flex;
  border: 1px solid lightgray;
  padding: 10px;
  color: #4d4c4c;
  width: 515px;
  position: relative;
  cursor: pointer;
  flex-direction: column;

  .info {
    display: flex;
  }
}
.couponLeft {
  > div {
    margin-top: 5px;
  }
}
.couponRight {
  margin-left: 15px;
  > div {
    line-height: 19px;
  }
}
.title-color {
  margin-right: 5px;
  color: #585D66;
}
.small-line-box {
  margin: 0px 5px;
}
.choose-goods-box {
  display: flex;
}
.choose-goods-tips {
  color: #c7c7c7;
}
.goods-img {
  margin-right: 5px;
  width: 60px;
  height: 60px;
}
