import React, { useImperativeHandle, useReducer, useEffect } from 'react';
import { Form, Field, Checkbox } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { FormLayout, PageData } from '../../../util';
import styles from '../../style.module.scss';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  return (
    <div>
      <LzPanel title="活动监控">
        <Form {...formItemLayout} field={field}>
          <FormItem label="活动强制结束">
            <Checkbox
              checked={formData.endActivity}
              onChange={(v) => {
                setData({ endActivity: v });
              }}
            >
              全部奖品发完强制结束活动
            </Checkbox>
            <div className={styles['gray-text-box']}>注：勾选后，所有奖品发放完毕后，活动会自动结束</div>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
