import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { deepCopy, getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import React from 'react';

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;

  stepCount: number | string;
  stepAmount: number | string;
  exchangeCount: number;
}
export const PRIZE_INFO = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,

  stepCount: '',
  stepAmount: '',
  exchangeCount: 1,
};
export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export interface PageData {
  activityName: string;
  isAllOrderSku: number;
  cmdImg: string;
  playType: number;
  crowdBag: any;
  endTime: string;
  h5Img: string;
  mpImg: string;
  orderType: number;
  orderRangeTime: [Dayjs, Dayjs];
  orderStartTime: string;
  orderEndTime: string;
  receiveCount: number;
  customerLimitDay: number;
  rangeDate: [Dayjs, Dayjs];
  rules: string;
  shareStatus: number;
  shareTitle: string;
  skuList: any;
  startTime: string;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  prizeList: PrizeInfo[];
  templateCode: string;
  threshold: number;
  gradeLabel: string[];
  shopType: number;
}
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
export const activityTypeGroup = {
  1: {
    consumptionActivityType: '满额有礼',
    activityUnit: '元',
    minValue: 0,
    maxValue: 999999,
    actBg: '//img10.360buyimg.com/imgzone/jfs/t1/234208/36/15632/82842/66053855F3b688fa4/0f5eb03164d006d5.png',
    tipsText: '用户【累计消费的总金额】满足对应条件后，即有机会获得对应奖励;',
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/241153/12/6372/74617/6614de61Fe1d96b59/c4a7e883db680607.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/179756/23/43990/48472/6614de61F27aa5d1a/7f5b4de6378d9ac4.jpg',
  },
  2: {
    consumptionActivityType: '满次有礼',
    activityUnit: '次',
    minValue: 1,
    maxValue: 999,
    actBg: '//img10.360buyimg.com/imgzone/jfs/t1/231015/23/15830/102392/660a236dF84cf17de/cf1792dd21f75529.png',
    tipsText: '用户【累计消费订单次数】满足门槛条件后，即有机会获得设置的奖励;',
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/199826/15/43748/11083/6614de62F03ec89b1/e2ee2386cf609174.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/249243/11/7065/75493/6614de61F8ab5acc8/e1844e98fe6cd200.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/190865/31/43669/47870/6614de61F85cbf275/ce9fbcbb1aabd0db.jpg',
  },
};
export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  ruleBtn: string;
  myPrizeBtn: string;
  prizeBg: string;
  surplusTextColor: string;
  prevBtn: string;
  prizeIconAct: string;
  prizeIcon: string;
  prizeBtnAct: string;
  prizeBtn: string;
  prizeBtnColor: string;
  joinActBtn: string;
  toShopBtn: string;
  progressBg: string;
  progressColor: string;
  thresholdColor: string;
  textColor: string;
  indexRule: string;
  ruleBk: string;
  ruleTextColor: string;
  joinMemberBk: string;
  notSaveAddressBk: string;
  saveAddressBk: string;
  saveAddressTextColor: string;
  confirmAddressBk: string;
  confirmAddressTextColor: string;
  myPrizeBk: string;
  orderSkuBtn: string;
  myPrizeTextColor: string;
  myPrizeTitle: string;
  addressInfoBk: string;
  addressInfoTextColor: string;
  goodsBk: string;
  goodsToShopBtn: string;
  confirmPrizeBk: string;
  showGoods: number;
  h5Img: string;
  cmdImg: string;
  mpImg: string;
  consumptionActivityType: number;
}
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE_DATA: CustomValue = {
  actBg: '',
  pageBg: '//img10.360buyimg.com/imgzone/jfs/t1/267970/34/14036/40676/678df9caF333e1334/bef12dc005feda0d.jpg',
  actBgColor: '#fff',
  ruleBtn: '//img10.360buyimg.com/imgzone/jfs/t1/252446/31/14797/4281/678df9caF80e5b3f7/90d35b6c25a22e8d.png',
  myPrizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/267281/4/13749/4200/678df9caF3552d701/01a809544cfe131a.png',
  orderSkuBtn: '//img10.360buyimg.com/imgzone/jfs/t1/252944/35/15248/4194/67a5a406F694fe520/68b46188c3b45043.png',
  prizeBg: '//img10.360buyimg.com/imgzone/jfs/t1/261096/16/13880/125011/678df9cbFc6d07141/c85ebbaded13dd75.png',
  surplusTextColor: '#d00204',
  prevBtn: 'https://img10.360buyimg.com/imgzone/jfs/t1/259439/32/14230/1767/004e8c01Ff0ef8285/393caa5188e5bf12.gif',
  prizeIconAct: '//img10.360buyimg.com/imgzone/jfs/t1/257133/37/14095/4915/004e8c01F74007f88/ca51afdac9302071.gif',
  prizeIcon: '//img10.360buyimg.com/imgzone/jfs/t1/254413/8/15080/4622/004e8c01F2d80cdbf/2a6b9caef02b204c.gif',
  prizeBtnAct: '//img10.360buyimg.com/imgzone/jfs/t1/261397/7/13927/569/678df9caF4d8d20a7/a06246c83fc52206.png',
  prizeBtn: '//img10.360buyimg.com/imgzone/jfs/t1/262635/34/13913/567/678df9caF9fab4d9e/f96572f62b3f3a38.png',
  prizeBtnColor: '#fff',
  joinActBtn: '//img10.360buyimg.com/imgzone/jfs/t1/255940/23/14827/19459/004e8c01Fd6761efd/a582c83826ff9291.gif',
  toShopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/256777/18/14250/19241/004e8c01Fcd1ba311/dbe00e8acb9ff79f.gif',
  // 进度条背景图
  progressBg: '//img10.360buyimg.com/imgzone/jfs/t1/263851/29/14030/4485/678df9caF667e6e40/a820891305d2f2ae.png',
  // 进度条前颜色
  progressColor: '#d00204',
  // 门槛文字颜色
  thresholdColor: '#111111',
  // 页面文字颜色
  textColor: '#181818',
  indexRule: '//img10.360buyimg.com/imgzone/jfs/t1/267246/36/13843/49840/678df9cbF473870c4/26e5a18c9f7c5220.png',

  ruleBk: '//img10.360buyimg.com/imgzone/jfs/t1/237290/23/32281/16624/6790bd80Fc701305d/9c148897a68e92b2.png',
  ruleTextColor: '#111111',
  joinMemberBk: '//img10.360buyimg.com/imgzone/jfs/t1/254663/4/15615/23238/6790bd81Fe510cde5/dcfb9552e0e37ad9.png',
  notSaveAddressBk: '//img10.360buyimg.com/imgzone/jfs/t1/262596/16/14644/17522/6790bd81F6c68d1b0/80ae217fd8e9f57e.png',
  saveAddressBk: '//img10.360buyimg.com/imgzone/jfs/t1/261478/37/13987/35443/6791b21aFd62d8ff5/ca29c60119d7a5e9.png',
  saveAddressTextColor: '#111111',
  confirmAddressBk: '//img10.360buyimg.com/imgzone/jfs/t1/258329/33/14645/23413/6790bd80F41a3e856/5fe3fc1ad0f9caf9.png',
  confirmAddressTextColor: '#111111',
  myPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/260845/35/14317/16680/6790bd80Fcf40ad70/ecf6cea999e7d5fe.png',
  myPrizeTextColor: '#111111',
  myPrizeTitle: '//img10.360buyimg.com/imgzone/jfs/t1/236755/27/32467/4442/6790bd80F844a7610/a941688a3ea10124.png',
  addressInfoBk: '//img10.360buyimg.com/imgzone/jfs/t1/252298/30/15804/17491/6790bd81Fa1d727b5/61a109781818ab0c.png',
  addressInfoTextColor: '#111111',
  goodsBk: 'https://img10.360buyimg.com/imgzone/jfs/t1/252583/23/17622/17301/67a581cbF8fe8ca4e/4da29e8d0361cec2.png',
  goodsToShopBtn: '//img10.360buyimg.com/imgzone/jfs/t1/256777/18/14250/19241/004e8c01Fcd1ba311/dbe00e8acb9ff79f.gif',
  confirmPrizeBk: '//img10.360buyimg.com/imgzone/jfs/t1/253936/5/18243/17628/67adc221Fcd0e8e18/7025c1d4d8006ba4.png',
  showGoods: 1,

  h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
  cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
  mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
  consumptionActivityType: 1,
};
// 活动设置默认数据
export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `珀莱雅会员满赠-${dayjs().format('YYYY-MM-DD')}`,
    playType: DEFAULT_CUSTOM_VALUE_DATA.consumptionActivityType,
    // 活动结束时间
    crowdBag: null,
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    h5Img: '//img10.360buyimg.com/imgzone/jfs/t1/136924/14/44785/11029/6614de62Fe70dcd26/215742d3f6dc467b.png',
    cmdImg: '//img10.360buyimg.com/imgzone/jfs/t1/241153/12/6372/74617/6614de61Fe1d96b59/c4a7e883db680607.png',
    mpImg: '//img10.360buyimg.com/imgzone/jfs/t1/179756/23/43990/48472/6614de61F27aa5d1a/7f5b4de6378d9ac4.jpg',
    orderType: 1,
    orderRangeTime: [dayjs().add(0, 'day'), dayjs().add(30, 'day')],
    orderStartTime: dayjs().add(0, 'day').format('YYYY-MM-DD 00:00:00'),
    orderEndTime: dayjs().add(30, 'day').format('YYYY-MM-DD 23:59:59'),
    receiveCount: 1,
    customerLimitDay: 7,

    rangeDate: [dayjs().add(0, 'day').startOf('day'), dayjs().add(30, 'day').endOf('day')],
    rules: '',
    shareStatus: 1,
    shareTitle: `${activityTypeGroup[DEFAULT_CUSTOM_VALUE_DATA.consumptionActivityType].consumptionActivityType}`,
    isAllOrderSku: 0,
    skuList: [],
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    prizeList: [deepCopy(PRIZE_INFO)],
    templateCode: '2001',
    threshold: 1, // 后端需要的一个参数，参数值与autoStopAct一致
    gradeLabel: [], // 根据实际情况，可能需要定义奖品的类型
    shopType: 0,
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

const isOrderFinishStartTimeValid = (
  activityStartTime: string,
  activityEndTime: string,
  orderFinishStartTime: any,
  orderFinishEndTime: any,
): boolean => {
  // const isGreaterThanStartBefore: boolean = dayjs(orderFinishStartTime).isAfter(
  //   dayjs(activityStartTime).subtract(1, 'month'),
  // );
  const isGreaterEnd: boolean = dayjs(activityEndTime).isAfter(orderFinishEndTime);
  // if (!isGreaterThanStartBefore) {
  //   Message.error('【订单完成时间】开始时间，最多只能早于活动开始时间1个月');
  //   return false;
  // }
  if (dayjs(orderFinishEndTime).format('YYYY-MM-DD') !== dayjs(activityEndTime).format('YYYY-MM-DD') && !isGreaterEnd) {
    Message.error('【订单完成时间】结束时间,不能晚于活动结束时间');
    return false;
  }
  return true;
};
export const PRIZE_TYPE: any = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  5: '折扣商品',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
  11: '令牌',
};

// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品信息完整
const arePrizesValid = (formData: PageData): boolean => {
  const { prizeList } = formData;
  for (let i = 0; i < prizeList.length; i++) {
    if (!prizeList[i].prizeName) {
      Message.error('请选择奖品');
      return false;
    }
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
    if (formData.playType === 1) {
      if (!prizeList[i].stepAmount) {
        Message.error('请输入领奖条件');
        return false;
      }
      if (i && prizeList[i].stepAmount <= prizeList[i - 1].stepAmount) {
        Message.error('领奖条件需要递增');
        return false;
      }
    }
    if (formData.playType === 2) {
      if (!prizeList[i].stepCount) {
        Message.error('请输入领奖条件');
        return false;
      }
      if (i && prizeList[i].stepCount <= prizeList[i - 1].stepCount) {
        Message.error('领奖条件需要递增');
        return false;
      }
    }
    if (!prizeList[i].exchangeCount) {
      Message.error('请输入门槛兑换次数');
      return false;
    }
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (
    !isOrderFinishStartTimeValid(
      formData.startTime,
      formData.endTime,
      formData.orderRangeTime[0],
      formData.orderRangeTime[1],
    )
  ) {
    return false;
  }
  if (formData.prizeList.length === 0) {
    Message.error('请选择奖品');
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(formData)) {
    return false;
  }
  return true;
};
