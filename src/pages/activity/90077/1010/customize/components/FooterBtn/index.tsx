/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 获奖名单
 */
import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Input, Switch } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '@/pages/activity/90077/1010/util';

const formLayout: Omit<FormLayout, 'labelAlign' | 'wrapperCol'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);

  return (
    <div className={styles.award}>
      <LzPanel title="底部设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="更多会员活动按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  value={formData.btnToShop}
                  onChange={(btnToShop) => {
                    setForm({ btnToShop });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：建议431*89px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ btnToShop: defaultValue?.btnToShop });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item required label="更多会员活动跳转链接">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <Input
                  value={formData.moreActLink}
                  onChange={(moreActLink) => setForm({ moreActLink })}
                  className={styles.popupLink}
                  placeholder="请输入跳转链接"
                />
                <div className={styles.tips}>此链接为点击更多会员活动按钮的跳转链接</div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="分享好友组件"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={375} */}
          {/*        height={88} */}
          {/*        value={formData.btnShare} */}
          {/*        onChange={(btnShare) => { */}
          {/*          setForm({ btnShare }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：375*88px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ btnShare: defaultValue?.btnShare }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
        </Form>
      </LzPanel>

      <LzPanel title="跳转设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item required label="是否开启跳转">
            <Switch
              checked={formData.isShowJump}
              autoWidth
              checkedChildren={'已启用'}
              unCheckedChildren={'已关闭'}
              onChange={(isShowJump) => {
                setForm({ isShowJump });
              }}
            />
          </Form.Item>
          {formData.isShowJump && (
            <Form.Item required label="跳转链接">
              <Grid.Row>
                <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                  <Input
                    value={formData.jumpUrl}
                    onChange={(jumpUrl) => setForm({ jumpUrl })}
                    className={styles.popupLink}
                    placeholder="请输入跳转链接"
                  />
                  <div className={styles.tips}>此链接为进入页面不符合首购条件跳转的链接</div>
                </Form.Item>
              </Grid.Row>
            </Form.Item>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
