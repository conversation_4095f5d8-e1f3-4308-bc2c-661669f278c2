/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 大转盘
 */
import React, { useReducer, useEffect, useState } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Icon, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '../EditHotZone';
import { deepCopy } from '@/utils';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [hotVisible, setHotVisible] = React.useState(false);
  const [editHotData, setEditHotData] = useState({
    bg: '',
    hotZoneList: [],
  });
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };

  /**
   * 添加热区
   */
  const addHotZone = () => {
    if (!formData.prizeBg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditHotData({
      bg: formData.prizeBg,
      hotZoneList: formData.hotZoneList,
    });
    setHotVisible(true);
  };

  const onCloseHotZone = () => {
    setHotVisible(false);
  };

  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel title="领奖设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="领奖背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  // height={1562}
                  value={formData.prizeBg}
                  onChange={(prizeBg) => {
                    setForm({ prizeBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px，建议高度1562px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ prizeBg: defaultValue?.prizeBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="奖品和剩余份数颜色">
            <LzColorPicker value={formData.prizeNameColor} onChange={(prizeNameColor) => setForm({ prizeNameColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ prizeNameColor: defaultValue?.prizeNameColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="领奖按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={272}
                  height={75}
                  value={formData.getPrizeBtn}
                  onChange={(getPrizeBtn) => {
                    setForm({ getPrizeBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：272*75px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ getPrizeBtn: defaultValue?.getPrizeBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="领奖置灰按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={272}
                  height={75}
                  value={formData.getPrizeGrayBtn}
                  onChange={(getPrizeGrayBtn) => {
                    setForm({ getPrizeGrayBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：272*75px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ getPrizeGrayBtn: defaultValue?.getPrizeGrayBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="图片热区" required key="hot">
            <Button
              style={{ margin: '20px auto', width: '20%', color: '#3399FF' }}
              type="normal"
              onClick={() => {
                addHotZone();
              }}
            >
              <Icon className="iconfont icon-a-1plus" style={{ marginRight: 10 }} />
              {`添加热区`}
            </Button>
          </Form.Item>
          <LzDialog
            title={'编辑热区'}
            visible={hotVisible}
            footer={false}
            onClose={() => setHotVisible(false)}
            style={{ width: '750px' }}
          >
            <EditHotZone
              data={deepCopy(editHotData)}
              dataIndex={0}
              dispatch={(val: any) => {
                setForm({
                  hotZoneList: val,
                });
                setHotVisible(false);
              }}
              onClose={() => onCloseHotZone()}
            />
          </LzDialog>
        </Form>
      </LzPanel>
    </div>
  );
};
