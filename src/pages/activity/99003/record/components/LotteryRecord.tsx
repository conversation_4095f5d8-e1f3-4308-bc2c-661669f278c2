import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Select, Field, Table, Button, Message } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataParticipateRecord, dataParticipateRecordExport } from '@/api/v99003';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

// isWin	1待发放  2 已失效 3已领取 5发放失败
const IS_WIN = [
  { label: '全部', value: '' },
  { label: '中奖', value: 1 },
  { label: '未中奖', value: 0 },
];
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const dateFormat = 'YYYY-MM-DD';

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataParticipateRecord(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataParticipateRecordExport(formValue).then((data: any) => downloadExcel(data, '参与记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        {/* <Form.Item name="nickName" label="用户昵称"> */}
        {/*  <Input maxLength={20} placeholder="请输入用户昵称" /> */}
        {/* </Form.Item> */}
        {/* <Form.Item name="pin" label="用户pin"> */}
        {/*  <Input placeholder="请输入用户pin" /> */}
        {/* </Form.Item> */}
        {/* <FormItem name="isWin" label="发放状态" requiredMessage="请选择发放状态"> */}
        {/*  <Select */}
        {/*    followTrigger */}
        {/*    mode="single" */}
        {/*    showSearch */}
        {/*    hasClear */}
        {/*    style={{ marginRight: 8 }} */}
        {/*    dataSource={IS_WIN} */}
        {/*    defaultValue={''} */}
        {/*  /> */}
        {/* </FormItem> */}
        <FormItem name="dateRange" label="参与时间">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={dateFormat} />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        {/* <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}> */}
        {/*  生成人群包 */}
        {/* </Button> */}
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="日期" cell={(value, index, data) => <div>{data.dt}</div>} />
        <Table.Column title="PV" dataIndex="pv" />
        <Table.Column title="UV" dataIndex="uv" />
        <Table.Column title="入会人数" dataIndex="memberNum" />
        <Table.Column title="参与人数" dataIndex="drawNum" />
        <Table.Column title="关注人数" dataIndex="followNum" />
        <Table.Column title="直播间评价人数" dataIndex="liveContentNum" />
        <Table.Column title="中奖人数" dataIndex="winNum" />
        <Table.Column title="奖品数量" dataIndex="prizeTotalNum" />
        <Table.Column title="剩余奖品数量" dataIndex="prizeRemainNum" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {/* <LzDialog */}
      {/*  title="生成人群包" */}
      {/*  className="lz-dialog-mini" */}
      {/*  visible={packVisible} */}
      {/*  footer={false} */}
      {/*  onCancel={() => setPackVisible(false)} */}
      {/*  onClose={() => setPackVisible(false)} */}
      {/* > */}
      {/*  <LzGenerateCrowdBag */}
      {/*    dataUploadPin={dataLotteryUploadPin} */}
      {/*    formValue={field.getValues()} */}
      {/*    cancel={() => setPackVisible(false)} */}
      {/*  /> */}
      {/* </LzDialog> */}
    </div>
  );
};
