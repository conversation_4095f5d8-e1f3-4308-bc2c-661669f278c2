/**
 * Author: z<PERSON>yue
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  giftId?: string;
  giftName?: string;
}
export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  followShopBtn: string;
  makeCommentBtn: string;
  drawBtn: string;
  rules: string;
  myPrizeBtn: string;
  joinMemberBg: string;
  notWinningBg: string;
  myPrizeBg: string;
  addressBg: string;
  logisticsBg: string;
  makeCommentContentBtn: string;
}
export interface PageData {
  activityName: string;
  rangeDateBulletScreen: [Dayjs, Dayjs];
  bulletScreenStartTime: string;
  bulletScreenEndTime: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  bulletScreenWordCount: number; // 免费赠送次数
  bulletScreenComment: string; // 免费赠送评论
  bulletScreenType: number; // 免费赠送类型
  crowdBag: any;
  totalProbability: number;
  prizeList: PrizeInfo[]; // 根据实际情况，可能需要定义奖品的类型
  taskList: any[]; // 根据实际情况，可能需要定义任务的类型
  winLotteryDayType: number;
  winLotteryDayCounts: number;
  winLotteryTotalType: number;
  winLotteryTotalCounts: number;
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  setting: string;
  prizeListMap: any;
  drawScoreLimit: number; // 游戏分数
  drawChanceNum: number; // 可抽奖次数
  skuList: any[]; // 曝光商品
  // 是否开启曝光
  isExposure: number;
}
interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 关注店铺按钮
  followShopBtn: '',
  // 发表评论按钮(字数)
  makeCommentBtn: '',
  // 发表评论按钮(内容)
  makeCommentContentBtn: '',
  // 立即抽奖
  drawBtn: '',
  // 活动规则
  rules: '',
  // 我的奖品
  myPrizeBtn: '',
  // 入会弹窗
  joinMemberBg: '',
  // 未中奖背景
  notWinningBg: '',
  // 我的奖品弹窗
  myPrizeBg: '',
  logisticsBg: '',
  addressBg: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `SKII-直播抽奖-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 0,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5,-9',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 抽奖机会数
    bulletScreenWordCount: 1,
    bulletScreenComment: '',
    // 初始赠送 1/每人每天赠送 2
    bulletScreenType: 1,
    // 中奖总概率
    totalProbability: 0,
    // 奖品列表
    prizeList: [],
    // 任务列表
    taskList: [],
    // 是否限制每人每天中奖次数 1 不限制 2 限制
    winLotteryDayType: 1,
    // 每人每天中奖次数
    winLotteryDayCounts: 1,
    // 是否限制每人累计次数 1 不限制 2 限制
    winLotteryTotalType: 2,
    // 每人累计中奖次数
    winLotteryTotalCounts: 1,
    // 是否开启分享
    shareStatus: 0,
    // 分享标题
    shareTitle: '小鸟飞翔穿越障碍，更有机会抽取大奖，一起来玩吧！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '123',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    // 奖品设置 1: 统一设置 2: 按等级设置
    setting: '1',
    // 根据等级设置奖品列表
    prizeListMap: {
      all: [],
    },
    drawScoreLimit: 140,
    drawChanceNum: 1,
    // 商品列表
    skuList: [],
    // 是否开启曝光
    isExposure: 1,
    rangeDateBulletScreen: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 弹幕开始时间
    bulletScreenStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 弹幕结束时间
    bulletScreenEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 1,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  giftId: '',
  giftName: '',
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
const bulletScreenWordCountValid = (formData: PageData): boolean => {
  // 弹幕开始时间不能早于活动开始时间
  const isStart: boolean = dayjs(formData.bulletScreenStartTime).isBefore(dayjs(formData.startTime));
  if (isStart) {
    Message.error('弹幕开始时间需要晚于或等于活动开始时间');
    return false;
  }
  // 弹幕结束时间不能晚于活动结束时间
  const isEnd: boolean = dayjs(formData.bulletScreenEndTime).isAfter(dayjs(formData.endTime));
  if (isEnd) {
    Message.error('弹幕结束时间需要早于或等于活动结束时间');
    return false;
  }

  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < prizeList.length; i++) {
    if (!isPrizeValid(prizeList[i], formData)) {
      return false;
    }
  }
  return true;
};
const hasPrize = (prizeList: PrizeInfo[]): boolean => {
  if (!prizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  for (const prize of prizeList) {
    if (!prize.giftId) {
      Message.error(`请设置${prize.prizeName}的奖品ID`);
      return false;
    }
  }
  return true;
};

export const checkActivityData = (prizeList: PrizeInfo[], formData: PageData): boolean => {
  if (!isProcessingEditType()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 没有选择奖品
  if (!hasPrize(prizeList)) {
    return false;
  }

  // 奖品时间与活动时间冲突
  if (!arePrizesValid(prizeList, formData)) {
    return false;
  }
  if (!bulletScreenWordCountValid(formData)) {
    return false;
  }

  return true;
};
