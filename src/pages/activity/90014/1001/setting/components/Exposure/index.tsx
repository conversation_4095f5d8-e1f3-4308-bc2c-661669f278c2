import React, { useReducer, useImperativeHandle, useEffect } from 'react';
import { Form, Field, Radio, Input, Grid } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData } from '@/pages/activity/90014/1001/util';
import ChooseGoods from '@/components/ChooseGoods';
import styles from './index.module.scss';
// import DraggingProducts from '@/components/DraggingProducts';
import SkuList from '@/components/SkuList';

const FormItem = Form.Item;
const RadioGroup = Radio.Group;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const field: Field = Field.useField();

  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const handleSkuChange = (data) => {
    setData({ exposureSkuList: data });
    field.setErrors({ exposureSkuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.exposureSkuList.splice(index, 1);
    setData({ exposureSkuList: formData.exposureSkuList });
  };

  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const handlePreview = (data) => {
    setData({ skuListPreview: data });
  };
  return (
    <div>
      <LzPanel title="曝光商品">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="是否添加曝光商品" required>
            <RadioGroup
              value={formData.isExposure}
              onChange={(isExposure: number) => {
                if (!isExposure) {
                  formData.exposureSkuList = [];
                  formData.skuListPreview = [];
                }
                setData({ isExposure, exposureSkuList: formData.exposureSkuList });
              }}
            >
              <Radio id="1" value={1}>
                是
              </Radio>
              <Radio id="0" value={0}>
                否
              </Radio>
            </RadioGroup>
            <Grid.Row>
              {formData.isExposure === 1 && (
                <FormItem
                  name="exposureSkuList"
                  required
                  requiredMessage={'请选择曝光商品'}
                  style={{ marginTop: '15px' }}
                >
                  <ChooseGoods value={formData.exposureSkuList} onChange={handleSkuChange} max={300} />
                  {formData.exposureSkuList.length !== 0 && (
                    <SkuList skuList={formData.exposureSkuList} handlePreview={handlePreview} removeSku={removeSku} />

                    // <DraggingProducts
                    //   exposureSkuList={formData.exposureSkuList}
                    //   handleSkuChange={handleSkuChange}
                    //   removeSku={removeSku}
                    // />
                  )}
                  <p className={styles.tip}>注：商品价格每天凌晨同步;</p>
                  <Input className="validateInput" name="exposureSkuList" value={formData.exposureSkuList} />
                </FormItem>
              )}
            </Grid.Row>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
