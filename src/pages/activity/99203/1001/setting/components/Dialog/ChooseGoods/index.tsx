import React, { useEffect, useState } from 'react';
import { Button, Tab, Icon, Select, Form } from '@alifd/next';
import SkuSelector from './components/SkuSelector';
// import SkuExport from './components/SkuExport';
import SelectedProduct from './components/SelectedProduct';
import LzDialog from '@/components/LzDialog';
import SkuImport from '@/components/ChooseGoods/components/SkuImport';
import LineImport from '@/components/ChooseGoods/components/LineImport';

const emptyFunction = () => {};

interface SelectOptions {
  label: string;
  value: string;
}

export default (props) => {
  const { defaultValue, disabled, value, max = 0, onChange = emptyFunction, mode = 'multiple' } = props;
  const [visible, setVisible] = useState(false);
  const [skus, setSkus] = useState(defaultValue || []);
  const [selectList, setSelectList] = useState([]);
  const [filterList, setFilterList] = useState([]);
  const [selectQuantity, setSelectQuantity] = useState(0);
  const [activeKey, setActiveKey] = useState<string>('1');

  const itemRender = (item: { label: string }) => {
    const { label } = item;
    return (
      <>
        <span>{label}</span>
      </>
    );
  };
  const onChooseGoodsClick = () => {
    setSelectList(skus);
    setSelectQuantity(skus.length);
    setVisible(true);
  };

  const onCloseDialog = () => {
    setVisible(false);
  };

  const onSelectedRow = (data) => {
    data.forEach((val, idx) => {
      val.seq = idx;
    });
    onChange(data);
    setSkus(data);
    setVisible(false);
  };

  // 获取选择商品
  const getSelectList = (data) => {
    setSelectList(data);
    setSelectQuantity(data.length);
  };

  // 获取上传数据
  const uploadList = (data) => {
    setSelectList(data);
    setFilterList(data);
    setSelectQuantity(data.length);
  };

  // 获取选择商品
  const getHandleResult = (data) => {
    setSelectList(data);
    setSelectQuantity(data.length);
    setFilterList(data);
  };

  useEffect(() => {
    setSkus(value || []);
  }, [value]);

  return (
    <span>
      <>
        <Button type="primary" disabled={disabled} onClick={onChooseGoodsClick}>
          <Icon type="add" />
          添加商品{max > 0 ? (skus && skus.length > 0 ? `（${skus.length}/${max}）` : `（0/${max}）`) : ''}
        </Button>
      </>
      {/* 选择选择商品弹窗 */}
      <LzDialog
        title={false}
        className="lz-dialog-medium"
        style={{ width: 921 }}
        visible={visible}
        onClose={onCloseDialog}
        onCancel={onCloseDialog}
        footer={false}
      >
        <div style={{ maxHeight: '650px' }}>
          <Tab size="small" activeKey={activeKey} onChange={(key) => setActiveKey(key)}>
            <Tab.Item key="1" title="选择商品">
              <SkuSelector
                max={max}
                value={skus}
                filterList={filterList}
                getSelectList={getSelectList}
                confirm={onSelectedRow}
                cancel={onCloseDialog}
                mode={mode}
              />
            </Tab.Item>
            <Tab.Item key="2" title="导入商品">
              <SkuImport
                selectList={selectList}
                uploadList={uploadList}
                cancel={onCloseDialog}
                confirm={onSelectedRow}
              />
            </Tab.Item>
            <Tab.Item key="3" title="导入产品线">
              <LineImport uploadList={uploadList} cancel={onCloseDialog} confirm={onSelectedRow} />
            </Tab.Item>
            <Tab.Item key="4" title={`已选商品 (${selectQuantity}件)`}>
              <SelectedProduct
                max={max}
                selectList={selectList}
                getHandleResult={getHandleResult}
                confirm={onSelectedRow}
                cancel={onCloseDialog}
              />
            </Tab.Item>
          </Tab>
        </div>
      </LzDialog>
    </span>
  );
};
