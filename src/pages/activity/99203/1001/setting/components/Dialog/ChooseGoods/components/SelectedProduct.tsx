import React, { useEffect, useState } from 'react';
import { Box, Button, Table } from '@alifd/next';
import styles from '../index.module.scss';

export default ({ max = 0, selectList, getHandleResult, confirm, cancel }) => {
  const [loading] = useState(false);
  const [list, setList] = useState(selectList);

  // 选中的key列表
  const [selectedRowKeys, setSelectedRowKeys] = useState(selectList?.map((sku) => sku.skuId));

  function arrDistinctByProp(arr, prop) {
    return arr.filter(function (item, index, self) {
      return self.findIndex((el) => el[prop] === item[prop]) === index;
    });
  }

  // 确定
  const handleConfirm = () => {
    const res = arrDistinctByProp(list, 'skuId');
    const filterList = res.filter((sku) => selectedRowKeys.includes(sku.skuId));
    confirm(filterList);
  };

  // 删除
  const deleteSelected = (val) => {
    const listStore = list.filter((item) => item.skuId !== val.skuId);
    setSelectedRowKeys(listStore?.map((sku) => sku.skuId));
    setList(listStore);
    getHandleResult(listStore);
  };

  useEffect(() => {
    setList(selectList);
    setSelectedRowKeys(selectList?.map((sku) => sku.skuId));
  }, [selectList]);

  return (
    <div>
      <Table.StickyLock loading={loading} primaryKey="skuId" fixedHeader maxBodyHeight={420} dataSource={list}>
        <Table.Column
          title="商品名称"
          cell={(val, index, record) => (
            <div className={styles.part1} style={{ alignItems: 'center' }}>
              <div className={styles.part1_p1}>{record.skuName}</div>
            </div>
          )}
        />
        <Table.Column
          title="京东价（元）"
          width={120}
          cell={(val, index, info) => <span>{new Intl.NumberFormat().format(info.jdPrice)}</span>}
        />
        <Table.Column
          title="操作"
          width={120}
          cell={(val, index, info) => (
            <span style={{ color: '#39f', cursor: 'pointer' }} onClick={() => deleteSelected(info)}>
              删除
            </span>
          )}
        />
      </Table.StickyLock>
      <br />
      <Box direction="row" justify="flex-end" align="center" spacing={10}>
        <Button
          type="primary"
          onClick={handleConfirm}
          disabled={max > 0 && (!selectedRowKeys.length || selectedRowKeys.length > max)}
        >
          确定({`${selectedRowKeys.length}${max > 0 ? `/${max}` : ''} `})
        </Button>
        <Button type="normal" onClick={cancel}>
          取消
        </Button>
      </Box>
    </div>
  );
};
