import styles from '../index.module.scss';
import { Button, Form, Input, NumberPicker, Message, Loading, Radio, Field, Icon, Grid } from '@alifd/next';
import { useState } from 'react';
import * as React from 'react';
import { ComponentProps, CreateProduct } from '../types';
import { addResPrizeSku } from '@/api/prize';
import LzDialog from '@/components/LzDialog';
import SkuSelector from '@/components/ChoosePrize/JdProduct/components/SkuSelector';
import constant from '@/utils/constant';
import LzJDImageSelector from '@/components/LzJDImageSelector';

export default ({ handleCancel, handleSubmit }: ComponentProps) => {
  const field = Field.useField();
  const formItemLayout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };
  const [loading, setLoading] = React.useState<boolean>(false);

  const [productValue, setProductValue] = useState(0);
  const [wmsCode, setWmsCode] = useState(); // 商家内部编号

  const [chooseSkuVisible, setChooseSkuVisible] = useState<boolean>(false);
  const [skus] = useState<any[]>([]);
  const [lzJDImageSelectorVisible, setLzJDImageSelectorVisible] = useState(false); // 选择图片弹窗
  const [srcForParams, setSrcForParams] = useState(''); // 选择后传给后端的链接
  const validateWmsCode = (value) => {
    if (!/^[0-9a-zA-Z]*$/g.test(value)) {
      value = wmsCode;
    }
    console.log(value);
    setWmsCode(value);
  };

  const onOkCreate = (value: CreateProduct, errors: unknown): void | boolean => {
    if (!errors) {
      if (+value.quantityTotal <= 0) {
        Message.error('库存总量必须大于0');
        return false;
      }
      setLoading(true);
      addResPrizeSku(value)
        .then(() => {
          Message.success('添加成功');
          setLoading(false);
          handleSubmit();
        })
        .catch((err) => {
          setLoading(false);
          Message.error(err.message);
        });
    }
  };
  // 关闭弹窗
  const onCloseCreate = () => {
    handleCancel();
  };

  return (
    <div className={styles.CreateJdBeanPlan}>
      <Loading visible={loading}>
        <Form {...formItemLayout} style={{ width: 600 }} field={field}>
          <Form.Item label="商品来源：" className={styles.item}>
            <Radio.Group
              name="source"
              defaultValue={1}
              onChange={(val) => {
                if (val === 2) {
                  setChooseSkuVisible(true);
                }
              }}
            >
              <Radio value={1}>新建商品</Radio>
              <Radio
                value={2}
                onClick={() => {
                  setChooseSkuVisible(true);
                }}
              >
                选择店铺商品
              </Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item label="实物名称：" required className={styles.item} requiredMessage="请输入实物名称">
            <Input trim placeholder="请输入实物名称" maxLength={20} showLimitHint name="skuName" />
          </Form.Item>
          <Form.Item label="商家内部编号：" className={styles.item}>
            <Input
              trim
              placeholder="用于与商家发货系统对接时使用，作为唯一发货标识"
              maxLength={20}
              showLimitHint
              name="wmsCode"
              value={wmsCode}
              onChange={(e) => validateWmsCode(e)}
            />
          </Form.Item>
          <Form.Item label="实物描述：" className={styles.item}>
            <Input.TextArea placeholder="请输入实物描述" maxLength={200} showLimitHint name="demo" />
          </Form.Item>
          <Form.Item label="实物图片：">
            <Input htmlType="hidden" name="skuMainPicture" value={srcForParams} />
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <div>
                  <div
                    className={styles.uploaderSelector}
                    onClick={() => {
                      setLzJDImageSelectorVisible(true);
                    }}
                  >
                    {srcForParams && <img className={styles.previewImg} src={srcForParams} alt="" />}
                    {!srcForParams && (
                      <>
                        <Icon className={styles.icon} type="add" />
                        <div className={styles.tip}>选择图片</div>
                      </>
                    )}
                  </div>
                  {lzJDImageSelectorVisible && (
                    <LzDialog
                      title="选择图片"
                      className="lz-dialog-larger"
                      visible={lzJDImageSelectorVisible}
                      footer={false}
                      onCancel={() => setLzJDImageSelectorVisible(false)}
                      onClose={() => setLzJDImageSelectorVisible(false)}
                    >
                      <LzJDImageSelector
                        width={200}
                        height={200}
                        onCloseTree={() => setLzJDImageSelectorVisible(false)}
                        onSelect={(url) => {
                          setSrcForParams(`${constant.IMAGE_PREFIX}${url}`);
                          setLzJDImageSelectorVisible(false);
                        }}
                        onCancel={() => setLzJDImageSelectorVisible(false)}
                      />
                    </LzDialog>
                  )}
                </div>
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：200px*200px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setSrcForParams('');
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="库存总量：" required requiredMessage="请输入库存总量">
            <NumberPicker
              value={productValue}
              onChange={(quantityTotal) => setProductValue(quantityTotal)}
              type="inline"
              min={0}
              max={9999999}
              name="quantityTotal"
              style={{ width: '200px' }}
            />
            件
          </Form.Item>
          <Form.Item label=" ">
            <Form.Submit validate type="primary" className={styles.form_btn} onClick={onOkCreate}>
              提交
            </Form.Submit>
            <Button className={styles.form_btn} onClick={onCloseCreate} style={{ marginLeft: '15px' }}>
              取消
            </Button>
          </Form.Item>
        </Form>
      </Loading>
      <LzDialog
        title="选择店铺商品"
        visible={chooseSkuVisible}
        footer={false}
        onCancel={() => setChooseSkuVisible(false)}
        onClose={() => setChooseSkuVisible(false)}
        width={800}
      >
        <SkuSelector
          max={1}
          value={skus}
          filterList={[]}
          getSelectList={(val) => {
            let { skuName } = val[0];
            // 长度最长保留20个字符
            if (skuName.length > 20) {
              skuName = skuName.substring(0, 20);
            }
            field.setValue('skuName', skuName);
            field.setValue('skuMainPicture', val[0].skuMainPicture || '');
            setSrcForParams(val[0].skuMainPicture ? val[0].skuMainPicture.replace('n0/jfs', 'n1/s200x200_jfs') : '');
            setChooseSkuVisible(false);
          }}
        />
      </LzDialog>
    </div>
  );
};
