import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import {
  Form,
  Field,
  Radio,
  NumberPicker,
  Button,
  Table,
  Dialog,
  Select,
  Box,
  Checkbox,
  TimePicker2,
  DatePicker2,
  Balloon,
  Icon,
  Input,
} from '@alifd/next';
import styles from '../../style.module.scss';
import { activityEditDisabled } from '@/utils';
import { FormLayout, PageData } from '../../../util';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '@/components/ChoosePrize';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';
import ChooseGoods from '@/components/ChooseGoods';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const { venderType } = JSON.parse(localStorage.getItem('LZ_CURRENT_SHOP') || '{}');
  const shopInfo = [
    {
      rightsTitle: 'POP',
      selectGoodsLabel: '折扣商品',
      selectGoodsRequiredMessage: '请选择折扣商品',
      exchangePointLabel: '兑换所需积分',
      exchangePointRequiredMessage: '请输入兑换所需积分',
    },
    {
      rightsTitle: '自营',
      selectGoodsLabel: '选择令牌',
      selectGoodsRequiredMessage: '请选择令牌',
      exchangePointLabel: '兑换消耗',
      exchangePointRequiredMessage: '请输入兑换所需的积分数量',
    },
  ];
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  // 选择兑换商品弹窗
  const [visible, setVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新奖品信息
    formData.prizeInfo = data;
    if (data?.prizeType === 1) {
      formData.exchangeLimit = 1;
      formData.exchangeNum = 1;
    } else {
      formData.exchangeLimit = 0;
      formData.exchangeNum = '';
    }
    setData(formData);
    setVisible(false);
  };
  const onCancel = (): void => {
    setVisible(false);
  };
  const handleCheckboxChange = (isChecked) => {
    setData({ sameTermOnce: isChecked });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const getDisabledHours = (item: any) => {
    const hours = Array.from({ length: 24 }, (v, k) => k);
    const filteredHours = hours.filter((e) => e <= item.exchangeCycleStartTime.split(':')[0] - 1);
    return (index: number) => {
      return filteredHours.includes(index);
    };
  };

  const getDisabledMinutes = (item: any) => {
    const minutes = Array.from({ length: 60 }, (v, k) => k);
    const filteredMinutes = minutes.filter((e) => e <= item.exchangeCycleStartTime.split(':')[1] - 1);
    // 如果开始时间和结束时间是小时，结束时间的分钟要大于开始时间的分钟
    if (item.exchangeCycleStartTime.split(':')[0] === item.exchangeCycleEndTime.split(':')[0]) {
      return (index: number) => {
        return filteredMinutes.includes(index);
      };
    }
  };

  const getDisabledSeconds = (item: any) => {
    const seconds = Array.from({ length: 60 }, (v, k) => k);
    const filteredSeconds = seconds.filter((e) => e <= item.exchangeCycleStartTime.split(':')[2]);
    if (
      item.exchangeCycleStartTime.split(':')[0] === item.exchangeCycleEndTime.split(':')[0] &&
      item.exchangeCycleStartTime.split(':')[1] === item.exchangeCycleEndTime.split(':')[1]
    ) {
      return (index: number) => {
        return filteredSeconds.includes(index);
      };
    }
  };

  useEffect((): void => {
    setData({ prizeInfo: formData.prizeInfo ? formData.prizeInfo : null });
  }, []);
  /**
   * 创建会员等级兑换时间设置
   * @param data
   */
  const createMemberExchangeTimeSettingMap = (data: any) => {
    const { levelsLimit, gradeLabel } = data;
    return gradeLabel
      .filter((it: any) => it !== '关注店铺用户')
      .map((item: any, index: number) => {
        return (
          formData.levelExchangeTime.find((e: any) => e.levelName === item) ?? {
            levelName: item,
            level: levelsLimit.split(',')[index],
            levelExchangeStartTime: (dayjs().add(1, 'day') as any).format('YYYY-MM-DD 00:00:00'),
            levelExchangeEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
            exchangeCycle: 2,
            exchangeCycleStartTime: dayjs().format('HH:mm:ss'),
            exchangeCycleEndTime: dayjs().add(2, 'hour').format('HH:mm:ss'),
          }
        );
      });
  };
  const exchangePoint = (data: any) => {
    const { levelsLimit, gradeLabel } = data;
    return gradeLabel
      .filter((it: any) => it !== '关注店铺用户')
      .map((item: any, index: number) => {
        return (
          formData.exchangePoint.find((e: any) => e.levelName === item) ?? {
            levelName: item,
            level: levelsLimit.split(',')[index],
            points: '',
          }
        );
      });
  };
  const onSubmit = (prizeInfo: any) => {
    field.setErrors({
      prizeInfo: prizeInfo ? '' : shopInfo[venderType].selectGoodsRequiredMessage,
      rightsName: prizeInfo?.prizeName ? '' : '请输入权益名称',
    });
    setData({ prizeInfo, rightsName: prizeInfo?.prizeName });
  };

  useEffect((): void => {
    const hasLevel = formData.levelsLimit?.split(',').some((e: any) => e > 0);
    if (hasLevel) {
      setData({
        levelExchangeTime: createMemberExchangeTimeSettingMap(formData),
        exchangePoint: exchangePoint(formData),
      });
    } else {
      setData({
        levelExchangeTimeLimit: 1,
      });
    }
  }, [formData.gradeLabel || formData.levelsLimit]);
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  return (
    <div>
      <LzPanel title={'兑换设置'}>
        <Form {...formItemLayout} field={field}>
          <FormItem
            label={shopInfo[venderType].selectGoodsLabel}
            required
            requiredMessage={shopInfo[venderType].selectGoodsRequiredMessage}
          >
            <div>
              <ChoosePromotion
                submit={onSubmit}
                value={formData.prizeInfo ?? null}
                disabled={activityEditDisabled()}
                name="prizeInfo"
              />
              <div className={styles['choose-promotion-tips']}>
                <div>
                  注意：在申请令牌时，注意勾选允许使用1次，避免一个令牌被多次使用
                  <Balloon.Tooltip
                    style={{ minWidth: 940 }}
                    v2
                    trigger={<Icon type="help" size="small" className={styles['help-icon-style']} />}
                    align="b"
                  >
                    <img
                      alt=""
                      className={styles['ballon-img']}
                      src="https://img10.360buyimg.com/imgzone/jfs/t1/159093/33/22468/221348/61b9ae59Ef356fcf7/03c00f8deed59000.png"
                    />
                  </Balloon.Tooltip>
                </div>
                <div className={styles['choose-promotion-tips-line2']}>
                  如果此令牌在不同时间段使用在多个活动中，则通过其中1个活动加入令牌的用户不能再参加其他积分加钱购活动
                </div>
              </div>
            </div>
          </FormItem>
          <>
            <FormItem label="选择商品" required>
              <div>
                <ChooseGoods
                  mode="single"
                  // @ts-ignore
                  name="skuInfo"
                  max={1}
                  disabled={activityEditDisabled()}
                  value={formData.skuInfo}
                  onChange={(skuInfo: any) => setData({ skuInfo })}
                />
                {formData.skuInfo.length !== 0 && (
                  <Table.StickyLock
                    fixedHeader
                    style={{ marginTop: '10px', width: '500px' }}
                    dataSource={formData.skuInfo}
                  >
                    <Table.Column
                      title="商品名称"
                      dataIndex="skuName"
                      cell={(val, index, record) => {
                        return (
                          <div className={styles['choose-goods-box']}>
                            <img src={record.skuMainPicture} alt="" className={styles['goods-img']} />
                            <div>
                              <div>{record.skuName}</div>
                              <div className={styles['choose-goods-tips']}>商品编码{record.skuId}</div>
                            </div>
                          </div>
                        );
                      }}
                    />
                    <Table.Column title="京东价(元)" dataIndex="jdPrice" width={100} />
                  </Table.StickyLock>
                )}
                <div className={styles['choose-promotion-tips']}>
                  注意：商品必须选择与令牌绑定的SKU，且设置了SKU限购数量，否则用户会无限购买
                </div>
              </div>
            </FormItem>
            <FormItem label="权益名称" required requiredMessage="请输入权益名称">
              <Input
                showLimitHint
                maxLength={100}
                className={styles['input-width1']}
                name="rightsName"
                placeholder={'请输入权益名称'}
                disabled={activityEditDisabled()}
                value={formData.rightsName}
                onChange={(rightsName) => setData({ rightsName })}
              />
              <div className={styles['choose-goods-tips']}>注：请填写易于识别的权益名称，如折扣商品</div>
            </FormItem>
            <FormItem label="划线价" required requiredMessage="请输入划线价">
              <NumberPicker
                name="salePrice"
                precision={2}
                hasTrigger={false}
                max={999999999}
                placeholder={'请输入划线价'}
                className={styles['input-width']}
                value={formData.salePrice}
                onChange={(salePrice) => setData({ salePrice })}
                disabled={activityEditDisabled()}
              />
              <span className={styles['unit-style']}>元</span>
              {formData?.skuInfo[0]?.jdPrice && <span>(京东价：{formData?.skuInfo[0]?.jdPrice})</span>}
            </FormItem>
          </>
          <FormItem label="兑换所需积分" required>
            <div>
              <Box style={{ float: 'left' }}>
                <Select
                  className={styles['cycle-select']}
                  name="exchangeCycle"
                  disabled={activityEditDisabled()}
                  value={formData.exchangePointType}
                  onChange={(exchangePointType) => {
                    setData({
                      exchangePointType,
                      exchangePoint: exchangePointType == '2' ? exchangePoint(formData) : [],
                      unifyExchangePoint: '',
                    });
                  }}
                >
                  <Select.Option value={'1'}>统一设置</Select.Option>
                  <Select.Option value={'2'}>按等级设置</Select.Option>
                </Select>
              </Box>
              {formData.exchangePointType === '1' && (
                <Box style={{ float: 'left' }}>
                  <Form.Item required requiredMessage={'请输入兑换所需积分'}>
                    <NumberPicker
                      className={styles['exchange-input']}
                      disabled={activityEditDisabled()}
                      max={999999999}
                      min={1}
                      placeholder="请输入兑换所需积分"
                      name="unifyExchangePoint"
                      hasTrigger={false}
                      value={formData.unifyExchangePoint}
                      onChange={(unifyExchangePoint) => setData({ unifyExchangePoint })}
                    />
                    积分
                  </Form.Item>
                </Box>
              )}
              {formData.exchangePointType === '2' && (
                <Box className={styles['point-list-box']}>
                  <Form {...formItemLayout} field={field}>
                    {formData.exchangePoint.map((item, index) => {
                      return (
                        <Form.Item
                          key={`${index}_${item.levelName}`}
                          required
                          requiredMessage={`请输入${item.levelName}兑换所需积分`}
                          label={item.levelName}
                          labelTextAlign={'right'}
                          labelAlign={'left'}
                          colon
                        >
                          <NumberPicker
                            style={{ width: '200px' }}
                            disabled={activityEditDisabled()}
                            placeholder={`请输入${item.levelName}兑换所需积分`}
                            max={999999999}
                            min={1}
                            name={`${index}_${item.levelName}`}
                            hasTrigger={false}
                            value={item.points}
                            onChange={(points) => {
                              formData.exchangePoint[index].points = points;
                              setData(formData);
                            }}
                          />
                          积分
                        </Form.Item>
                      );
                    })}
                  </Form>
                </Box>
              )}
            </div>
          </FormItem>
          <FormItem label={'用户购买的折扣价格'} required requiredMessage="请输入用户购买的折扣价格">
            <NumberPicker
              name="conversionPrice"
              max={999999999}
              precision={2}
              hasTrigger={false}
              placeholder="请输入用户购买的折扣价格"
              className={styles['input-width']}
              disabled={activityEditDisabled()}
              value={formData.conversionPrice}
              onChange={(conversionPrice) => setData({ conversionPrice })}
            />
            <span className={styles['unit-style']}>元</span>
            <div className={styles['choose-promotion-tips']}>注：申请令牌时关联商品的折扣价</div>
          </FormItem>
          <FormItem label="活动兑换总量" required requiredMessage="请输入活动兑换总量">
            <NumberPicker
              name="totalExchangeAmount"
              hasTrigger={false}
              max={999999999}
              placeholder="请输入活动兑换总量"
              className={styles['input-width']}
              value={formData.totalExchangeAmount}
              onChange={(totalExchangeAmount) => setData({ totalExchangeAmount })}
              disabled={activityEditDisabled()}
            />
            <span className={styles['unit-style']}>份</span>
          </FormItem>
          <FormItem label="活动每日最多可兑" required>
            <Radio.Group
              value={formData.exchangeType}
              onChange={(exchangeType) => setData({ exchangeType, prizeCycleTotalNum: '' })}
              disabled={activityEditDisabled()}
              className={styles['flex-box']}
            >
              <Radio value={false}>不限制</Radio>
              <Radio value>限制</Radio>
              {formData.exchangeType && (
                <>
                  <Form.Item required requiredMessage="请输入活动每日最多可兑数量">
                    <NumberPicker
                      style={{ width: '200px' }}
                      disabled={activityEditDisabled()}
                      placeholder="请输入活动每日最多可兑数量"
                      name="prizeCycleTotalNum"
                      hasTrigger={false}
                      max={formData.totalExchangeAmount}
                      value={formData.prizeCycleTotalNum}
                      onChange={(prizeCycleTotalNum) => setData({ prizeCycleTotalNum })}
                    />
                    份
                  </Form.Item>
                </>
              )}
            </Radio.Group>
          </FormItem>
          <FormItem label="会员等级兑换时间" required>
            <Radio.Group
              disabled={activityEditDisabled()}
              value={formData.levelExchangeTimeLimit}
              onChange={(levelExchangeTimeLimit) =>
                setData({
                  levelExchangeTimeLimit,
                  levelExchangeTime: levelExchangeTimeLimit === 2 ? createMemberExchangeTimeSettingMap(formData) : [],
                })
              }
              className={styles['flex-box']}
            >
              <Radio value={1}>关闭</Radio>
              <Radio disabled={!formData.levelsLimit?.split(',').some((e: any) => e > 0)} value={2}>
                开启
              </Radio>
            </Radio.Group>
          </FormItem>
          {formData.levelExchangeTimeLimit === 1 && (
            <Form.Item label="可兑周期">
              <div className={styles['flex-box']}>
                <Select
                  disabled={activityEditDisabled()}
                  className={styles['cycle-select']}
                  name="exchangeCycle"
                  value={formData.exchangeCycle}
                  onChange={(exchangeCycle) => setData({ exchangeCycle })}
                >
                  <Select.Option value={1}>活动同时</Select.Option>
                  <Select.Option value={2}>每天</Select.Option>
                </Select>
                {formData.exchangeCycle === 2 && (
                  <div>
                    <TimePicker2
                      hasClear={false}
                      disabled={activityEditDisabled()}
                      placeholder="开始时间"
                      className={styles['select-time']}
                      value={formData.exchangeCycleStartTime}
                      onChange={(exchangeCycleStartTime) =>
                        setData({ exchangeCycleStartTime: dayjs(exchangeCycleStartTime).format('HH:mm:ss') })
                      }
                    />
                    <span className={styles['select-time-line']}>-</span>
                    <TimePicker2
                      hasClear={false}
                      disabled={activityEditDisabled()}
                      disabledHours={getDisabledHours(formData)}
                      disabledMinutes={getDisabledMinutes(formData)}
                      disabledSeconds={getDisabledSeconds(formData)}
                      placeholder="结束时间"
                      className={styles['select-time']}
                      value={formData.exchangeCycleEndTime}
                      onChange={(exchangeCycleEndTime) =>
                        setData({ exchangeCycleEndTime: dayjs(exchangeCycleEndTime).format('HH:mm:ss') })
                      }
                    />
                  </div>
                )}
              </div>
            </Form.Item>
          )}
          {formData.levelExchangeTimeLimit === 2 &&
            formData.levelExchangeTime &&
            formData.levelExchangeTime.map((item, index) => {
              return (
                <Form.Item label=" " colon={false} key={item.label}>
                  <div className={styles['flex-box']} style={{ alignItems: 'center' }}>
                    <div style={{ margin: '0 5px', width: '80px', textAlign: 'right' }}>{item.levelName}</div>
                    <RangePicker
                      disabled={activityEditDisabled()}
                      className="w-300"
                      name="rangeDate"
                      inputReadOnly
                      followTrigger
                      format={dateFormat}
                      hasClear={false}
                      showTime
                      value={[new Date(item.levelExchangeStartTime), new Date(item.levelExchangeEndTime)]}
                      onChange={(rangeDate) => {
                        formData.levelExchangeTime[index].levelExchangeStartTime = format.formatDateTimeDayjs(
                          rangeDate[0],
                        );
                        formData.levelExchangeTime[index].levelExchangeEndTime = format.formatDateTimeDayjs(
                          rangeDate[1],
                        );
                        setData(formData);
                      }}
                      disabledDate={(date) => {
                        return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
                      }}
                    />
                    <div style={{ margin: '0 5px', whiteSpace: 'nowrap' }}>可兑周期</div>
                    <Select
                      disabled={activityEditDisabled()}
                      className={styles['cycle-select']}
                      name="exchangeCycle"
                      value={item.exchangeCycle}
                      onChange={(exchangeCycle) => {
                        formData.levelExchangeTime[index].exchangeCycle = exchangeCycle;
                        setData(formData);
                      }}
                    >
                      <Select.Option value={1}>等级兑换时间同步</Select.Option>
                      <Select.Option value={2}>每天</Select.Option>
                    </Select>
                    {item.exchangeCycle === 2 && (
                      <div>
                        <TimePicker2
                          hasClear={false}
                          disabled={activityEditDisabled()}
                          placeholder="开始时间"
                          className={styles['select-time']}
                          value={item.exchangeCycleStartTime}
                          onChange={(exchangeCycleStartTime) => {
                            formData.levelExchangeTime[index].exchangeCycleStartTime =
                              dayjs(exchangeCycleStartTime).format('HH:mm:ss');
                            setData(formData);
                          }}
                        />
                        <span className={styles['select-time-line']}>-</span>
                        <TimePicker2
                          hasClear={false}
                          disabled={activityEditDisabled()}
                          disabledHours={getDisabledHours(item)}
                          disabledMinutes={getDisabledMinutes(item)}
                          disabledSeconds={getDisabledSeconds(item)}
                          placeholder="结束时间"
                          className={styles['select-time']}
                          value={item.exchangeCycleEndTime}
                          onChange={(exchangeCycleEndTime) => {
                            formData.levelExchangeTime[index].exchangeCycleEndTime =
                              dayjs(exchangeCycleEndTime).format('HH:mm:ss');
                            setData(formData);
                          }}
                        />
                      </div>
                    )}
                  </div>
                </Form.Item>
              );
            })}
          <Form.Item label="会员兑换限制" required requiredMessage="请选择会员兑换限制">
            <Radio.Group
              value={formData.exchangeLimit}
              disabled={activityEditDisabled()}
              onChange={(exchangeLimit) => setData({ exchangeLimit })}
            >
              <Radio value={1}>活动期限内可兑换1次</Radio>
            </Radio.Group>
          </Form.Item>
          {/* <FormItem label="兑换限制" required> */}
          {/*  <Radio.Group */}
          {/*    value={formData.exchangeLimit} */}
          {/*    onChange={(exchangeLimit) => { */}
          {/*      setData({ exchangeLimit, exchangeNum: '' }); */}
          {/*      field.setErrors({ exchangeNum1: '', exchangeNum2: '' }); */}
          {/*    }} */}
          {/*    disabled={activityEditDisabled()} */}
          {/*    className={styles['flex-box']} */}
          {/*  > */}
          {/*    <Radio value={0}>不限制</Radio> */}
          {/*    <Radio value={1}> */}
          {/*      <FormItem */}
          {/*        requiredMessage={'请输入活动期每人限兑换次数'} */}
          {/*        required={formData.exchangeLimit == 1} */}
          {/*        className={styles.formItemLine} */}
          {/*      > */}
          {/*        活动期每人限兑换 */}
          {/*        <NumberPicker */}
          {/*          className={styles['exchange-limit']} */}
          {/*          disabled={formData.exchangeLimit !== 1 || activityEditDisabled()} */}
          {/*          hasTrigger={false} */}
          {/*          max={999999999} */}
          {/*          min={1} */}
          {/*          name={'exchangeNum1'} */}
          {/*          value={formData.exchangeLimit === 1 ? formData.exchangeNum : ''} */}
          {/*          onChange={(exchangeNum) => setData({ exchangeNum })} */}
          {/*        /> */}
          {/*        次 */}
          {/*      </FormItem> */}
          {/*    </Radio> */}
          {/*    <Radio value={2}> */}
          {/*      <FormItem */}
          {/*        requiredMessage={'请输入活动期每人每天限兑换次数'} */}
          {/*        required={formData.exchangeLimit == 2} */}
          {/*        className={styles.formItemLine} */}
          {/*      > */}
          {/*        活动期每人每天限兑换 */}
          {/*        <NumberPicker */}
          {/*          className={styles['exchange-limit']} */}
          {/*          disabled={formData.exchangeLimit !== 2 || activityEditDisabled()} */}
          {/*          hasTrigger={false} */}
          {/*          max={999999999} */}
          {/*          min={1} */}
          {/*          name={'exchangeNum2'} */}
          {/*          value={formData.exchangeLimit === 2 ? formData.exchangeNum : ''} */}
          {/*          onChange={(exchangeNum) => setData({ exchangeNum })} */}
          {/*        /> */}
          {/*        次 */}
          {/*      </FormItem> */}
          {/*    </Radio> */}
          {/*  </Radio.Group> */}
          {/* </FormItem> */}
          <FormItem
            label="同期活动限制"
            extra={
              <div className="next-form-item-help">
                同期活动概念：1.活动开始时间和结束时间相同；2.活动配置时都勾选了“同期活动限制”
              </div>
            }
          >
            <div>
              <Checkbox
                key={formData.sameTermOnce}
                disabled={activityEditDisabled()}
                checked={formData.sameTermOnce === true}
                label={'同期内所有奖品仅限兑换1次'}
                onChange={(isChecked) => handleCheckboxChange(isChecked)}
              />
            </div>
          </FormItem>
          <Form.Item label="活动自动停止">
            <Checkbox
              checked={formData.autoStopAct}
              onChange={(autoStopAct) => setData({ autoStopAct, endActivity: autoStopAct })}
            >
              活动参与人数达到享受此优惠的最大人数活动自动停止
            </Checkbox>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          productImgTip
          hasLimit
          prizeNameLength={15}
          typeList={[3]}
          defaultTarget={3}
        />
      </LzDialog>
    </div>
  );
};
