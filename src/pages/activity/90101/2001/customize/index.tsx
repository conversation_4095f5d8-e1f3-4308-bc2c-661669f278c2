/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 设置模板
 */
import React from 'react';
import { CustomValue } from '../util';
// import LzTipPanel from '@/components/LzTipPanel';
// 基础信息
import Base from './components/Base';
// 大转盘信息
import Wheel from './components/Wheel';
// 获奖信息
import Award from './components/Award';
import { Tab } from '@alifd/next';
import AwardPopup from './components/AwardPopup';
import NoAwardPopup from './components/NoAwardPopup';
import RulePopup from './components/RulePopup';
import MyPrizePopup from './components/MyPrizePopup';
import SaveAddressPopup from './components/SaveAddressPopup';
import CopyCardPopup from './components/CopyCardPopup';
import NoChanceNumPopup from './components/NoChanceNumPopup';
import JoinPopup from './components/JoinPopup';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  showPopup: (boolean) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { target, defaultValue, value, handleChange, showPopup } = props;
  // 选中的tab
  const [drawerTabIndex, setDrawerTabIndex] = React.useState('1');

  console.log('target', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab
        onChange={(key): void => {
          setDrawerTabIndex(key);
          showPopup(key);
        }}
        activeKey={drawerTabIndex}
      >
        <Tab.Item title="主页" key="1">
          {<Base {...eventProps} />}
          {<Wheel {...eventProps} />}
          {<Award {...eventProps} />}
        </Tab.Item>
        <Tab.Item title="规则弹窗" key="rule">
          <RulePopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="我的奖品弹窗" key="myPrize">
          <MyPrizePopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="中奖弹窗" key="award">
          <AwardPopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="未中奖弹窗" key="noAward">
          <NoAwardPopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="保存地址弹窗" key="saveAddress">
          <SaveAddressPopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="展示卡密弹窗" key="copyCard">
          <CopyCardPopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="抽奖次数不足弹窗" key="noChanceNum">
          <NoChanceNumPopup {...eventProps} />
        </Tab.Item>
        <Tab.Item title="开卡弹窗" key="join">
          <JoinPopup {...eventProps} />
        </Tab.Item>
      </Tab>
    </div>
  );
};
