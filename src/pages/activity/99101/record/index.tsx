/**
 * 日历签到数据报表
 */
import React, { useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LotteryRecord from './components/LotteryRecord';
import ReportData from './components/ReportData';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="转段有礼数据报表">
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="领取记录" key="1">
            <LotteryRecord />
          </Tab.Item>
          <Tab.Item title="活动数据" key="2">
            <ReportData />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
