/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 设置模板
 */
import React from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';
import Series from './components/Series';
import { Tab } from '@alifd/next';
import Member from './components/Member';
import Rule from './components/Rule';
import MyPrize from './components/MyPrize';
import ConfirmPrize from './components/ConfirmPrize';
import ReceiveSuccess from './components/ReceiveSuccess';
import SaveAddress from './components/SaveAddress';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  handleChangeActiveKey: (activeKey: string) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { defaultValue, value, handleChange } = props;
  const [popupActiveKey, setPopupActiveKey] = React.useState('index');
  const handleTabChange = (key: string): void => {
    setPopupActiveKey(key);
    props.handleChangeActiveKey(key);
  };
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={popupActiveKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="主页" key="index">
          <Base {...eventProps} />
          <Series {...eventProps} />
        </Tab.Item>
        <Tab.Item title="入会弹窗" key="member">
          <Member {...eventProps} />
        </Tab.Item>
        <Tab.Item title="规则弹窗" key="rule">
          <Rule {...eventProps} />
        </Tab.Item>
        <Tab.Item title="我的奖品弹窗" key="myPrize">
          <MyPrize {...eventProps} />
        </Tab.Item>
        <Tab.Item title="奖项选择弹窗" key="confirm">
          <ConfirmPrize {...eventProps} />
        </Tab.Item>
        <Tab.Item title="领取成功弹窗" key="receive">
          <ReceiveSuccess {...eventProps} />
        </Tab.Item>
        <Tab.Item title="保存地址弹窗" key="saveAddress">
          <SaveAddress {...eventProps} />
        </Tab.Item>
      </Tab>
    </div>
  );
};
