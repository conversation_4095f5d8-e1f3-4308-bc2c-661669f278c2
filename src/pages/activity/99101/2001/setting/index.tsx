/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:03
 * Description: 活动设置
 */
import React, { useImperativeHandle, useRef, useReducer, useState, useEffect } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 领奖限制
import SeriesSetting from './components/SeriesSetting';
// 分享设置
import ShareInfo from './components/Share';
// 活动规则
import RulesInfo from './components/Rules';
import Order from './components/Order';
import Prizes from './components/Prizes';
import { PageData, CustomValue } from '../util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}

interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [renderBase] = useState(true);

  const onChange = (activityInfo, type?: string): void => {
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  const rulesRef = useRef<{ submit: () => void | null }>(null);
  const seriesSettingRef = useRef<{ submit: () => void | null }>(null);
  const orderRef = useRef<{ submit: () => void | null }>(null);
  const prizeSettingRef = useRef<{ submit: () => void | null }>(null);
  const [renderPrize, setRenderPrize] = useState(false);
  const checkForm = () => {
    const events: any[] = [
      baseRef.current!,
      orderRef.current,
      prizeSettingRef.current,
      seriesSettingRef.current,
      shareRef.current,
    ];
    return events.every((item, index: number): boolean => {
      const result = events[index].submit();
      if (result) {
        return false;
      }
      return true;
    });
  };

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current!,
        orderRef.current,
        prizeSettingRef.current,
        seriesSettingRef.current,
        shareRef.current,
        rulesRef.current,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        console.log('events', events, index);
        const result = events[index].submit();
        console.log('console.log(result);', result);
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  useEffect((): void => {
    if (formData.fileList.length) {
      setRenderPrize(true);
    } else {
      setRenderPrize(false);
    }
  }, [formData.fileList]);
  return (
    <div className={styles.setting}>
      {renderBase && <BaseInfo sRef={baseRef} {...settingProps} />}
      <SeriesSetting sRef={seriesSettingRef} {...settingProps} />
      {renderPrize && <Prizes sRef={prizeSettingRef} {...settingProps} />}
      <Order sRef={orderRef} {...settingProps} />
      <ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} />
      <RulesInfo sRef={rulesRef} {...settingProps} checkForm={checkForm} />
    </div>
  );
};
