import React from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
}
interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}
export default (props: Props) => {
  const { target, defaultValue, value, handleChange } = props;
  console.log('target', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return <div>{<Base {...eventProps} />}</div>;
};
