/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 大转盘
 */
import React, { useReducer, useState, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid, Radio } from '@alifd/next';
import LzColorPicker from '@/components/LzColorPicker';
import LzDialog from '@/components/LzDialog';
import LzWheelAssets from '@/components/LzWheelAssets';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [visible, setVisible] = useState<boolean>(false);
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };

  const selectWheel = (wheelBg): void => {
    setVisible(false);
    setForm({ wheelBg });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel title="奖盘">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="奖盘样式">
            <Radio.Group
              value={formData.wheelType}
              onChange={(val: string) => {
                setForm({ wheelType: val });
              }}
            >
              <Radio value={'1'}>预设转盘</Radio>
              <Radio value={'2'}>自定义转盘</Radio>
            </Radio.Group>
          </Form.Item>
          {formData.wheelType === '1' && (
            <Form.Item label="选择模板">
              <div className={styles.wheelStyle} onClick={() => setVisible(true)}>
                <img src={formData.wheelBg} alt="" />
              </div>
            </Form.Item>
          )}
          {formData.wheelType === '2' && (
            <>
              <Form.Item label="奖盘背景图">
                <Grid.Row>
                  <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                    <LzImageSelector
                      width={700}
                      height={700}
                      value={formData.wheelPanel}
                      onChange={(wheelPanel) => {
                        setForm({ wheelPanel });
                      }}
                    />
                  </Form.Item>
                  <Form.Item style={{ marginBottom: 0 }}>
                    <div className={styles.tip}>
                      <p>图片尺寸：700px * 700px</p>
                      <p>图片大小：不超过1M</p>
                      <p>图片格式：JPG、JPEG、PNG, GIF</p>
                    </div>
                    <div>
                      <Button
                        type="primary"
                        text
                        onClick={() => {
                          setForm({ wheelPanel: defaultValue?.wheelPanel });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </Form.Item>
                </Grid.Row>
              </Form.Item>
              <Form.Item label="奖盘按钮图">
                <Grid.Row>
                  <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                    <LzImageSelector
                      width={190}
                      height={225}
                      value={formData.wheelBtn}
                      onChange={(wheelBtn) => {
                        setForm({ wheelBtn });
                      }}
                    />
                  </Form.Item>
                  <Form.Item style={{ marginBottom: 0 }}>
                    <div className={styles.tip}>
                      <p>图片尺寸：190px * 225px</p>
                      <p>图片大小：不超过1M</p>
                      <p>图片格式：JPG、JPEG、PNG</p>
                    </div>
                    <div>
                      <Button
                        type="primary"
                        text
                        onClick={() => {
                          setForm({ wheelBtn: defaultValue?.wheelBtn });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </Form.Item>
                </Grid.Row>
              </Form.Item>
            </>
          )}
          <Form.Item label="奖盘文案颜色">
            <LzColorPicker value={formData.wheelTextColor} onChange={(wheelTextColor) => setForm({ wheelTextColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ wheelTextColor: defaultValue?.wheelTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="抽奖次数">
        {' '}
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="文案颜色">
            <LzColorPicker value={formData.drawsNum} onChange={(drawsNum) => setForm({ drawsNum })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ drawsNum: defaultValue?.drawsNum });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzDialog
        title="奖盘样式"
        className="lz-dialog-larger"
        visible={visible}
        footer={false}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
      >
        <LzWheelAssets handleSelect={selectWheel} />
      </LzDialog>
    </div>
  );
};
