/**
 * 累计签到数据报表
 */
import React, { useState } from 'react';
import { Tab, Button } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import WinRecord from '@/pages/activity/80003/record/components/WinRecord';
import LotteryRecord from '@/pages/activity/80003/record/components/LotteryRecord';
import exportCombinedLogs from '@/utils/exportAll';
import { dataSignLogExport, dataWinningLogExport } from '@/api/v80003';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="累计签到有礼数据报表">
        <Button
          onClick={() => {
            exportCombinedLogs([dataSignLogExport, dataWinningLogExport], '累计签到有礼数据报表');
          }}
          style={{ float: 'right', position: 'relative', zIndex: 1000, marginTop: 8 }}
        >
          导出全部
        </Button>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="参与记录" key="1">
            <LotteryRecord />
          </Tab.Item>
          <Tab.Item title="中奖记录" key="2">
            <WinRecord />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
