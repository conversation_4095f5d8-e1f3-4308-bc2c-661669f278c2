@import "@alifd/next/variables";

.skuList {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f1f6;
  border-radius: 2px;

  .skuItem {
    position: relative;
    display: inline-block;
    width: 130px;
    height: 200px;
    margin: 5px;
    overflow: hidden;
    background-color: white;
    border-radius: 2px;

    .skuL<PERSON> {
      width: 130px;
      height: 130px;

      img {
        width: 130px;
        height: 130px;
      }
    }

    .skuTitle {
      padding: 2px 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .skuPrice {
      padding: 2px 5px;
    }

    .skuId {
      padding: 2px 5px;
    }

    &:hover {
      .skuClose {
        opacity: 1;
      }
    }

    .skuClose {
      position: absolute;
      top: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 22px;
      height: 22px;
      color: white;
      background-color: #f33;
      cursor: pointer;
      opacity: 0.5;
      transition: opacity 200ms linear;
    }
  }
}
.part1 {
  display: flex;
  justify-content: flex-start;
}

.part1_p1 {
  display: -webkit-box;
  width: 100%;
  margin: 0;
  overflow: hidden;
  line-height: 1.5 !important;
  text-align: left;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.part1_p2 {
  margin: 0;
  color: #8e97a2;
  text-align: left;
}

.part1_p1_p1 {
  display: -webkit-box;
  width: 100%;
  margin: 0;
  overflow: hidden;
  line-height: 1.5 !important;
  text-align: left;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
