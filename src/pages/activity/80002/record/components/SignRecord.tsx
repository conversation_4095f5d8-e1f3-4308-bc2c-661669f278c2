import React, { useEffect, useState } from 'react';
import { Field, Form, Select, Tab, Table, DatePicker2, Calendar2 } from '@alifd/next';
import { deepCopy, getParams } from '@/utils';
import constant from '@/utils/constant';
import dayJs, { Dayjs } from 'dayjs';
import { dataSignLog } from '@/api/v80002';
import LzPagination from '@/components/LzPagination';
import styles from './style.module.scss';
import LzLoading from '@/components/LzLoading';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

const IS_WIN = [
  { label: '全部', value: '' },
  { label: '未领奖', value: 0 },
  { label: '已领奖', value: 1 },
  { label: '未获奖', value: 2 },
];
let allData = [] as any[];

export default ({ id }) => {
  const [activeKey, setActiveKey] = useState('1');
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [signDays, setSignDays] = useState({ continuousSignDays: 0, signDays: 0 });
  // const [allData, setAllData] = useState<any[]>([]);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  ];

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.id = id;
    dataSignLog(query)
      .then((res: any): void => {
        setSignDays({
          continuousSignDays: res.continuousSignDays,
          signDays: res.signDays,
        });
        setTableData(res.signLog.records as any[]);
        pageInfo.total = +res.signLog.total;
        pageInfo.pageSize = +res.signLog.size;
        pageInfo.pageNum = +res.signLog.current;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const handlePage = ({ pageSize, pageNum }) => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  // 加载全部天数给日历用
  const loadAllData = (): void => {
    setLoading(true);
    const query = {
      activityId: getParams('id'),
      id,
      rangeDate: defaultRangeVal,
      pageNum: 1,
      pageSize: 500,
    };
    dataSignLog(query)
      .then((res: any): void => {
        allData = res.signLog.records as any[];
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  const checkSign = (time: Dayjs) => {
    const result = allData.find((item) => dayJs(item.signTime).startOf('day').valueOf() === time.valueOf());
    return !!result;
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  const Record = () => (
    <div>
      <div className={styles.list}>
        {/* <div className={styles.content}> */}
        {/*  <div className={styles.days}> */}
        {/*    <span>{signDays.continuousSignDays}</span>天 */}
        {/*  </div> */}
        {/*  <div>当前连续签到</div> */}
        {/* </div> */}
        <div className={styles.content}>
          <div className={styles.days}>
            <span>{signDays.signDays}</span>天
          </div>
          <div>当前连续签到</div>
        </div>
      </div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="签到时间" style={{ minWidth: '350px' }}>
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem name="isWin" label="是否领奖" requiredMessage="请选择是否领奖">
          <Select
            followTrigger
            mode="single"
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            defaultValue={''}
            dataSource={IS_WIN}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column
          title="签到人"
          dataIndex="nickName"
          cell={(value, index, data) => (
            <div>
              <img src={data.avatarUrl} alt="" className={styles.avatarUrl} />
              <span>{data.nickName}</span>
            </div>
          )}
        />
        <Table.Column
          title="签到时间"
          dataIndex="signTime"
          cell={(value, index, data) => <div>{dayJs(data.signTime).format('YYYY-MM-DD HH:mm:ss')}</div>}
        />
        <Table.Column title="是否领奖" dataIndex="isWinning" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );

  const Calendar = () => (
    <LzLoading visible={loading}>
      {!loading && (
        <Calendar2
          className={styles.calendarRest}
          shape="panel"
          defaultValue={dayJs().add(1, 'days')}
          dateCellRender={(val) => {
            return <div className={`${checkSign(val) ? styles.restDay : ''}`}>{val.format('D')}</div>;
          }}
        />
      )}
    </LzLoading>
  );

  return (
    <Tab
      defaultActiveKey="1"
      activeKey={activeKey}
      onChange={(key) => {
        setActiveKey(key);
        if (key === '2') {
          loadAllData();
        }
      }}
      unmountInactiveTabs
    >
      <Tab.Item title="签到记录" key="1">
        <Record />
      </Tab.Item>
      <Tab.Item title="签到日历" key="2">
        <Calendar />
      </Tab.Item>
    </Tab>
  );
};
