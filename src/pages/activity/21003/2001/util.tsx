import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import format from '../../../../utils/format';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}
class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

export interface CustomValue {
  // 活动主图
  actBg: string;
  // 页面背景图
  pageBg: string;
  // 申领成功页面背景
  successPageBg: string;
  // 页面背景颜色
  actBgColor: string;
  // 礼品介绍图
  introductionImg: string;
  // 申领成功介绍图
  giftIntroductionImg: string;
  // 活动规则图
  ruleImg: string;
  // 我的订单图标
  recordImg: string;
  // 领取攻略图标
  strategyImg: string;
  // 活动详情页主图
  detailsKvImg: string;
  // 活动详情页颜色
  detailsBgColor: string;
  // 攻略图
  strategyPopupImg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  shopNameColor: string;
  disableShopName: number;
}

export interface PageData {
  shopName: string;
  activityName: string;
  rangeDate: [Dayjs, Dayjs] | [string, string];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  reservationAmountShow: number;
  crowdBag: any;
  totalProbability: number;
  prizeList: any[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
  orderStartTime: Dayjs | string;
  orderStartTimeNum: string;
  maxParticipateNum: string;
  fullGiftThreshold: string;
  giftSkuList: Sku[];
  orderSkuType: number;
  orderSkuList: any[];
  skuList: any[];
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 申领成功页面背景图
  successPageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 礼品介绍图
  introductionImg: '',
  // 申领成功介绍图
  giftIntroductionImg: '',
  // 活动规则图
  ruleImg: '',
  // 我的订单图标
  recordImg: '',
  // 领取攻略图标
  strategyImg: '',
  // 活动详情页主图
  detailsKvImg: '',
  // 活动详情页颜色
  detailsBgColor: '',
  // 攻略图
  strategyPopupImg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  // 店铺名称颜色
  shopNameColor: '',
  disableShopName: 0,
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `会员首购加赠好礼-${dayjs().format('YYYY-MM-DD')}`,
    // 店铺名称
    shopName: getShop().shopName,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 预定所需金额
    reservationAmountShow: 0.01,
    // 计算订单开始时间
    // orderStartTime: dayjs().subtract(6, 'month').add(1, 'day'),
    orderStartTime: '2022-07-24 00:00:00',
    // 订单开始时间
    orderStartTimeNum: '',
    // 累计最大参与人数
    maxParticipateNum: '',
    // 满赠门槛
    fullGiftThreshold: '',
    // 赠品SKU
    giftSkuList: [],
    // 参与活动商品  0 全店 1 指定
    orderSkuType: 0,
    // 参与活动正装商品
    orderSkuList: [],
    // 曝光商品列表
    skuList: [],
    // 中奖总概率
    totalProbability: 0,
    // 奖品列表
    prizeList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '会员首购领好礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '活动规则',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

const checkPrizeSetting = (formData: PageData) => {
  if (!formData.giftSkuList.length) {
    Message.error('请输入赠品SKU');
    return false;
  }
  if (!formData.maxParticipateNum) {
    Message.error('请输入累计最大参与人数');
    return false;
  }
  if (!formData.fullGiftThreshold) {
    Message.error('请输入满赠门槛');
    return false;
  }
  if (formData.orderSkuType === 1 && !formData.orderSkuList.length) {
    Message.error('请输入参与活动商品');
    return false;
  }
  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择令牌
  if (!formData.prizeList.length) {
    Message.error('请选择令牌');
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 奖品设置部分校验
  if (!checkPrizeSetting(formData)) {
    return false;
  }
  // 活动时间在令牌有效期内
  if (
    (formData.prizeList.length && dayjs(formData.startTime).isBefore(dayjs(formData.prizeList[0].beginTime))) ||
    dayjs(formData.endTime).isAfter(dayjs(formData.prizeList[0].endTime))
  ) {
    Message.error('活动时间应在令牌有效期内');
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  return true;
};
