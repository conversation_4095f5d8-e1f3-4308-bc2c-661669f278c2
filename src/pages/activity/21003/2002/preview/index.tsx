/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Input } from '@alifd/next';
import { formItemLayout, PageData } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import ChoosePromotion from '@/components/ChoosePromotion';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        <FormItem label="参与规则">
          1.会员入会后未有过任何一笔订单状态为完成的正装商品订单 2.会员首购有礼同一用户只享受一次
        </FormItem>
        {/* <FormItem label="计算订单开始时间">
          {format.formatDateTimeDayjs(formData.orderStartTime)}至活动开始时间，不存在订单状态为完成的订单
        </FormItem> */}
        <FormItem label="奖品类型">总价促销令牌（满赠）</FormItem>
        <FormItem label="令牌" isPreview>
          <ChoosePromotion value={formData.prizeList[0] ?? null} disabled />
        </FormItem>
        <FormItem label="发放时机">
          用户在活动首页，满足活动参与门槛及参与规则，点击“立即申请”后，系统自动发放总价促销令牌（即系统自动将用户加入令牌中）
        </FormItem>
        <FormItem label="赠品SKU">
          {formData.giftSkuList.map((item, index) => (
            <div key={index}>{item.skuId}</div>
          ))}
        </FormItem>
        <FormItem label="累计最大参与人数">{formData.maxParticipateNum}人</FormItem>
        <FormItem label="满赠门槛">{formData.fullGiftThreshold}元</FormItem>
        {/* <FormItem label="参与活动正装商品（即主商品）">
          {formData.orderSkuType === 0 && <div>全店正装商品</div>}
          {formData.orderSkuType === 1 && (
            <div className={styles.container}>
              {formData.orderSkuList?.map((sku) => {
                return (
                  <div className={styles.skuContainer}>
                    <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                    <div>
                      <div className={styles.skuName}>{sku.skuName}</div>
                      <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                      <div className={styles.price}>¥ {sku.jdPrice}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </FormItem> */}
        {/* <FormItem label="曝光商品">
          <div className={styles.container}>
            {formData.skuList?.map((sku, key) => {
              return (
                <div className={styles.skuContainer}>
                  <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                  <div>
                    <div className={styles.skuName}>{sku.skuName}</div>
                    <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                    <div className={styles.price}>¥ {sku.jdPrice}</div>
                  </div>
                </div>
              );
            })}
          </div>
        </FormItem> */}
        {/* <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className={styles.wordBreak} style={{ wordBreak: 'break-all' }} value={formData.rules} />
        </FormItem> */}
      </Form>
    </div>
  );
};
