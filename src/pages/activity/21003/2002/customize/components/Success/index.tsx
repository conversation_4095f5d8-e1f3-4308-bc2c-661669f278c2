import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel title="活动详情页">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="页面主图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.detailsKvImg}
                  onChange={(detailsKvImg) => {
                    setForm({ detailsKvImg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px、推荐图片高度480/790/1334px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ detailsKvImg: defaultValue?.detailsKvImg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="页面背景图"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={750} */}
          {/*         value={formData.successPageBg} */}
          {/*         onChange={(successPageBg) => { */}
          {/*           setForm({ successPageBg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：宽度750px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ successPageBg: defaultValue?.successPageBg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
          <Form.Item label="页面背景颜色">
            <LzColorPicker value={formData.detailsBgColor} onChange={(detailsBgColor) => setForm({ detailsBgColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ detailsBgColor: defaultValue?.detailsBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          {/* <Form.Item label="申领成功介绍"> */}
          {/*   <Grid.Row> */}
          {/*     <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*       <LzImageSelector */}
          {/*         width={650} */}
          {/*         height={370} */}
          {/*         value={formData.giftIntroductionImg} */}
          {/*         onChange={(giftIntroductionImg) => { */}
          {/*           setForm({ giftIntroductionImg }); */}
          {/*         }} */}
          {/*       /> */}
          {/*     </Form.Item> */}
          {/*     <Form.Item style={{ marginBottom: 0 }}> */}
          {/*       <div className={styles.tip}> */}
          {/*         <p>图片尺寸：650px*370px</p> */}
          {/*         <p>图片大小：不超过1M</p> */}
          {/*         <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*       </div> */}
          {/*       <div> */}
          {/*         <Button */}
          {/*           type="primary" */}
          {/*           text */}
          {/*           onClick={() => { */}
          {/*             setForm({ giftIntroductionImg: defaultValue?.giftIntroductionImg }); */}
          {/*           }} */}
          {/*         > */}
          {/*           重置 */}
          {/*         </Button> */}
          {/*       </div> */}
          {/*     </Form.Item> */}
          {/*   </Grid.Row> */}
          {/* </Form.Item> */}
        </Form>
      </LzPanel>
    </div>
  );
};
