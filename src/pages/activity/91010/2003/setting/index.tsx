/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:03
 * Description: 活动设置
 */
import React, { useImperativeHandle, useRef, useReducer, useState } from 'react';
import styles from './style.module.scss';
// 基础信息
import BaseInfo from './components/Base';
// 完善信息
import CompleteInfo from './components/Info';
// 参与人群
// import Crowd from './components/Crowd';
// 分享设置
import ShareInfo from './components/Share';
import Prize from './components/Prizes';
// 活动规则

import { PageData, CustomValue } from '@/pages/activity/91010/2003/util';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}
interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}
export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const [renderBase] = useState(true);
  const [renderPrize, setRenderPrize] = useState(formData.threshold !== 1);
  console.log(renderPrize, 'renderPrize=');
  const onChange = (activityInfo, type?: string): void => {
    console.log('活动设置信息更新:', activityInfo);
    if (activityInfo.supportLevels) {
      setTimeout(() => {
        setRenderPrize(true);
      });
    }
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const completeRef = useRef<{ submit: () => void | null }>(null);
  const prizeRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  // const crowdRef = useRef<{ submit: () => void | null }>(null);
  // const rulesRef = useRef<{ submit: () => void | null }>(null);

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current!,
        // crowdRef.current,
        completeRef.current,
        prizeRef.current,
        shareRef.current,
      ];
      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((item, index: number): boolean => {
        const result = events[index].submit();
        if (result) {
          resultList.push(result);
          return false;
        }
        return true;
      });
      onSubmit(resultList.length);
    },
  }));
  // const checkForm = (): boolean => {
  //   const events: any[] = [
  //     baseRef.current!,
  //     crowdRef.current,
  //     completeRef.current,
  //     prizeRef.current,
  //     shareRef.current,
  //     rulesRef.current,
  //   ];
  //   return events.every((item, index: number): boolean => {
  //     const result = events[index].submit();
  //     if (result) {
  //       return false;
  //     }
  //     return true;
  //   });
  // };
  return (
    <div className={styles.setting}>
      {renderBase && <BaseInfo sRef={baseRef} {...settingProps} />}
      {/*<Crowd sRef={crowdRef} {...settingProps} />*/}
      <CompleteInfo sRef={completeRef} {...settingProps} />
      <Prize sRef={prizeRef} {...settingProps} />
      <ShareInfo sRef={shareRef} {...settingProps} decoValue={decoValue} />
      {/*<RulesInfo sRef={rulesRef} {...settingProps} checkForm={checkForm} />*/}
    </div>
  );
};
