import React, { useEffect, useState } from 'react';
import {Form, Input, Field, Table, Button, Select, Dialog} from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import {dataExamineImg, dataExamineImgPage} from '@/api/v91010';
import { deepCopy, getParams } from '@/utils';
import dayJs from 'dayjs';
// import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
// import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';
import VideoDialog from "./VideoDialog";
const FormItem = Form.Item;
// const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [showVideoDialog,setShowVideoDialog] = useState(false);
  const [videoUrl,setVideoUrl] = useState('');
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  // const [packVisible, setPackVisible] = useState(false);
  // const defaultRangeVal = [
  //   dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
  //   dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD 23:59:59'),
  // ];
  const reviewList = [{
    label: '全部',
    value: '',
  }, {
    label: '待审核',
    value: 0,
  }, {
    label: '审核成功',
    value: 1,
  }, {
    label: '审核失败',
    value: 2,
  }]
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataExamineImgPage(query)
      .then((res: any): void => {
        res.records.forEach((item) => {
          if (item.bornInfo) {
            item.imgOne = JSON.parse(item.bornInfo).imgOne;
            item.imgTwo = JSON.parse(item.bornInfo).imgTwo;
            item.video = JSON.parse(item.bornInfo).video;
          }
        });
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  // const exportData = () => {
  //   const formValue: any = field.getValues();
  //   formValue.activityId = getParams('id');
  //   // dataWinningLogExport(formValue).then((data: any) => downloadExcel(data, '中奖记录'));
  // };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);
//   查看视频
const queryVideo = (row: any) => {
  console.log('查看视频')
  setVideoUrl(row.video);
  setShowVideoDialog(true);
};
  const onRowChange = (selectedKeys) => {
    setSelectedRows(selectedKeys);
  };
  const rowSelection = {
    selectedRowKeys: selectedRows,
    onChange: onRowChange,
  };
  // 一键审核
  const reviewAllClick = (type: any, id: any, status: number) => {
    console.log(selectedRows, '一键审核');

    const queryData = {
      id: type === 'all' ?  selectedRows : [id],
      status: status,
    }
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content:  status === 1 ? '确认审核通过' : '确认审核不通过',
      onOk: async (): Promise<void> => {
        dataExamineImg(queryData)
          .then((res: any): void => {
            setSelectedRows([]);
            const formValue: any = field.getValues();
            loadData({ ...formValue, ...defaultPage });
          })
          .catch((e) => {
            setLoading(false);
          });
      },
    } as any);
  };
  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="province" label="省">
          <Input maxLength={20} placeholder="请输入省份" />
        </Form.Item>
        <Form.Item name="city" label="市">
          <Input maxLength={20} placeholder="请输入城市" />
        </Form.Item>
        <Form.Item name="county" label="区">
          <Input maxLength={20} placeholder="请输入地区" />
        </Form.Item>
        <Form.Item name="nickName" label="用户昵称">
          <Input placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户pin">
          <Input placeholder="请输入用户pin" />
        </Form.Item>
        <FormItem name="status" label="审核状态" requiredMessage="请选择审核状态">
          <Select
            followTrigger
            mode="single"
            showSearch
            hasClear
            style={{ marginRight: 8 }}
            dataSource={reviewList}
            defaultValue={''}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button
          type="primary"
          size="medium"
          style={{ marginLeft: '10px' }}
          onClick={() => reviewAllClick('all', '', 1)}
        >
          一键通过
        </Button>
        {/*<Button onClick={exportData}>导出</Button>*/}
        {/*<Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>*/}
        {/*  生成人群包*/}
        {/*</Button>*/}
      </div>
      <Table.StickyLock dataSource={tableData} loading={loading} rowSelection={rowSelection}>
        <Table.Column width={120} title="审核状态" dataIndex="status"></Table.Column>
        <Table.Column
          width={160}
          title="图片审核时间"
          dataIndex="updateTime"
          cell={(data, index, row) => {
            return <div>{row.updateTime ? dayJs(row.updateTime).format('YYYY-MM-DD HH:mm:ss') : '--'}</div>;
          }}
        ></Table.Column>
        <Table.Column
          width={160}
          title="完善信息时间"
          dataIndex="createTime"
          cell={(data, index, row) => {
            return <div>{row.createTime ? dayJs(row.createTime).format('YYYY-MM-DD HH:mm:ss') : '--'}</div>;
          }}
        ></Table.Column>
        <Table.Column width={120} title="订单审核状态" dataIndex="examineOrderStatus"></Table.Column>
        <Table.Column
          width={160}
          title="订单审核时间"
          dataIndex="examineOrderTime"
          cell={(data, index, row) => {
            return <div>{row.examineOrderTime ? dayJs(row.examineOrderTime).format('YYYY-MM-DD HH:mm:ss') : '--'}</div>;
          }}
        ></Table.Column>
        <Table.Column
          width={120}
          title="昵称"
          dataIndex="nickName"
          cell={(data, index, row) => {
            return <div>{row.nickName ? row.nickName : '--'}</div>;
          }}
        ></Table.Column>
        <Table.Column width={120} title="家长姓名" dataIndex="parentName"></Table.Column>
        <Table.Column width={120} title="宝宝生日" dataIndex="babyBir"></Table.Column>
        <Table.Column width={120} title="出生证编号" dataIndex="bornNo"></Table.Column>
        <Table.Column width={120} title="手机号" dataIndex="phone"></Table.Column>
        <Table.Column
          width={150}
          title="登记地址-省/市/区"
          dataIndex="province"
          cell={(data, index, row: any) => {
            return (
              <div>
                {row.province}/{row.city}/{row.county}
              </div>
            );
          }}
        ></Table.Column>
        {/*<Table.Column width={120} title="登记地址-市" dataIndex="city"></Table.Column>*/}
        {/*<Table.Column width={120} title="登记地址-区" dataIndex="county"></Table.Column>*/}
        <Table.Column width={120} title="详细地址" dataIndex="address"></Table.Column>
        <Table.Column
          width={120}
          title="上传图片1"
          dataIndex="imgOne"
          cell={(data, index, row) => {
            return (
              <div>
                {row.imgOne && <img style={{ width: '50px', height: '50px' }} src={row.imgOne} alt="" />}{' '}
                {!row.imgOne && <div>--</div>}
              </div>
            );
          }}
        ></Table.Column>
        <Table.Column
          width={120}
          title="上传图片2"
          dataIndex="imgTwo"
          cell={(data, index, row) => {
            return (
              <div>
                {row.imgTwo && <img style={{ width: '50px', height: '50px' }} src={row.imgTwo} alt="" />}
                {!row.imgTwo && <div>--</div>}
              </div>
            );
          }}
        ></Table.Column>
        <Table.Column
          width={120}
          title="上传视频"
          dataIndex="imgTwo"
          cell={(data, index, row) => {
            return (
              <div>
                {row.video && (
                  <Button
                    text
                    type="primary"
                    onClick={() => {
                      queryVideo(row);
                    }}
                  >
                    查看视频
                  </Button>
                )}
                {!row.video && <div>--</div>}
              </div>
            );
          }}
        ></Table.Column>
        <Table.Column
          width={120}
          title="操作"
          lock="right"
          cell={(data, index, row) => {
            return (
              <div>
                {row.status !== '审核通过' && row.status !== '审核不通过' && (
                  <div>
                    <Button text type="primary" onClick={() => reviewAllClick('onlyOne', row.id, 1)}>
                      通过
                    </Button>
                    <Button
                      style={{ marginLeft: '10px' }}
                      text
                      type="primary"
                      warning
                      onClick={() => reviewAllClick('onlyOne', row.id, 2)}
                    >
                      不通过
                    </Button>
                  </div>
                )}
                {(row.status === '审核通过' || row.status === '审核不通过') && <div>--</div>}
              </div>
            );
          }}
        ></Table.Column>
      </Table.StickyLock>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <LzDialog
        title="上传视频"
        className="lz-dialog-mini"
        visible={showVideoDialog}
        footer={false}
        onCancel={() => setShowVideoDialog(false)}
        onClose={() => setShowVideoDialog(false)}
      >
        <VideoDialog formValue={field.getValues()} videoUrl={videoUrl} cancel={() => setShowVideoDialog(false)} />
      </LzDialog>
    </div>
  );
};
