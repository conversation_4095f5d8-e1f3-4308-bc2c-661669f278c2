/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 11:37
 * Description:
 */
import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { activityEditDisabled } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';
import { getShop } from '@/utils/shopUtil';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  disableShopName: number;
  myPrizeBg: string;
  skuListDeco?: any[];
  ruleBtnBg: string;
  joinVipPopBg: string; // 入会弹窗
  submitTipPopBg: string; // 提交信息提示弹窗
  submitSuccPopBg: string; // 提交信息成功弹窗
  reviewSuccPopBg: string; // 图片审核成功弹窗
  otherPopBg: string; // 动态文案弹窗背景
  otherTextPop: string; // 弹窗动态文案颜色

  applyTitleBg: string; // 申请流程标题
  infoUploadBg: string; // 申请流程1用户资料上传背景
  stepTwpTitleBg: string; // 申请流程2标题
  stepTwpTopBG: string; // 申请流程2头部背景
  stepTwpCenterBG: string; // 申请流程2中部背景
  stepTwpBottomBG: string; // 申请流程2下部背景
  infoTipBg: string; // 档案说明图片
  ruleBg: string; // 活动规则说明
  myPrizePop: string; // 我的奖品弹窗
  reviewResultPop: string; // 审核结果弹窗
  submitFailPopBg: string; // 提交信息不符合新客身份
  infoPop: string; //  填写/查看档案弹窗
  upLoadPop: string; // 上传出生证明弹唱
}
export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
};
export interface SkuInfo {
  seriesName: string;
  sortId: number;
  formalSku: string;
  formalName: string;
  giftSku: string;
  giftName: string;
  giftCode: string;
  gitAllName: string;
  giftTotal: number;
  giftNum: number;
  giftRule: string;
}
export const SKU_INFO: SkuInfo = {
  seriesName: '',
  sortId: 1,
  formalSku: '',
  formalName: '',
  giftSku: '',
  giftName: '',
  giftCode: '',
  gitAllName: '',
  giftTotal: 1,
  giftNum: 1,
  giftRule: '',
}
export interface PageData {
  shopName: string;
  rangeDate: [dayjs.Dayjs, dayjs.Dayjs];
  gradeLabel: any[];
  h5Img: string;
  activityName: string;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  threshold: number;
  rules: string;
  templateCode: string;
  endActivity: number;
  shareStatus: number;
  shareTitle: string;
  mpImg: string;
  prizeList: any[];
  cmdImg: string;
  startTime: string;
  endTime: string;
  crowdBag: any;
  crowdPackage: number;
  activitySill: number;
  uploadImg: number;
  examineLimit: number;
  downloadTemplateList: any[];
  updateTemplateList: any[];
  uploadRuleRequest?: any;
  uploadRuleRequestArr?: any[];
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  actBgColor: '',
  // 页面背景图
  pageBg: '',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/273660/38/27316/88020/680f6aa2Fbefa01cb/090fe7885822f7be.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/276724/4/29038/96731/680f6aa2F9c1d8162/cad91ca146b29a38.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/276724/4/29038/96731/680f6aa2F9c1d8162/cad91ca146b29a38.png',
  disableShopName: 0,
  ruleBtnBg: '',
  myPrizeBg: '',
  skuListDeco: [],
  joinVipPopBg: '', // 入会弹窗
  submitTipPopBg: '', // 提交信息提示弹窗
  submitSuccPopBg: '', // 提交信息成功弹窗
  reviewSuccPopBg: '', // 图片审核成功弹窗
  otherPopBg: '', // 动态文案弹窗背景
  otherTextPop: '', // 弹窗动态文案颜色
  infoUploadBg: '', // 申请流程1用户资料上传背景
  applyTitleBg: '', // 申请流程标题
  stepTwpTitleBg: '', // 申请流程2标题
  stepTwpTopBG: '', // 申请流程2头部背景
  stepTwpCenterBG: '', // 申请流程2中部背景
  stepTwpBottomBG: '', // 申请流程2下部背景
  infoTipBg: '', // 档案说明图片
  ruleBg: '', // 活动规则说明
  reviewResultPop: '',
  myPrizePop: '',
  submitFailPopBg: '',
  infoPop: '', //  填写/查看档案弹窗
  upLoadPop: '', // 上传出生证明弹唱
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 店铺名称
    shopName: getShop().shopName,
    // 活动名称
    activityName: `新客礼2.0-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 参与者生成人群包
    crowdPackage: 0,
    // 奖品列表
    prizeList: [],
    // 活动强制结束
    endActivity: 0,
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '新客校验，即可获得奖品！',
    // 分享图片
    cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/273660/38/27316/88020/680f6aa2Fbefa01cb/090fe7885822f7be.png',
    h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/276724/4/29038/96731/680f6aa2F9c1d8162/cad91ca146b29a38.png',
    mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/276724/4/29038/96731/680f6aa2F9c1d8162/cad91ca146b29a38.png',
    // 活动规则
    rules: '111',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
    activitySill: 1, // 活动门槛： 品牌新客
    uploadImg: 1, // 是否上传出生证明 1上传 0不上传
    examineLimit: 1, // 每日客服审批限制
    downloadTemplateList: [], // 活动商品系列数据
    updateTemplateList: [], // 修改的商品数组的新数组
    uploadRuleRequestArr: ['上传出生证明照片','上传手持出生证明照片','上传手持出生证明视频'],
    uploadRuleRequest: {
      image: true,
      parentsImg: true,
      video: true,
    }
  };
};

// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return activityEditDisabled();
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    console.log('endTime==', endTime);
    console.log('startTime==', startTime);
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
const checkSku = (formData: PageData) => {
  if (formData.downloadTemplateList.length <= 0) {
    Message.error('请先添加系列商品');
    return false;
  }
  const downloadTemplateList1 = formData.downloadTemplateList.filter((item) => item.giftSku);
  downloadTemplateList1.forEach((item1) => {
    item1.giftSku = parseFloat(item1.giftSku);
    item1.formalSku = parseFloat(item1.formalSku);
  })
  const ids1 = downloadTemplateList1.map(item => item.formalSku);
  const uniqueIds1 = new Set(ids1);
  if (uniqueIds1.size !== ids1.length) {
    Message.error('正装商品SkuID不能相同，请检查');
    return false;
  }
  const ids = downloadTemplateList1.map(item => item.giftSku);
  const uniqueIds = new Set(ids);
  if (uniqueIds.size !== ids.length) {
    Message.error('赠品SkuID不能相同，请检查');
    return false;
  }
  // 新增：校验 giftNum 是否超过库存 stock
  for (const item of downloadTemplateList1) {
    if (item.giftNum > item.giftTotal) {
      Message.error('赠品数量不能超过赠品库存，请检查');
      return false;
    }
  }
  return true;
};
// 校验系列商品数据
export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    formData.downloadTemplateList.forEach((item,index) => {
      if (item.operationType === 'canEdit' || item.operationType === 'add') {
        formData.updateTemplateList.push({...item});
      }
    })
    return true;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (!checkSku(formData)) {
    return false;
  }
  formData.downloadTemplateList.forEach((item,index) => {
    if (item.operationType === 'canEdit' || item.operationType === 'add') {
      formData.updateTemplateList.push({...item});
    }
  })
  console.log(formData, '最终数据=====');
  return true;
};
