import React, { useEffect, useState } from 'react';
import { Form, DatePicker2, Field, Table, Button } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataDetailData, dataExportDetailData } from '@/api/v96005';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';

import { request as httpRequest } from 'ice';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [tableHeader, setTableHeader] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataDetailData(query)
      .then((res: any): void => {
        // 将二维数组转换为对象数组，使每个对象的键与表头对应
        const formattedData = res.dataPage.records.map((record, index) => {
          const rowData: any = { num: index + 1 };
          // 将每个数组值映射到对应的表头
          res.header.forEach((headerItem, headerIndex) => {
            rowData[headerItem] = record[headerIndex];
          });
          return rowData;
        });
        setTableData(formattedData);
        setTableHeader(res.header as any[]);
        pageInfo.total = +res.dataPage.total!;
        pageInfo.pageSize = +res.dataPage.size!;
        pageInfo.pageNum = +res.dataPage.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
};
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    // dataExportDetailData(formValue).then((data: any) => downloadExcel(data, `${getParams('name')}活动数据`));
    httpRequest({
      url: '/96005/data/exportDetailData',
      method: 'post',
      data: formValue,
      responseType: 'blob' // 覆盖默认配置
    }).then((data: any) => {
      downloadExcel(data, `${getParams('name')}活动数据`);
    });
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="查询时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={'YYYY-MM-DD'}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <div style={{ overflowX: 'auto', width: '100%' }}>
        <Table
          dataSource={tableData}
          loading={loading}
          style={{borderTop: '0px'}}
        >
          {tableHeader && tableHeader.map((column, index) => (
            <Table.Column
              style={{borderTop: '1px solid #d9dbde'}}
              key={index}
              align={'center'}
              title={
                <span style={{ whiteSpace: 'nowrap' }}>
                  {column}
                </span>
              }
              dataIndex={column}
              cell={(value) => (
                <span style={{ whiteSpace: 'nowrap' }}>
                  {value}
                </span>
              )}
            />
          ))}
        </Table>
      </div>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
