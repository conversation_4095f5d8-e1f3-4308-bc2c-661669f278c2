.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;
  .img {
    min-height: 193.5px;
    width: 100%;
    background: #efefef;
  }
  .rule {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 10px;
    background: #efefef;
    padding: 5px 10px;
    border-radius: 10px;
  }
  .member {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
    margin-top: -17px;
    background: var(--member-bg) no-repeat center center;
    background-size: cover;
    position: relative;
    height: 187px;
  }
  .memberInfo {
    padding: 37.5px 15px 0 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 100;

    .memberInfoLeft {
      display: flex;
      align-items: center;
      .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #efefef;
        margin-right: 8px;
      }
      .name {
        font-weight: bold;
        font-size: 13px;
        margin-left: 11px;
      }
      .points {
        background: #efefef;
        padding: 3px 10px;
        margin-top: 5px;
        border-radius: 15px;
        font-size: 10px;
      }
    }
    .levelLabel {
      font-size: 10px;
      color: gray;
    }
    .level {
      font-size: 14px;
      font-weight: bold;
      margin-top: 5px;
    }

  }
  .icons {
    padding: 0 15px;
    overflow-x: scroll;
    display: flex;
    gap: 43px;
    margin-top: 20px;


    .icon {
      flex-shrink: 0;
      width: 50px;
      height: 62.5px;
    }
  }
  .icons::-webkit-scrollbar {
    display: none;
  }

}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .kvContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
  .memberContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
    .levelSetting {

    }
  }

}
