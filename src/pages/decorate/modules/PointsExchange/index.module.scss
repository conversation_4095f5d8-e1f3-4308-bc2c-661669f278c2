.preview {
  width: 100%;
  position: relative;
  text-align: center;
  background: var(--bg-img);
  background-size: cover;
  border-radius: 8.5px;
  overflow: hidden;
  .items {
    overflow-x: scroll;
    display: flex;
    gap: 15px;
    padding: 15px;


    .itemImgEmpty {
      flex-shrink: 0;
      width: 111px;
      height: 120px;
      background: #efefef;
      position: relative;
      overflow: hidden;
      border-radius: 12px;
    }

    .itemImg {
      flex-shrink: 0;
      width: 111px;
      height: 120px;
      position: relative;
      overflow: hidden;
      border-radius: 12px;



      img {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
      }
    }
    .itemBtn {
      width: 100%;
      height: 21px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 10px;
      font-size: 10px;
      margin-top: 15px;
      color: var(--btn-color);
      background: var(--btn-bg);
    }
  }
  .itemTip {
    width: 100px;
    margin-top: 15px;
  }
  .items::-webkit-scrollbar {
    display: none;
  }

  .tip {
    display: flex;
    justify-content: center;
    color: #9f9f9f;
    font-size: 10px;
    margin-bottom: 15px;
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  .bgContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
  .exchangeContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
  }
  .itemContainer {
    display: flex;
    &:first-child {
      margin-top: 15px;
    }
    .itemImg {
      margin-right: 10px;
    }
    .itemInfo {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .icons {
        .transform180 {
          display: inline-block;
          transform: rotate(180deg);
        }
        i {
          margin-right: 5px;
          cursor: pointer;
          transform: rotate(180deg);
        }
      }
    }
  }
}
