// 会员卡
import MemberCardDecorate from './modules/MemberCard';
import MemberCardPreview from './modules/MemberCard/preview';

// 会员优惠券
import CouponFloorDecorate from './modules/CouponFloor';
import CouponFloorPreview from './modules/CouponFloor/preview';

// 静态图
import MemberRuleDecorate from './modules/MemberRule';
import MemberRulePreview from './modules/MemberRule/preview';

// 爆款商品
import HotProductDecorate from './modules/HotProduct';
import HotProductPreview from './modules/HotProduct/preview';

// 积分兑换
import MemberPointExchangeDecorate from './modules/MemberPointExchange';
import MemberPointExchangePreview from './modules/MemberPointExchange/preview';

//热区
import HotZoneDecorate from './modules/HotZone';
import HotZonePreview from './modules/HotZone/preview';

export const initialState = () => {
  return {
    selectedModule: 'MemberCard',
    previews: {
      MemberCard: {
        previewComponent: MemberCardPreview,
        decorateComponent: MemberCardDecorate,
        name: '会员卡',
      },
      Map: {
        previewComponent: MemberRulePreview,
        decorateComponent: MemberRuleDecorate,
        name: '薅羊毛地图',
      },
      HotZone1: {
        previewComponent: HotZonePreview,
        decorateComponent: HotZoneDecorate,
        name: '热区1',
      },
      CouponFloor: {
        previewComponent: CouponFloorPreview,
        decorateComponent: CouponFloorDecorate,
        name: '会员优惠券',
      },
      HotZone2: {
        previewComponent: HotZonePreview,
        decorateComponent: HotZoneDecorate,
        name: '热区2',
      },
      MemberPointExchange: {
        previewComponent: MemberPointExchangePreview,
        decorateComponent: MemberPointExchangeDecorate,
        name: '积分商城',
      },
      HotProduct: {
        previewComponent: HotProductPreview,
        decorateComponent: HotProductDecorate,
        name: '爆款商品',
      },
      MemberRule: {
        previewComponent: MemberRulePreview,
        decorateComponent: MemberRuleDecorate,
        name: '会员规则',
      },
      HotZone3: {
        previewComponent: HotZonePreview,
        decorateComponent: HotZoneDecorate,
        name: '备用热区1',
      },
      HotZone4: {
        previewComponent: HotZonePreview,
        decorateComponent: HotZoneDecorate,
        name: '备用热区2',
      },
    },
    modules: {
      MemberCard: {},
      MemberRule: {},
      Map: {},
      HotProduct: {
        skuList: [],
      },
      CouponFloor: {
        couponAct: {},
      },
      MemberPointExchange: {
        goodsAct: {},
      },
      HotZone1:{},
      HotZone2:{},
      HotZone3:{},
      HotZone4:{},
    },
    id: null,
    defaultValue: {}, // 重置用的原始数据
    defaultIconInfo: [],
    defaultCouponList: [],
    defaultGoodsList: [],
    levelInfo: [],
    activityActiveTab: {
      biTab: '1',
      newGiftTab: '1',
    }, // 会员专属福利切换人群tab、新客专享价切换按钮tab，预览需要展示不同样式
    allJson: [], // 接口返回的完整json
    refreshPage: false,
  };
};
function updateModule(modules, selectedModule, payload) {
  if (Array.isArray(modules[selectedModule])) {
    return {
      ...modules,
      [selectedModule]: payload,
    };
  } else {
    console.log(2);
    return {
      ...modules,
      [selectedModule]: {
        ...modules[selectedModule],
        ...payload,
      },
    };
  }
}

export function decorateReducer(state, action) {
  switch (action.type) {
    case 'SELECT_MODULE':
      return { ...state, selectedModule: action.payload };
    case 'UPDATE_MODULE':
      return {
        ...state,
        modules: updateModule(state.modules, state.selectedModule, action.payload),
      };
    case 'INIT_MODULE': {
      for (const stateKey in state.modules) {
        const pageinfo = action.payload.filter((item) => item.pageType == stateKey);
        if (pageinfo.length > 0) {
          state.modules[`${stateKey}`] = { ...pageinfo[0], ...state.modules[`${stateKey}`] };
        }
      }
      return {
        ...state,
        modules: {
          ...state.modules,
        },
      };
    }
    case 'INIT_MEMBER_CARD': {
      return {
        ...state,
        modules: {
          ...state.modules,
          MemberCard: { ...action.payload },
        },
      };
    }
    case 'UPDATE_MEMBER_CARD': {
      return {
        ...state,
        MemberCard: { ...action.payload },
      };
    }
    case 'INIT_DEFAULT': {
      return {
        ...state,
        defaultValue: action.payload,
      };
    }
    case 'INIT_DEFAULT_ICONINFO': {
      return {
        ...state,
        defaultIconInfo: action.payload,
      };
    }
    case 'INIT_DEFAULT_LEVELINFO': {
      return {
        ...state,
        levelInfo: action.payload,
      };
    }
    case 'UPDATE_REFRESHPAGE': {
      return {
        ...state,
        refreshPage: action.payload,
      };
    }

    case 'UPDATE_ACTIVITYACTIVETAB': {
      return {
        ...state,
        activityActiveTab: action.payload,
      };
    }
    case 'INIT_SKULIST': {
      return {
        ...state,
        modules: {
          ...state.modules,
          HotProduct: {
            ...state.modules.HotProduct,
            skuList: action.payload,
          },
        },
      };
    }
    case 'INIT_ALLJSON': {
      return {
        ...state,
        allJson: action.payload,
      };
    }
    case 'INIT_TOKENACT': {
      return {
        ...state,
        modules: {
          ...state.modules,
          TimeLimitedFlash: {
            ...state.modules.TimeLimitedFlash,
            tokenAct: action.payload,
          },
        },
      };
    }
    case 'INIT_REBUY': {
      return {
        ...state,
        modules: {
          ...state.modules,
          LadderRepurchase: {
            ...state.modules.LadderRepurchase,
            reBuy: action.payload,
          },
        },
      };
    }
    case 'INIT_COUPON_ACT': {
      return {
        ...state,
        defaultCouponList: JSON.parse(JSON.stringify(action.payload)),
        modules: {
          ...state.modules,
          CouponFloor: {
            ...state.modules.CouponFloor,
            couponAct: action.payload,
          },
        },
      };
    }
    case 'UPDATE_COUPON_ACT': {
      return {
        ...state,
        modules: {
          ...state.modules,
          CouponFloor: {
            ...state.modules.CouponFloor,
            couponAct: action.payload,
          },
        },
      };
    }
    case 'INIT_GOODS_ACT': {
      console.log('************* INIT_GOODS_ACT ****************', action.payload);
      return {
        ...state,
        defaultGoodsList: JSON.parse(JSON.stringify(action.payload)),
        modules: {
          ...state.modules,
          MemberPointExchange: {
            ...state.modules.MemberPointExchange,
            goodsAct: action.payload,
          },
        },
      };
    }
    case 'UPDATE_GOODS_ACT': {
      return {
        ...state,
        modules: {
          ...state.modules,
          MemberPointExchange: {
            ...state.modules.MemberPointExchange,
            goodsAct: action.payload,
          },
        },
      };
    }
    default:
      return state;
  }
}
