$px-scale: 20;
.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;
  .main-view {
    width: 100%;
    height: 27.5px*$px-scale;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: top;
    box-sizing: border-box;
    padding-top: 3px*$px-scale;
    padding-left: 0.75px*$px-scale;
    //padding-right: 0.75px*$px-scale;
    overflow: hidden;
  }
  .product-view {
    width: 100%;
    height: 21px*$px-scale;
    overflow-y: scroll;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .product-item {
      flex: none;
      width: 8.2px*$px-scale;
      height: 11.5px*$px-scale;
      margin-bottom: 0.25px*$px-scale;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      background-position: center;
      box-sizing: border-box;
      padding: 0.5px*$px-scale 0.5px*$px-scale;
      .product-img {
        display: block;
        width: 6.5px*$px-scale;
        border-radius: 0.6px*$px-scale;
        margin: 0 auto .3px*$px-scale;
      }
      .product-name {
        font-size: 0.5px*$px-scale;
        line-height: 0.75px*$px-scale;
        height: 1.375px*$px-scale;
        color: #000;
        font-weight: bold;
        overflow: hidden; /*溢出隐藏*/
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        width: 100%;
      }
      .buy-img {
        margin: 10px auto 0 ;
        width: 5.44px*$px-scale ;
        display: block;
      }
    }
  }
  .link-btn {
    display: block;
    height: 2px*$px-scale;
    margin: 0.5px*$px-scale auto ;
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .kvContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
}
