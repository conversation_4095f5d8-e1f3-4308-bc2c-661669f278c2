import React from 'react';
import styles from './index.module.scss';

export default ({ data, dispatch }) => {
  return (
    <div className={styles.preview}>
      <div
        className={styles['main-view']}
        style={{
          backgroundImage: `url(${data.content.background.imgUrl})`,
          backgroundColor: data.content.background.backgroundColor,
        }}
      >
        <div className={styles['product-view']}>
          {data.skuList &&
            data.skuList.map((item, index) => {
              return (
                <div
                  className={styles["product-item"]}
                  key={index}
                  style={{ backgroundImage: `url(${data.content.productBackground.imgUrl})` }}
                >
                  <img className={styles["product-img"]} src={item.skuMainPicture} />
                  <div className={styles["product-name"]}>{item.skuName}</div>
                  <img className={styles["buy-img"]}
                       src="https://img10.360buyimg.com/imgzone/jfs/t1/298675/28/606/5459/680dc71eFe9f2c4bd/7be740ab2e6e7093.png"
                       alt={''} />

                </div>
              );
            })}
        </div>
      </div>
      <img
        className={styles["link-btn"]}
        src={data.content.linkBtn.imgUrl}
        alt={''}
      />
    </div>
  );
};
