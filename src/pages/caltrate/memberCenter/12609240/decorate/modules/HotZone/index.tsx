import LzDialog from '@/components/LzDialog';
import LzImageSelector from '@/components/LzImageSelector';
import LzTipPanel from '@/components/LzTipPanel';
import EditHotZone from '../../compoonets/EditHotZone';
import SettingPeople from '@/pages/decorate/compoonets/SettingPeople';
import { Button, Card, Divider, Message, Radio } from '@alifd/next';
import React, { useEffect } from 'react';
import styles from './index.module.scss';
import { memberSavePageJson } from '@/api/caltratePop';

const RadioGroup = Radio.Group;

const TIP = (
  <div>
    <div style={{ fontWeight: 'bold', fontSize: 14, marginBottom: 5 }}>说明: </div>
    <div>1.建议宽度为730px-750px</div>
    <div>2.大小不超过1M图片类型为jpg、png</div>
  </div>
);

export default ({ data, dispatch, allJson }) => {
  console.log(data);
  const [pageLoading, setPageLoading] = React.useState(false);
  const [visible, setVisible] = React.useState(false);
  const [peopleVisible, setPeopleVisible] = React.useState(false);
  const [editData, setEditData] = React.useState({});
  const [editDataIndex, setEditDataIndex] = React.useState(0);

  const deleteHotZoneUrl = (dataIndex, hotZoneIndex) => {
    data.content.hotZone[dataIndex].hotZoneList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };

  const deleteHotZone = (dataIndex) => {
    data.content.hotZone.splice(dataIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };

  const addHotZone = (dataIndex) => {
    const info = data.content.hotZone[dataIndex];
    if (!info.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditData(info);
    setEditDataIndex(dataIndex);
    setVisible(true);
  };

  const handleAddImage = () => {
    const updatedDataList = data.content.hotZone;
    updatedDataList.push({ bg: '', hotZoneList: [] });
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };
  const isValidSwiper = () => {
    let err = false;

    data.content.hotZone.forEach((e) => {
      if (!e.bg) {
        err = true;
      }
      if (!e.hotZoneList.length) {
        err = true;
      }
    });
    if (err) {
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!data.content.hotZone.length) {
      Message.error('请至少添加一张图片');
      return false;
    }
    if (!isValidSwiper()) {
      Message.error('请完善热区信息');
      return false;
    }
    delete data[0];
    delete data[1];
    delete data[2];
    const updateJson = allJson.filter((item) => item.pageType != data.pageType);
    updateJson.push(data);
    console.log('updateJson', updateJson);
    setPageLoading(true);
    memberSavePageJson({ pageInfo: JSON.stringify(updateJson) })
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };
  const commonProps = {
    title: data.pageName,
    extra: (
      <div>
        {/* eslint-disable-next-line react/jsx-no-undef */}
        <RadioGroup
          value={data.showType}
          onChange={(showType) => {
            data.showType = showType;
            dispatch({ type: 'UPDATE_MODULE', payload: data });
          }}
        >
          <Radio id="0" value={0}>
            全显示
          </Radio>
          <Radio id="1" value={1}>
            限新客
          </Radio>
          <Radio id="2" value={2}>
            限老客
          </Radio>
          <Radio id="3" value={3}>
            不显示
          </Radio>
        </RadioGroup>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  useEffect(() => {
  }, []);
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <LzTipPanel message={TIP} />
          <Button className={styles.addBtn} onClick={handleAddImage} disabled={data.content.hotZone?.length >= 3}>
            添加图片（{data.content.hotZone?.length} / 3）
          </Button>

          <div className={styles.operation}>
            {data.content.hotZone?.map((hotdata, dataIndex) => (
              <div className={styles.hotContainer} key={dataIndex}>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      width={[729,750]}
                      bgWidth={158}
                      value={hotdata.bg}
                      onChange={(bg) => {
                        const updatedDataList = data.content.hotZone;
                        updatedDataList[dataIndex].bg = bg;
                        updatedDataList[dataIndex].hotZoneList = [];
                        dispatch({ type: 'UPDATE_MODULE', payload: data });
                      }}
                    />
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addHotZone(dataIndex)}>
                      添加热区
                    </Button>
                    {hotdata.hotZoneList.map((item, hotZoneIndex) => (
                      <div key={hotZoneIndex} className={styles.urlContainer}>
                        <div className={styles.url}>{`热区0${hotZoneIndex + 1}：${item.url}`}</div>
                        <i
                          className="iconfont icon-icon-07 btn-del"
                          onClick={() => deleteHotZoneUrl(dataIndex, hotZoneIndex)}
                        />
                      </div>
                    ))}
                  </div>
                  <div className={styles.removeHotZone}>
                    <i className="iconfont icon-icon-07 btn-del" onClick={() => deleteHotZone(dataIndex)} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          data={editData}
          dataList={data.content.hotZone}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setVisible(false)}
        />
      </LzDialog>
      <LzDialog
        title={'人群限制'}
        visible={peopleVisible}
        footer={false}
        onClose={() => setPeopleVisible(false)}
        style={{ width: '500px' }}
      >
        <SettingPeople
          dataList={data.content.hotZone}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setPeopleVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
