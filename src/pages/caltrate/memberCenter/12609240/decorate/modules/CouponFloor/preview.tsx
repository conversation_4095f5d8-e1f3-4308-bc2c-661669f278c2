import React from 'react';
import styles from './index.module.scss';

export default ({ data, dispatch }) => {
  return (
    <div className={styles.preview}>
      <div className={styles['main-view']} style={{ backgroundImage: `url(${data.content.background.imgUrl})` }}>
        <div className={styles['prize-view']}>
          {data.couponAct.couponList.map((item, i) => {
            return <img key={i} className={styles['prize-item']} src={item.imgUrl} alt={''} />;
          })}
        </div>
      </div>
    </div>
  );
};
