$px-scale: 20;
.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;
  .main-view {
    width: 100%;
    height: auto;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    box-sizing: border-box;
  }
  .prize-view {
    width: 100%;
    height: 17.5px * $px-scale;
    margin-bottom: 0.75px*$px-scale;
    display: flex;
    align-items: center;
    overflow-y: hidden;
    overflow-x: hidden;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    padding: 4px*$px-scale 1px*$px-scale 0;
    position: relative;
    .record-btn {
      position: absolute;
      top: 2.75px  * $px-scale;
      left: 1.5px * $px-scale;
      width: 3px * $px-scale;
    }
  }
  .prize-box {
    width: 100%;
    height: 12.5px * $px-scale;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    overflow-y: scroll;
  }
  .prize-item {
    flex: none;
    width: 4.95px * $px-scale;
    height: 6.25px * $px-scale;
    margin-bottom: 0.1px*$px-scale;
    margin-right: 0.1px*$px-scale;
    //background-repeat: no-repeat;
    //background-size: contain;
    //background-position: center;
    //background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/174282/3/49321/2591/6707789eF22887637/48ea15aeb98ee378.png);
    // background-color: #00a6a3;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .prize-point {
    width: 100%;
    height: 1px * $px-scale;
    color: #8f602f;
    text-align: center;
    font-size: 0.75px * $px-scale;
    line-height: 1px * $px-scale;
    font-weight: bold;
  }
  .prize-img {
    width: 3px * $px-scale;
    margin: 0.25px * $px-scale;
  }
  .prize-name {
    width: 100%;
    height: 0.6px * $px-scale;
    color: #b1814e;
    text-align: center;
    font-size: 0.5px * $px-scale;
    line-height: 0.6px * $px-scale;
    overflow: hidden; /*溢出隐藏*/
    text-overflow: ellipsis; /*溢出用省略号显示*/
    white-space: nowrap;
  }
  .prize-btn {
    width: 3.8px * $px-scale;;
    height: 0.75px * $px-scale;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/179257/24/48731/3901/6707882aF7f1d20d5/f87b9f8bfff06aac.png)
  }
  .empty {
    width: 100%;
    height: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-bottom: 1px solid #fff;
    color: #000;

  }
  .previewItem {
    position: relative;

    img {
      width: 100%;
      vertical-align: middle;
      -webkit-user-drag: none;
    }
    .hotZone {
      position: absolute;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
    }
  }
}
.addBtn {
  width: 100%;
  margin-bottom: 10px;
  margin-top: -5px;
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .hotContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;

      .removeHotZone {
        margin-top: 4px;
        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
    .setting {
      margin-left: 15px;
      .btn {
        width: 300px;
      }
      .urlContainer {
        width: 300px;
        height: 28px;
        background: #fff;
        display: flex;
        align-items: center;
        padding: 0 10px;
        border-radius: 5px;
        margin-top: 8px;
        position: relative;
        .url {
          width: 100%;
          display: flex;
          overflow-x: scroll;
          white-space: nowrap;
        }
        .url::-webkit-scrollbar {
          display: none;
        }

        i {
          position: absolute;
          right: -17px;
          top: 5px;
          cursor: pointer;
          font-size: 12px;
        }
      }
    }

  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .kvContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
      }
    }
  }
}
