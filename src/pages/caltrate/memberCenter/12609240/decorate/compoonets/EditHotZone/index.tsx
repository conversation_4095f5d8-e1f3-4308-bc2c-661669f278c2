import React, { useState, useRef, useEffect } from 'react';
import styles from './index.module.scss';
import { fabric } from 'fabric';
import { checkOverlap, isTwoRectOverlap } from './utils';
import { Input, Message, Button, Divider, Form, Field } from '@alifd/next';
import { urlRegularCheck } from '@/pages/decorate/utils';

const BORDER_WIDTH = 2;
const ACTIVE_COLOR = '#1677ff';
const maxAreaCount = 8;

export default ({ data, dispatch, onClose, dataList, dataIndex }) => {
  const field = Field.useField({});
  const canvasRef = useRef<any>(null);
  const [canvas, setCanvas] = useState<any>(null);
  const imgWidth = useRef(0);
  const imgHeight = useRef(0);
  const canvasWidth = useRef(0);
  const canvasHeight = useRef(0);
  const currentSelection = useRef({
    startX: 0,
    startY: 0,
  });
  const [selectedRectId, setSelectedRectId] = useState('');
  // 将热区配置数据映射为编辑组件的内部状态
  const [rectConfig, setRectConfig] = useState(
    data.hotZoneList.map((config) => ({
      id: config.rectId,
      url: config.url,
      width: config.width,
      height: config.height,
      x: config.left,
      y: config.top,
    })),
  );

  console.log(selectedRectId);
  // 删除热区对象
  const deleteObject = (index, newCanvas) => {
    setRectConfig((prevRectConfig) => {
      const newRectConfig = [...prevRectConfig];
      const targetCanvas = [...newCanvas.getObjects()].filter(
        (e) => e.rectId === newRectConfig[index].id || e.rectId === newRectConfig[index].rectId,
      )[0];
      newRectConfig.splice(index, 1);
      newCanvas.remove(targetCanvas);
      return newRectConfig;
    });
    newCanvas.requestRenderAll();
  };

  // 选择热区时的回调
  const onSelectRect = (options, newCanvas) => {
    if (options.selected?.length > 1) {
      newCanvas.discardActiveObject();
    } else if (options.selected?.length === 1) {
      setSelectedRectId(options.selected[0].rectId);
    } else {
      setSelectedRectId('');
    }
  };
  // 获取画布上的热区对象列表
  const getRectList = (newCanvas?) => {
    const rectList: any[] = [];
    const result = canvas || newCanvas;
    if (!result) return rectList;

    result.forEachObject((obj: any) => {
      if (obj.rectId) {
        rectList.push(obj);
      }
    });

    return rectList;
  };
  // 添加新热区
  const addRect = ({ x, y }, newCanvas) => {
    const { startX, startY } = currentSelection.current;

    const width = x - startX;
    const height = y - startY;

    if (Math.abs(width) <= 10 || Math.abs(height) <= 10) {
      return;
    }

    const rect = createRect({
      top: height < 0 ? startY - Math.abs(height) : startY,
      left: width < 0 ? startX - Math.abs(width) : startX,
      width: Math.abs(width),
      height: Math.abs(height),
      rectId: `${Date.now()}`,
    });

    const hasOverlap = getRectList(newCanvas).some((rect2) =>
      isTwoRectOverlap(rect.getBoundingRect(), rect2.getBoundingRect()),
    );
    if (hasOverlap) {
      Message.error('热区之间不可重叠框选，请调整热区！');
      rect.dispose();
      return;
    }
    newCanvas.add(rect);
    newCanvas.setActiveObject(rect);
    setRectConfig((prevRectConfig: any) => [...prevRectConfig, { id: rect.rectId, url: '', x, y, width, height }]);
  };

  // 热区移动时的回调
  const onRectMoving = (target) => {
    const { top, left, lineCoords } = target;
    const { br, tl } = lineCoords;
    const width = br.x - tl.x;
    const height = br.y - tl.y;

    // 如果热区左侧位置小于0，则将其设置为0，防止热区超出画布左侧
    if (left < 0) {
      target.set('left', 0);
    }

    // 如果热区顶部位置小于0，则将其设置为0，防止热区超出画布顶部
    if (top < 0) {
      target.set('top', 0);
    }

    // 如果热区右侧位置加上宽度大于画布宽度，则将其左侧位置设置为合适的位置，防止热区超出画布右侧
    if (left + width >= canvasWidth.current) {
      target.set('left', canvasWidth.current - width);
    }

    // 如果热区底部位置加上高度大于画布高度，则将其顶部位置设置为合适的位置，防止热区超出画布底部
    if (top + height >= canvasHeight.current) {
      target.set('top', canvasHeight.current - height);
    }
  };

  // 热区释放鼠标时的回调
  const onRectMouseup = (target) => {
    const { lineCoords } = target;
    const { br, tl } = lineCoords;
    const options = {
      scaleX: 1,
      scaleY: 1,
    };
    // 如果热区的左上角横坐标小于0，则调整热区的左侧位置和宽度
    if (tl.x < 0) {
      Object.assign(options, {
        left: 0,
        width: br.x - BORDER_WIDTH,
      });
    }
    // 如果热区的右下角横坐标大于画布宽度，则调整热区的宽度
    else if (br.x > canvasWidth.current) {
      Object.assign(options, {
        width: canvasWidth.current - tl.x - BORDER_WIDTH,
      });
    }
    // 否则，按照原始计算方式设置热区的宽度
    else {
      Object.assign(options, {
        width: br.x - tl.x - BORDER_WIDTH,
      });
    }

    // 如果热区的左上角纵坐标小于0，则调整热区的顶部位置和高度
    if (tl.y < 0) {
      Object.assign(options, {
        top: 0,
        height: br.y - BORDER_WIDTH,
      });
    }
    // 如果热区的右下角纵坐标大于画布高度，则调整热区的高度
    else if (br.y > canvasHeight.current) {
      Object.assign(options, {
        height: canvasHeight.current - tl.y - BORDER_WIDTH,
      });
    }
    // 否则，按照原始计算方式设置热区的高度
    else {
      Object.assign(options, {
        height: br.y - tl.y - BORDER_WIDTH,
      });
    }
    // 应用计算后的选项，调整热区的位置和大小
    target.set(options);
  };
  // 创建热区对象
  const createRect = ({ top, left, width, height, rectId }) => {
    const rect = new fabric.Rect({
      top,
      left,
      width,
      height,
      fill: 'rgba(255, 255, 255, 0)',
      stroke: ACTIVE_COLOR,
      strokeWidth: BORDER_WIDTH,
      transparentCorners: false,
      lockRotation: true,
    }).setControlVisible('mtr', false);

    rect.rectId = rectId || `${Date.now()}`;

    rect.on('moving', () => onRectMoving(rect));
    rect.on('mouseup', () => onRectMouseup(rect));

    return rect;
  };
  // 初始化默认热区配置

  const initDefaultConfig = (configList, newCanvas?) => {
    const result = canvas || newCanvas;
    if (result) {
      configList.forEach((config) => {
        const rect = createRect({
          top: (config.top * canvasHeight.current) / config.containerHeight,
          left: (config.left * canvasWidth.current) / config.containerWidth,
          width: (config.width * canvasWidth.current) / config.containerWidth - BORDER_WIDTH,
          height: (config.height * canvasHeight.current) / config.containerHeight - BORDER_WIDTH,
          rectId: config.rectId,
        });
        result.add(rect);
      });
    }
  };
  // 获取鼠标释放位置
  const getEndPoint = (options) => {
    const { x, y } = canvasRef.current.getBoundingClientRect();
    const { pageX, pageY } = options;

    let endX = Math.min(canvasWidth.current, pageX - x) - BORDER_WIDTH;
    endX = Math.max(endX, 0);

    let endY = Math.min(canvasHeight.current, pageY - y) - BORDER_WIDTH;
    endY = Math.max(endY, 0);

    return {
      x: endX,
      y: endY,
    };
  };
  // 初始化画布
  const initCanvas = () => {
    const bgImg = new fabric.Image(document.getElementById(`bgImg${dataIndex}`));
    bgImg.scaleToWidth(canvasWidth.current);

    const newCanvas = new fabric.Canvas(canvasRef.current, {
      backgroundImage: bgImg,
      selectionColor: 'rgba(255, 255, 255, 0)',
      selectionLineWidth: BORDER_WIDTH,
      selectionBorderColor: ACTIVE_COLOR,
    });

    newCanvas.on('selection:cleared', (options) => onSelectRect(options, newCanvas));
    newCanvas.on('selection:updated', (options) => onSelectRect(options, newCanvas));
    newCanvas.on('selection:created', (options) => onSelectRect(options, newCanvas));

    newCanvas.on('mouse:down', (options) => {
      currentSelection.current.startX = options.e.offsetX;
      currentSelection.current.startY = options.e.offsetY;
    });

    newCanvas.on('mouse:up', (options) => {
      if (options.isClick) {
        if (checkOverlap(getRectList(newCanvas))) {
          Message.error('热区之间不可重叠框选，请调整热区！');
          return;
        }
      }

      if (options.isClick && options.target) return;

      if (!options.isClick) newCanvas.discardActiveObject();

      if (getRectList(newCanvas).length === maxAreaCount) {
        Message.error('最多可生成8个热区');
        return;
      }
      addRect(getEndPoint(options.e), newCanvas);
    });
    setRectConfig(data.hotZoneList);
    setCanvas(newCanvas);

    if (data.hotZoneList.length) {
      initDefaultConfig(data.hotZoneList, newCanvas);
    }
  };
  const onImgLoad = (e) => {
    const { width, height, naturalWidth, naturalHeight } = e.target;

    imgWidth.current = naturalWidth;
    imgHeight.current = naturalHeight;

    canvasWidth.current = width;
    canvasHeight.current = height;

    initCanvas();
  };

  const clearAll = () => {
    rectConfig.forEach(() => {
      deleteObject(0, canvas);
    });
  };

  const saveDraw = () => {
    const rectList = getRectList();

    if (!rectList.length) {
      Message.error('请至少添加1个热区');
      return;
    }
    let err: any = null;
    field.validate((errors: Object[]): void => {
      err = errors;
    });
    if (!err) {
      if (checkOverlap(rectList)) {
        Message.error('热区之间不可重叠框选，请调整热区！');
        return;
      }

      const hotZoneList = rectList.map((rect) => {
        const boundingRect = rect.getBoundingRect();
        return {
          left: boundingRect.left,
          top: boundingRect.top,
          width: boundingRect.width,
          height: boundingRect.height,
          url: rectConfig.filter((e) => e.id === rect.rectId || e.rectId === rect.rectId)[0].url, // 从 rectConfig 状态中获取链接
          rectId: rect.rectId,
          containerWidth: canvasWidth.current, // 使用 useRef 来获取画布的当前宽度
          containerHeight: canvasHeight.current, // 使用 useRef 来获取画布的当前高度
        };
      });
      const updatedDataList = [...dataList];
      updatedDataList[dataIndex].hotZoneList = hotZoneList;
      dispatch({ type: 'UPDATE_MODULE', payload: updatedDataList });
      onClose();
    }
  };

  useEffect(() => {
    if (canvas) {
      canvas.setWidth(canvasWidth.current);
      canvas.setHeight(canvasHeight.current);
    }
  }, [canvas, canvasWidth, canvasHeight]);
  return (
    <div>
      <div className={styles.container}>
        <div className={styles.left}>
          <div className={styles.tip}>
            鼠标置于图片区域内
            <span className={styles.red}>点击拖拽</span>即可生成热区，最多 <span className={styles.red}>8</span> 个热区
          </div>
          <div className={styles.imgContainer}>
            <div>
              <img id={`bgImg${dataIndex}`} src={data.bg} alt="" onLoad={onImgLoad} />
              <canvas ref={canvasRef} width={canvasWidth.current} height={canvasHeight.current} />
            </div>
          </div>
        </div>
        <div className={styles.right}>
          <div className={styles.title}>热区管理</div>
          <div style={{ minHeight: canvasHeight.current }}>
            {rectConfig.map((item, index) => {
              return (
                <div className={styles.hotZone} key={index}>
                  <div className={styles.title}>热区0{index + 1}:</div>
                  <div className={styles.inputContainer}>
                    <Form style={{ width: '100%' }} field={field}>
                      <Form.Item
                        required
                        name={`hotUrl${index}`}
                        requiredMessage={'请输入热区链接'}
                        validator={urlRegularCheck}
                      >
                        <Input
                          name={`hotUrl${index}`}
                          className={[
                            styles.input,
                            (item.id || item.rectId) === selectedRectId ? 'next-focus' : '',
                          ].join(' ')}
                          value={item.url}
                          placeholder={'请输入热区链接'}
                          onFocus={() => {}}
                          onChange={(val) => {
                            rectConfig[index].url = val;
                            setRectConfig([...rectConfig]);
                          }}
                        />
                      </Form.Item>
                    </Form>

                    <i className="iconfont icon-icon-07 btn-del" onClick={() => deleteObject(index, canvas)} />
                  </div>
                </div>
              );
            })}
          </div>
          <div className={styles.clearBtn}>
            <Button onClick={clearAll} style={{ width: '100%' }}>
              清空热区
            </Button>
            <div className={styles.tip}>注：链接需符合《海报及广告链接规范》</div>
          </div>
        </div>
      </div>
      <Divider />
      <div className={styles.footer}>
        <Button type={'primary'} onClick={saveDraw}>
          确定
        </Button>
        <Button onClick={onClose}>取消</Button>
      </div>
    </div>
  );
};
