import { memberSavePageJson } from '@/api/caltratePop';
import LzImageSelector from '@/components/LzImageSelector';
import { Button, Card, Divider, Message, Radio, Dialog, Form, Input, Field,Checkbox } from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';
import LzDialog from '@/components/LzDialog';
import Plan from '../../compoonets/ProductPlan';
import { deepCopy } from '@/utils';
import { getShop } from '@/utils/shopUtil';
import EditHotZone from '@/pages/decorate/compoonets/EditHotZone';
import LzTipPanel from '@/components/LzTipPanel';

const RadioGroup = Radio.Group;
const TIP = (
  <div>
    <div style={{ fontWeight: 'bold', fontSize: 14, marginBottom: 5 }}>说明: </div>
    <div>1.建议宽度为750px</div>
    <div>2.大小不超过1M图片类型为jpg、png</div>
  </div>
);
function MemberPointExchange({ data, dispatch, allJson, defaultData, levelInfo, defaultGoodsList }) {
  console.log(data.goodsAct.goodsList,'00000');
  const pageinfo = defaultData.filter((item) => item.pageType == data.pageType);
  const [visible, setVisible] = React.useState(false);
  const [hotvisible, setHotVisible] = React.useState(false);
  const [editValue, setEditValue] = React.useState() as any;
  // @ts-ignore
  const [getLevel, setGetLevel] = React.useState('new');
  const [selectCouponList, setSelectCouponList] = React.useState(
    data.goodsAct.goodsList.filter((item) => item.getLevel == 'new'),
  );

  const [editData, setEditData] = React.useState({});
  const [editDataIndex, setEditDataIndex] = React.useState(0);

  const setData = (value) => {
    setSelectCouponList(value.goodsAct.goodsList.filter((item) => item.getLevel == getLevel));
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  const [pageLoading, setPageLoading] = React.useState(false);
  React.useEffect(() => {}, []);
  const field = Field.useField();
  const addItem = () => {
    const obj = {
      activityId: `2504${getShop().shopId}88`,
      couponUrl: '',
      couponId: '',
      getLevel,
      giftId: '',
      id: '',
      imgUrl: '',
      showLevel: getLevel == 'new' ? '[1]' : '[2]',
      sort: 1,
      type: '1',
      venderId: getShop().venderId,
      shopId: getShop().shopId,
      name: '',
      // 兑换所需积分
      pointLimit: 0,
      totalNum: 0,
      // 每人限领份数
      personLimit: 1,
    };
    data.goodsAct.goodsList.push(obj);
    defaultGoodsList.goodsList.push(JSON.parse(JSON.stringify(obj)));
    setData(data);
  };

  const isValidCoupon = () => {
    let err = false;

    data.goodsAct.goodsList.forEach((e, index) => {
      console.log(e);
      if (!e.imgUrl) {
        console.log(1);
        Message.error(`奖品${index + 1} 未设置奖品图`);
        err = true;
      }
      if (!e.name) {
        console.log(1);
        Message.error(`奖品${index + 1} 未设置奖品名称`);
        err = true;
      }
      if (!e.pointLimit) {
        Message.error(`奖品${index + 1} 未设置兑换所需积分`);
        err = true;
      }
      if (!e.personLimit || e.personLimit == '0' || e.personLimit < 0) {
        Message.error(`奖品${index + 1} 未设置每人限领份数`);
        err = true;
      }
      if (e.type == '2' && !e.couponUrl) {
        console.log(2);
        err = true;
      }
      if (e.type == '1' && !e.couponId) {
        console.log(3);
        Message.error(`奖品${index + 1} 未绑定奖品`);
        err = true;
      }
      if (!JSON.parse(e.showLevel).length) {
        console.log(4);
        err = true;
      }
    });
    if (err) {
      return false;
    }
    return true;
  };

  const deleteHotZoneUrl = (dataIndex, hotZoneIndex) => {
    data.content.hotZone[dataIndex].hotZoneList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };

  const deleteHotZone = (dataIndex) => {
    data.content.hotZone.splice(dataIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };

  const addHotZone = (dataIndex) => {
    const info = data.content.hotZone[dataIndex];
    if (!info.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditData(info);
    setEditDataIndex(dataIndex);
    setHotVisible(true);
  };

  const handleAddImage = () => {
    // const updatedDataList = data.content.hotZone;
    data.content.hotZone.push({ bg: '', hotZoneList: [] });
    dispatch({ type: 'UPDATE_MODULE', payload: data });
  };
  const isValidSwiper = () => {
    let err = false;

    data.content.hotZone.forEach((e) => {
      if (!e.bg) {
        err = true;
      }
      if (!e.hotZoneList.length) {
        err = true;
      }
    });
    if (err) {
      return false;
    }
    return true;
  };
  const publish = (): any => {
    // if (!data.content.background.imgUrl) {
    //   Message.error('请上传图片');
    //   return false;
    // }
    if (!data.goodsAct.goodsList.length) {
      Message.error('请配置奖品');
      return false;
    }
    if (!isValidCoupon()) {
      // Message.error('请正确配置奖品');
      return false;
    }
    if (data.content.hotZone.length && !isValidSwiper()) {
      Message.error('请完善热区信息');
      return false;
    }
    delete data[0];
    delete data[1];
    delete data[2];
    const update = deepCopy(data);
    delete update.goodsAct;
    const updateJson = allJson.filter((item) => item.pageType != data.pageType);
    updateJson.push(update);
    memberSavePageJson({ pageInfo: JSON.stringify(updateJson), goodsList: data.goodsAct.goodsList })
      .then((res) => {
        Message.success('发布成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };
  const commonProps = {
    title: data.pageName,
    extra: (
      <div>
        <RadioGroup
          value={data.showType}
          onChange={(showType) => {
            data.showType = showType;
            dispatch({ type: 'UPDATE_MODULE', payload: data.showType });
          }}
        >
          <Radio id="0" value={0}>
            全显示
          </Radio>
          <Radio id="1" value={1}>
            限新客
          </Radio>
          <Radio id="2" value={2}>
            限老客
          </Radio>
          <Radio id="3" value={3}>
            不显示
          </Radio>
        </RadioGroup>
        <Button type="primary" onClick={publish} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            {/*<div className={styles.kvContainer}>*/}
            {/*  <div className="crm-label">背景图</div>*/}
            {/*  <div className={styles.imgUpload}>*/}
            {/*    <LzImageSelector*/}
            {/*      // width={750}*/}
            {/*      // height={1077}*/}
            {/*      value={data.content.background.imgUrl}*/}
            {/*      onChange={(img:any) => {*/}
            {/*        data.content.background.imgUrl = img;*/}
            {/*        setData(data);*/}
            {/*      }}*/}
            {/*    />*/}
            {/*    <div className={styles.tip}>*/}
            {/*      <div>图片尺寸：仅支持宽度为750px</div>*/}
            {/*      <div>图片格式：大小不超过1M图片类型为jpg、png</div>*/}
            {/*    </div>*/}
            {/*  </div>*/}
            {/*  <div className={styles.colorPicker}>*/}
            {/*    <Button*/}
            {/*      style={{ marginLeft: '35px' }}*/}
            {/*      type={'primary'}*/}
            {/*      text*/}
            {/*      onClick={() => {*/}
            {/*        data.content.background.imgUrl = pageinfo[0].content.background.imgUrl;*/}
            {/*        setData(data);*/}
            {/*      }}*/}
            {/*    >*/}
            {/*      重置*/}
            {/*    </Button>*/}
            {/*  </div>*/}
            {/*</div>*/}
            <div className={styles.kvContainer}>
              <div className="crm-label">奖品背景图</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={750}
                  height={740}
                  value={data.content.changeContentBg.imgUrl}
                  onChange={(img:any) => {
                    data.content.changeContentBg.imgUrl = img;
                    setData(data);
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：仅支持宽度为750px，高度为740px</div>
                  <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                </div>
              </div>
              <div className={styles.colorPicker}>
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    data.content.background.imgUrl = pageinfo[0].content.background.imgUrl;
                    setData(data);
                  }}
                >
                  重置
                </Button>
              </div>
            </div>
          </div>
          <Divider />
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">奖品配置</div>
              <div className={styles.colorPicker}>
                <div style={{ marginBottom: '10px' }}>
                  <Button type={'primary'} onClick={() => addItem()} disabled={selectCouponList.length >= 20}>
                    添加（{selectCouponList.length}/20）
                  </Button>
                  <span className={'tip'} style={{ marginLeft: 5 }}>
                    注：最多可添加20个奖品, 最少保留1份
                  </span>
                </div>
              </div>
              <div>
                {data.goodsAct.goodsList.map((item, index) => {
                  return (
                    <div key={index}  hidden={item.getLevel != getLevel}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px', marginTop: '10px',position:'relative' }}>
                        <div className="crm-label">奖品{index + 1}配置</div>
                        <Button type={'primary'} text style={{position: 'absolute', right: '10px',top:'0px'}}>
                          <i
                            className="iconfont icon-icon-07 btn-del"
                            onClick={() => {
                              data.goodsAct.goodsList.splice(index, 1);
                              console.log(defaultGoodsList, '*******');
                              defaultGoodsList.goodsList.splice(index, 1);
                              setData(data);
                            }}
                          />
                        </Button>
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px',background:'#edf2fa',borderRadius:'5px',padding:'10px' }}>
                        <div >
                          <div className={styles.imgUpload} style={{ marginBottom: '10px' }}>
                            <span className={'tip'} style={{ marginRight: '5px' }}>
                              奖品图片
                            </span>
                            <div className={styles.imgUpload}>
                              <LzImageSelector
                                width={200}
                                height={200}
                                value={item.imgUrl}
                                onChange={(imgSrc) => {
                                  item.imgUrl = imgSrc;
                                  setData(data);
                                }}
                              />
                              <div style={{ marginLeft: '5px' }}>
                                <div className={styles.tip}>
                                  <div>图片尺寸：仅支持宽度为200px，高度为200px</div>
                                  <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                                </div>
                                {item.type === '1' && (
                                  <Button
                                    type={'primary'}
                                    disabled={item.id != ''}
                                    onClick={() => {
                                      setEditValue(item);
                                      setVisible(true);
                                    }}
                                  >
                                    选择奖品
                                  </Button>
                                )}{' '}
                                <div className={styles.buttonDiv}>
                                  <Button
                                    style={{ marginLeft: '10px', marginRight: '10px' }}
                                    type={'primary'}
                                    text
                                    onClick={() => {
                                      const resetData = JSON.parse(JSON.stringify(defaultGoodsList.goodsList[index]));
                                      item.imgUrl = resetData.imgUrl;
                                      item.showLevel = resetData.showLevel;
                                      item.getLevel = resetData.getLevel;
                                      item.type = resetData.type;
                                      item.couponUrl = resetData.couponUrl;
                                      item.couponId = resetData.couponId;
                                      setData(data);
                                    }}
                                  >
                                    重置
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center' }}>
                            <span className={'tip'} style={{ marginRight: '5px', marginBottom: '16px' }}>
                              可领取等级设置
                            </span>
                            <Form.Item name="showlevel" label="" style={{ marginRight: '5px' }}>
                              <Checkbox.Group
                                value={JSON.parse(item.showLevel)}
                                onChange={(checkedValues) => {
                                  item.showLevel = JSON.stringify(checkedValues);
                                  setData(data);
                                }}
                              >
                                {levelInfo.map((option) => (
                                  <Checkbox key={option.level} value={option.level}>
                                    {option.levelName}
                                  </Checkbox>
                                ))}
                              </Checkbox.Group>
                            </Form.Item>
                          </div>
                        </div>
                        <div >
                          <div className={styles.imgUpload}>
                            <span className={'tip'} style={{ marginRight: '5px' }}>
                              奖品名字
                            </span>
                            <Form style={{ width: '100px', marginTop: '10px' }} field={field}>
                              <Form.Item required name={`name`} requiredMessage={'请输入名字'}>
                                <Input
                                  style={{ width: '100px' }}
                                  name={`name`}
                                  value={item.name}
                                  placeholder={'请输入名字'}
                                  maxLength={7}
                                  onFocus={() => {}}
                                  onChange={(val) => {
                                    item.name = val;
                                    setData(data);
                                  }}
                                />
                              </Form.Item>
                            </Form>
                          </div>
                          <div className={styles.imgUpload}>
                            <span className={'tip'} style={{ marginRight: '5px' }}>
                              兑换积分
                            </span>
                            <Form style={{ width: '100px', marginTop: '10px' }} field={field}>
                              <Form.Item required name={`pointLimit${index}`} requiredMessage={'请输入兑换需要消耗积分'}>
                                <Input
                                  type={'number'}
                                  style={{ width: '100px' }}
                                  name={`pointLimit${index}`}
                                  value={item.pointLimit}
                                  placeholder={'请输入兑换需要消耗积分'}
                                  maxLength={7}
                                  disabled={item.id}
                                  onFocus={() => {}}
                                  onChange={(val:any) => {
                                    const numericRegex = /^[0-9]*$/;
                                    const newValueIsNumeric = numericRegex.test(val);

                                    if (newValueIsNumeric) {
                                      item.pointLimit = val;
                                      setData(data);
                                    }
                                  }}
                                />
                              </Form.Item>
                            </Form>
                          </div>
                          <div className={styles.imgUpload}>
                            <span className={'tip'} style={{ marginRight: '5px' }}>
                              每人限领份数
                            </span>
                            <Form style={{ width: '100px', marginTop: '10px' }} field={field}>
                              <Form.Item required name={`personLimit`} requiredMessage={'请输入兑换需要消耗积分'}>
                                <Input
                                  type={'number'}
                                  style={{ width: '100px' }}
                                  name={`personLimit`}
                                  value={item.personLimit}
                                  placeholder={'请输入兑换需要消耗积分'}
                                  maxLength={2}
                                  disabled={item.id}
                                  onFocus={() => {}}
                                  onChange={(val:any) => {
                                    const numericRegex = /^[0-9]*$/;
                                    const newValueIsNumeric = numericRegex.test(val);

                                    if (newValueIsNumeric) {
                                      item.personLimit = val;
                                      setData(data);
                                    }
                                  }}
                                />
                              </Form.Item>
                            </Form>
                          </div>
                        </div>
                        <div />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <Dialog
            title="选择奖品"
            footer={false}
            shouldUpdatePosition
            visible={visible}
            onClose={() => setVisible(false)}
          >
            <Plan
              onSubmit={(val) => {
                console.log(val);
                editValue.couponId = val.skuCode;
                editValue.totalNum = val.quantityAvailable;
                editValue.name = val.skuName;
                setData(data);
                setVisible(false);
              }}
            />
          </Dialog>
        </Card.Content>
      </Card>
      <Card free>
        <Divider />
        <Card.Content>
          <div className="crm-label">热区配置</div>
          <LzTipPanel message={TIP} />
          <Button className={styles.addBtn} onClick={handleAddImage} disabled={data.content.hotZone.length >= 3}>
            添加图片（{data.content.hotZone.length} / 3）
          </Button>

          <div className={styles.operation}>
            {data.content.hotZone.map((hotdata, dataIndex) => (
              <div className={styles.hotContainer} key={dataIndex}>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      width={750}
                      bgWidth={158}
                      value={hotdata.bg}
                      onChange={(bg) => {
                        const updatedDataList = data.content.hotZone;
                        updatedDataList[dataIndex].bg = bg;
                        updatedDataList[dataIndex].hotZoneList = [];
                        dispatch({ type: 'UPDATE_MODULE', payload: data });
                      }}
                    />
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addHotZone(dataIndex)}>
                      添加热区
                    </Button>
                    {hotdata.hotZoneList.map((item, hotZoneIndex) => (
                      <div key={hotZoneIndex} className={styles.urlContainer}>
                        <div className={styles.url}>{`热区0${hotZoneIndex + 1}：${item.url}`}</div>
                        <i
                          className="iconfont icon-icon-07 btn-del"
                          onClick={() => deleteHotZoneUrl(dataIndex, hotZoneIndex)}
                        />
                      </div>
                    ))}
                  </div>
                  <div className={styles.removeHotZone}>
                    <i className="iconfont icon-icon-07 btn-del" onClick={() => deleteHotZone(dataIndex)} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={hotvisible}
        footer={false}
        onClose={() => setHotVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          data={editData}
          dataList={data.content.hotZone}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setHotVisible(false)}
        />
      </LzDialog>
      {/* <LzDialog */}
      {/*  title={'人群限制'} */}
      {/*  visible={peopleVisible} */}
      {/*  footer={false} */}
      {/*  onClose={() => setPeopleVisible(false)} */}
      {/*  style={{ width: '500px' }} */}
      {/* > */}
      {/*  <SettingPeople */}
      {/*    dataList={data.content.hotZone} */}
      {/*    dataIndex={editDataIndex} */}
      {/*    dispatch={dispatch} */}
      {/*    onClose={() => setPeopleVisible(false)} */}
      {/*  /> */}
      {/* </LzDialog> */}
    </div>
  );
}

export default MemberPointExchange;
