import React, {useEffect, useState} from 'react';
import {Form, DatePicker2, Input, Field, Table, Button} from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import {combinedChannelQuery, combinedChannelExport} from '@/api/swisse';
import {deepCopy, downloadExcel} from '@/utils';
import dayJs from 'dayjs';

const FormItem = Form.Item;
const {RangePicker} = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [columns, setColumns] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const defaultRangeVal = [
    dayJs().subtract(1, 'month').format('YYYY-MM-DD'),
    dayJs().format('YYYY-MM-DD'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({...formValue, ...defaultPage});
  };

  const transformColumns = (columnItems) => {
    // 深拷贝源数组以避免修改原数据
    const clonedColumns = JSON.parse(JSON.stringify(columnItems));
    if (clonedColumns.length === 0) return clonedColumns;

    // 处理第一个根节点（添加 lock 和 align）
    const firstNode = clonedColumns[0];
    firstNode.lock = 'left';
    firstNode.align = 'center';
    firstNode.width = '120px';

    // 初始化队列并设置计数器
    const queue = [...clonedColumns];
    let counter = 1;

    // 若第一个节点是叶子节点，设置 dataIndex
    if (!firstNode.children?.length) {
      firstNode.dataIndex = `line${counter++}`;
    }
    // 层序遍历处理其他节点
    while (queue.length > 0) {
      const node = queue.shift();
      // 跳过已处理的首节点
      if (node === firstNode) {
        if (node.children?.length) queue.push(...node.children);
        continue;
      }
      // 为所有非首节点添加 align
      node.align = 'center';
      node.width = '150px';
      // 处理叶子节点的 dataIndex
      if (!node.children?.length) {
        node.dataIndex = `line${counter++}`;
      } else {
        // 非叶子节点则将其子节点入队
        queue.push(...node.children);
      }
    }
    return clonedColumns;
  };
  const transformArray = (arr: any[]): any[] => {
    return arr.map((item) => {
      if (item?.children) {
        item.children = transformArray(item.children);
        if (item.children.length === 1 && !item.children[0].children) {
          const {children, ...rest} = item;
          return rest;
        }
      }
      return item;
    });
  };
  const generateColumnsFromLists = (list: any[]): any[] => {
    const [list1, list2] = list;
    // 构建动态分组结构
    let currentSection = null;
    let currentPhase = null;
    const sections = [];

    for (let i = 1; i < list1.length; i++) {
      const sectionTitle = list1[i];
      const phaseTitle = list2[i];

      // 处理段变化
      if (!currentSection || currentSection.title !== sectionTitle) {
        currentSection && sections.push(currentSection);
        currentSection = {title: sectionTitle, phases: []};
        currentPhase = {title: phaseTitle, items: []};
        currentSection.phases.push(currentPhase);
      }

      // 处理阶段变化
      if (currentPhase.title !== phaseTitle) {
        currentPhase = {title: phaseTitle, items: []};
        currentSection.phases.push(currentPhase);
      }

      currentPhase.items.push(phaseTitle);
    }
    currentSection && sections.push(currentSection); // 添加最后一个段

    // 转换为目标结构
    const _columns = [{title: list1[0]}];
    sections.forEach((section) => {
      const entry = {title: section.title} as any;
      if (section.phases.length === 1 && section.phases[0].title === section.title) {
        // 直接子项结构
        entry.children = section.phases[0].items.map((item) => ({title: item}));
      } else {
        // 嵌套阶段结构
        entry.children = section.phases.map((phase) => ({
          title: phase.title,
          children: phase.items.map((item) => ({title: item})),
        }));
      }
      _columns.push(entry);
    });
    transformArray(_columns);
    return _columns;
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.shopId = '1000147201';
    query.activityMainId = '1909594994350538753';
    query.startDate = dayJs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endDate = dayJs(query.dateRange[1]).format('YYYY-MM-DD');

    combinedChannelQuery(query)
      .then((res: any): void => {
        pageInfo.total = res.total;
        pageInfo.pageSize = res.pageSize;
        pageInfo.pageNum = res.pageNum;
        setPage(pageInfo);
        const _columnsTitle = generateColumnsFromLists(res.title);
        const _tableData = res.content.map((item) => {
          const row = {};
          item.forEach((data, index) => {
            row[`line${index + 1}`] = data;
          });
          return row;
        });
        setTableData(_tableData);
        setColumns(transformColumns(_columnsTitle));
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({pageSize, pageNum}) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({...formValue, pageSize, pageNum});
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.shopId = '1000147201';
    formValue.activityMainId = '1909594994350538753';
    formValue.startDate = dayJs(formValue.dateRange[0]).format('YYYY-MM-DD');
    formValue.endDate = dayJs(formValue.dateRange[1]).format('YYYY-MM-DD');
    combinedChannelExport(formValue).then((data: any) => downloadExcel(data, '活动渠道数据'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({...formValue, ...defaultPage});
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="activityId" label="activityId">
          <Input placeholder="活动ID"/>
        </Form.Item>
        <FormItem name="dateRange" label="查询时间">
          <RangePicker
            hasClear={false}
            defaultValue={defaultRangeVal}
            format="YYYY-MM-DD"
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({...formValue, ...defaultPage});
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{margin: '10px 0', textAlign: 'right'}}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table.StickyLock columns={columns} dataSource={tableData} loading={loading} style={{marginTop: 20}}/>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
