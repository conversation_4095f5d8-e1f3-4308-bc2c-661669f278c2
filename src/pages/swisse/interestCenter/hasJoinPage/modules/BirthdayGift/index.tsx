import React from 'react';
import { Card, Button, Divider, Loading, Table, Message, Tab, Form, DatePicker2, Field, Balloon } from '@alifd/next';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import LzDialog from '@/components/LzDialog';
import { addDecorationData } from '@/api/swisseMemberCenter';
import format from '@/utils/format';
import constant from '@/utils/constant';
import dayjs from 'dayjs';
import { PrizeInfo } from '../../utils';
import ChoosePrize from '../../compoonets/ChoosePrize';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
export default ({ data, defaultData, dispatch }) => {
  const choosePrizeList = data.memberLevels.reduce((acc, cur) => {
    return acc.concat(cur.giftPackage.prizeList);
  }, []);
  const field: Field = Field.useField();
  const [loading, setLoading] = React.useState(false);
  const [editData, setEditData] = React.useState<PrizeInfo | null>(null);
  const [editOldData, setEditOldData] = React.useState(null);
  const [visible, setVisible] = React.useState(false);
  const [editDataIndex, setEditDataIndex] = React.useState(0);
  const [levelIndex, setLevelIndex] = React.useState(data.currentLevel);

  const saveValidate = () => {
    if (!data.startTime || !data.endTime) {
      Message.error('请选择投放时间');
      return false;
    }
    for (const level of data.memberLevels) {
      if (!level.giftPackage.prizeList.length) {
        Message.error(`请添加${level.level}的奖品配置`);
        return false;
      }
      for (const [index, prize] of level.giftPackage.prizeList.entries()) {
        if (!prize.prizeName) {
          Message.error(`请完善${level.level}的奖品配置，序号为${index + 1}`);
          return false;
        }
      }
    }
    return true;
  };

  const saveSetting = (): any => {
    console.log('data', data);
    if (!saveValidate()) {
      return false;
    }
    debugger;
    setLoading(true);
    addDecorationData({
      decorationData: JSON.stringify(data),
      pageType: 2,
      moduleId: 7,
      startTime: data.startTime,
      endTime: data.endTime,
      modulePrizeList: choosePrizeList,
    })
      .then((res) => {
        Message.success('生日礼模块保存成功');
        dispatch({ type: 'RESET_PUSH' });
        setLoading(false);
      })
      .catch((e) => {
        Message.error(e.message);
        setLoading(false);
      });
  };

  const onSubmit = (value) => {
    if (!value.prizeName) {
      value.prizeName = value.planName;
    }
    data.memberLevels[levelIndex].giftPackage.prizeList[editDataIndex] = {
      ...value,
      moduleId: 7,
      prizeLevel: Number(levelIndex) + 2, // 加2是因为从v2等级2开始
      sortId: data.memberLevels[levelIndex].giftPackage.prizeList[editDataIndex].sortId,
    };
    setData({ ...data });
    setVisible(false);
  };

  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setData({
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };
  const setData = (value, isUpdated = true) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value, isUpdated });
  };

  const commonProps = {
    title: '生日礼',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          更新至当前设置
        </Button>
      </div>
    ),
  };

  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: '100%' }}>
            <div className={styles.operation}>
              <div className={styles.MemberContainer}>
                <div className="crm-label">投放时间</div>
                <Form field={field}>
                  <FormItem extra={<div className="next-form-item-help">模块在C端显示的时间</div>}>
                    <RangePicker
                      className="w-300"
                      name="rangeDate"
                      inputReadOnly
                      format={dateFormat}
                      hasClear={false}
                      showTime
                      value={[data.startTime, data.endTime]}
                      onChange={onDataRangeChange}
                      disabledDate={(date) => {
                        return date.valueOf() < dayjs().subtract(1, 'day').valueOf();
                      }}
                    />
                  </FormItem>
                </Form>
              </div>

              <div className={styles.MemberContainer}>
                <Tab
                  shape={'pure'}
                  defaultActiveKey={levelIndex.toString()}
                  onClick={(key) => {
                    setLevelIndex(Number(key));
                    setData({ currentLevel: Number(key) }, false);
                  }}
                >
                  {data.memberLevels.map((item, index) => {
                    return (
                      <Tab.Item title={item.name} key={index}>
                        <div key={`${item.name}-${index}`} className={styles.EquityContainer}>
                          <div className={styles.MemberContainer}>
                            <div className="crm-label">背景图片上传</div>
                            <div className={styles.imgUpload}>
                              <LzImageSelector
                                bgWidth={200}
                                bgHeight={140}
                                value={item.giftPackage.bg}
                                onChange={(bg) => {
                                  data.memberLevels[levelIndex].giftPackage.bg = bg;
                                  setData({ ...data });
                                }}
                              />
                              <div className={styles.tip}>
                                <div>图片尺寸：建议宽度为706px，高度为434px</div>
                                <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                              </div>
                            </div>
                          </div>
                          <div className={styles.MemberContainer}>
                            <div className="crm-label">按钮图片上传</div>
                            <div className={styles.imgUpload}>
                              <LzImageSelector
                                bgWidth={200}
                                bgHeight={140}
                                value={item.giftPackage.btn}
                                onChange={(btn) => {
                                  data.memberLevels[levelIndex].giftPackage.btn = btn;
                                  setData({ ...data });
                                }}
                              />
                              <div className={styles.tip}>
                                <div>图片尺寸：建议宽度为706px，高度为434px</div>
                                <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                              </div>
                            </div>
                          </div>
                          <div className={styles.MemberContainer}>
                            <div className="crm-label">奖品配置</div>
                            <Button
                              type={'primary'}
                              onClick={() => {
                                setEditData(null);
                                setEditOldData(null);
                                data.memberLevels[levelIndex].giftPackage.prizeList.push({
                                  sortId: item.giftPackage.prizeList.length
                                    ? item.giftPackage.prizeList[item.giftPackage.prizeList.length - 1].sortId + 1
                                    : 1,
                                });
                                setData(data);
                              }}
                              disabled={
                                item.giftPackage.prizeList.length >= 1 ||
                                item.giftPackage.prizeList.some((it) => !it.skuName) ||
                                item.giftPackage.prizeList.some((it) => !it.prizeImg)
                              }
                            >
                              添加奖项（{item.giftPackage.prizeList.length} / 1）
                            </Button>
                            <Table.StickyLock dataSource={item.giftPackage.prizeList} style={{ marginTop: '15px' }}>
                              <Table.Column
                                title="序号"
                                lock="left"
                                width={100}
                                align={'center'}
                                cell={(_, indexPrizeList) => <div>{indexPrizeList + 1}</div>}
                              />
                              <Table.Column
                                title="奖品名称"
                                dataIndex="prizeName"
                                width={130}
                                cell={(_, indexPrizeList, row) => (
                                  <div>
                                    <Balloon
                                      v2
                                      triggerType="hover"
                                      trigger={<div className={styles.ellipsis}>{row.prizeName}</div>}
                                      closable={false}
                                    >
                                      {row.prizeName}
                                    </Balloon>
                                  </div>
                                )}
                              />
                              <Table.Column title="发放份数" width={130} dataIndex="sendTotalCount" />
                              <Table.Column
                                title="单份价值(元)"
                                width={130}
                                cell={(_, index1, row) => (
                                  <>
                                    {row.prizeType != 0 && (
                                      <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>
                                    )}
                                  </>
                                )}
                              />
                              <Table.Column
                                title="奖品图"
                                width={130}
                                cell={(_, index1, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                              />
                              <Table.Column
                                title="操作"
                                lock={'right'}
                                align={'center'}
                                width={240}
                                cell={(_, i, row) => (
                                  <div>
                                    <Button
                                      type={'primary'}
                                      text
                                      style={{ marginLeft: 15 }}
                                      onClick={() => {
                                        setEditData(row.prizeName ? row : null);
                                        setEditOldData(defaultData.memberLevels[levelIndex].giftPackage.prizeList[i]);
                                        setEditDataIndex(i);
                                        setVisible(true);
                                      }}
                                    >
                                      编辑
                                    </Button>
                                    <Button
                                      type={'primary'}
                                      text
                                      style={{ marginLeft: 15 }}
                                      onClick={() => {
                                        data.memberLevels[levelIndex].giftPackage.prizeList.splice(i, 1);
                                        setData(data);
                                      }}
                                    >
                                      删除
                                    </Button>
                                  </div>
                                )}
                              />
                            </Table.StickyLock>
                          </div>
                        </div>
                      </Tab.Item>
                    );
                  })}
                </Tab>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>

      <LzDialog
        title={'选择奖项'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '700px' }}
      >
        <ChoosePrize
          choosePrizeList={[]}
          typeList={[1, 3, 7, 8]}
          initTarget={1}
          hasProbability={false}
          hasLimit={false}
          editValue={editData}
          editDefaultValue={editOldData}
          onChange={onSubmit}
          model={'single'}
          onCancel={() => setVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
