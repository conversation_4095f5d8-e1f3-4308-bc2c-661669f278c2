import { prizeFormLayout } from '@/components/ChoosePrize';
import LzImageSelector from '@/components/LzImageSelector';
import { numRegularCheckInt } from '@/utils';
import format from '@/utils/format';
import { Button, Dialog, Field, Form, Grid, Input, Message, NumberPicker, Radio } from '@alifd/next';
import React, { useEffect, useReducer, useState } from 'react';
import active from '../assets/active.png';
import delIcon from '../assets/del-icon.png';
import notActive from '../assets/not-active.png';
import Plan from './components/Plan';
import styles from './index.module.scss';
import { getCardPageListAll } from '@/api/prize';

interface ComponentProps {
  [propName: string]: any;
}

// eslint-disable-next-line complexity
const PropertyGiftCard = ({
  editValue,
  editDefaultValue,
  onChange,
  onCancel,
  planList = [],
  hasProbability = true,
  hasLimit = true,
  width,
  height,
  prizeKeyList,
  isEdit,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/199905/32/493/7224/61039d4fE6c987585/02de76ea362183d6.png';
  const planImg = require('../assets/6.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 7,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): void | boolean => {
    if (prizeData.prizeKey) {
      if (prizeData.sendTotalCount > prizeData.cardSurplus) {
        Message.error(`发放份数不能大于最大可绑定数量${prizeData.cardSurplus}`);
        return false;
      }
      if (prizeData.dayLimitType === 2 && prizeData.dayLimit > prizeData.sendTotalCount) {
        Message.error(`每日限额不能大于发放份数`);
        return false;
      }
    }

    if (hasProbability && +prizeData.probability <= 0) {
      Message.error(`中奖概率必须大于0`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = async () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    setPrizeData(data);
    // 删除计划
    if (planList.indexOf(prizeData.cardCode) > -1) {
      planList.splice(planList.indexOf(prizeData.cardCode), 1);
    }
  };
  const onSubmit = (resource: any): any => {
    if (!resource) {
      return;
    }
    // 选择计划判断
    if (planList.indexOf(resource.cardCode) === -1) {
      // 添加计划记录
      planList.push(resource.cardCode);
      resource.prizeKey = resource.cardCode;
      setPrizeData(resource);
      setWinJdShow(false);
      field.setErrors({ prizeKey: '' });
    } else {
      Message.error('该计划已被选择，请重新选择');
      return false;
    }
  };

  useEffect(() => {
    if (editValue) {
      getCardPageListAll({ cardCode: editValue.cardCode, pageNum: 1, pageSize: 10 }).then((data) => {
        if (data?.records) {
          const prize = data?.records[0] ?? null;
          if (prize) {
            setData({
              cardSurplus:
                Number(prize.cardTotal) - Number(prize.quantityFreeze) + (editDefaultValue?.sendTotalCount ?? 0),
              planStatus: prize.planStatus,
            });
          }
        }
      });
    }
  }, []);

  return (
    <div className={styles.PropertyGiftCard}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item required requiredMessage="请选择礼品卡" style={{ paddingTop: '15px' }}>
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
              <img className={styles.prizeBg} src={prizeData.planStatus === 2 ? active : notActive} alt="" />
              {!isEdit && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ width: 210 }}>
                <p>礼品卡名称：{prizeData.cardName}</p>
                <p>礼品卡code：{prizeData.cardCode}</p>
                <p className="lz-multi-ellipsis--l2">礼品卡描述：{prizeData.cardDesc}</p>
                <p>创建时间：{format.formatDateTimeDayjs(prizeData.createTime)}</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>
                  有效期：
                  {prizeData.startTime
                    ? `${format.formatDateTimeDayjs(prizeData.startTime)}至
                  ${format.formatDateTimeDayjs(prizeData.endTime)}`
                    : '永久有效'}
                </p>
                <p>最大可绑定数量：{prizeData.cardSurplus}</p>
                <p>激活方式：{prizeData.cardType ? '卡号和密码' : '仅卡号'}</p>
              </div>
            </div>
          )}
        </Form.Item>

        <Form.Item label="单份价值" required requiredMessage="请输入单份价值">
          <NumberPicker
            className={styles.formNumberPicker}
            onChange={(unitPrice: any) => setData({ unitPrice })}
            name="unitPrice"
            type="inline"
            precision={2}
            max={9999999}
            min={0}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数" validator={numRegularCheckInt}>
          <NumberPicker
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={9999999}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount, unitCount: 1 })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  // 小于100且最多三位小数
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group defaultValue={prizeData.dayLimitType} onChange={(dayLimitType) => setData({ dayLimitType })}>
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={10}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            trim
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择礼品卡"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} prizeKeyList={prizeKeyList} />
      </Dialog>
    </div>
  );
};

export default PropertyGiftCard;
