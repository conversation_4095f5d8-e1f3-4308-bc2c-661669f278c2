export const urlRegExp = new RegExp(/^(http:\/\/|https:\/\/)(.*\.)?(isvjcloud\.com|isvjd\.com|jd\.com)(\/.*)?(\?.*)?$/);

export const urlRegularCheck = (rule: any, value: any, callback: any) => {
  if (value && !urlRegExp.test(value)) {
    callback(`链接只能以https://开头且应包含.isvjcloud.com、.isvjd.com或.jd.com`);
  } else {
    callback();
  }
};

export const objectToFormData = {
  9: 'memberCard',
  10: 'giftDisplay',
  11: 'equityIcon',
  17: 'popSetting',
};
