import React from 'react';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { Card, Button, Divider, Message, Loading } from '@alifd/next';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '@/pages/swisse/interestCenter/MedalPage/compoonets/EditHotZone';
import { addDecorationData } from '@/api/swisseMemberCenter';

export default ({ data, dispatch, id }) => {
  const [loading, setLoading] = React.useState(false);
  const [hotVisible, setHotVisible] = React.useState(false);
  const dataList = [data];

  const deleteHotZoneUrl = (hotZoneIndex) => {
    data.hotZoneList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: [...dataList] });
  };
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };

  const addHotZone = () => {
    if (!data.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setHotVisible(true);
  };

  const saveSetting = (): any => {
    // if (!dataList.length) {
    //   Message.error('请至少添加一张图片');
    //   return false;
    // }
    // if (!isValidSwiper()) {
    //   Message.error('请完善热区信息');
    //   return false;
    // }
    setLoading(true);
    addDecorationData({
      decorationData: JSON.stringify(data),
      moduleId: 16,
      pageType: 3,
    })
      .then((res) => {
        dispatch({ type: 'RESET_PUSH' });
        Message.success('勋章攻略模块保存成功');
        setLoading(false);
      })
      .catch((e) => {
        Message.error(e.message);
        setLoading(false);
      });
  };

  const commonProps = {
    title: '勋章赚取攻略模块',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存模块设置
        </Button>
      </div>
    ),
  };

  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: '100%' }}>
            <div className={styles.operation}>
              <div className={styles.MemberContainer}>
                <div className="crm-label">背景图设置</div>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      bgWidth={200}
                      bgHeight={140}
                      value={data.bg}
                      onChange={(bg) => {
                        setData({ bg });
                      }}
                    />
                    <div className={styles.tip}>
                      <div>图片尺寸：建议宽度为706px，高度为588px</div>
                      <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                    </div>
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addHotZone()}>
                      添加热区
                    </Button>
                    {data.hotZoneList.map((hot, hotZoneIndex) => (
                      <div key={hotZoneIndex} className={styles.urlContainer}>
                        <div className={styles.url}>{`热区${String(hotZoneIndex + 1).padStart(2, '0')}:${
                          hot.url
                        }`}</div>
                        <i className="iconfont icon-icon-07 btn-del" onClick={() => deleteHotZoneUrl(hotZoneIndex)} />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </Loading>
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={hotVisible}
        footer={false}
        onClose={() => setHotVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          data={data}
          dataIndex={0}
          dispatch={(val: any) => {
            console.log(val);
            setData({
              hotZoneList: val,
            });
            setHotVisible(false);
          }}
          onClose={() => setHotVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
