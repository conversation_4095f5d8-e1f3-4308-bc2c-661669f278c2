import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Balloon, Icon } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { selectPageData, selectListDataExport } from '@/api/swisseMemberCenterData';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const disabledDate = (date) => {
    return date && date >= dayJs().subtract(1, 'day');
  };
  const defaultRangeVal = [
    dayJs().subtract(8, 'day').format('YYYY-MM-DD'),
    dayJs().subtract(1, 'day').format('YYYY-MM-DD'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.startTime = dayJs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endTime = dayJs(query.dateRange[1]).format('YYYY-MM-DD');
    selectPageData(query)
      .then((res: any): void => {
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    setLoading(true);
    const formValue: any = field.getValues();
    console.log(formValue, 'formValue');
    const query = {
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      startTime: formValue.dateRange[0],
      endTime: formValue.dateRange[1],
    };
    selectListDataExport(query).then((data: any) => {
      downloadExcel(data, `常规数据（每日）`);
      setLoading(false);
    });
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="数据时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
            disabledDate={disabledDate}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" lock="left" />
        <Table.Column width={180} title="日期" dataIndex="dt" />
        <Table.Column width={80} title="PV" dataIndex="pv" />
        <Table.Column width={80} title="UV" dataIndex="uv" />
        <Table.Column width={180} title="UV且下单订单数（PLUS系列产品）" dataIndex="orderNumBySku" />
        <Table.Column width={180} title="UV且下单人数（PLUS系列产品）" dataIndex="orderUserBySku" />
        <Table.Column width={180} title="UV且下单金额（PLUS系列产品）" dataIndex="orderGmvBySku" />
        <Table.Column width={180} title="UV且下单订单数（全店）" dataIndex="orderNumByShop" />
        <Table.Column width={180} title="UV且下单人数（全店）" dataIndex="orderUserByShop" />
        <Table.Column width={180} title="UV且下单金额（全店）" dataIndex="orderGmvByShop" />
        <Table.Column width={100} title="卡面模块点击次数" dataIndex="memberCardClickNum" />
        <Table.Column width={100} title="卡面模块点击人数" dataIndex="memberCardClickUser" />
        <Table.Column width={100} title="升级进阶模块点击次数" dataIndex="upgradeAdvancedLevelClickNum" />
        <Table.Column width={100} title="升级进阶模块点击人数" dataIndex="upgradeAdvancedLevelClickUser" />
        <Table.Column width={100} title="勋章兑换展示模块点击次数" dataIndex="medalExchangeGiftClickNum" />
        <Table.Column width={100} title="勋章兑换展示模块点击人数" dataIndex="medalExchangeGiftClickUser" />
        <Table.Column width={100} title="专享价模块点击次数" dataIndex="exclusivePriceClickNum" />
        <Table.Column width={100} title="专享价模块点击人数" dataIndex="exclusivePriceClickUser" />
        <Table.Column width={100} title="首购礼模块点击次数" dataIndex="firstPurchaseGiftClickNum" />
        <Table.Column width={100} title="首购礼模块点击人数" dataIndex="firstPurchaseGiftClickUser" />
        <Table.Column width={100} title="满件礼模块点击次数" dataIndex="fullGiftClickNum" />
        <Table.Column width={100} title="满件礼模块点击人数" dataIndex="fullGiftClickUser" />
        <Table.Column width={100} title="生日礼模块点击次数" dataIndex="birthdayGiftClickNum" />
        <Table.Column width={100} title="生日礼模块点击人数" dataIndex="birthdayGiftClickUser" />
        <Table.Column width={100} title="规则模块点击次数" dataIndex="ruleSettingClickNum" />
        <Table.Column width={100} title="规则模块点击人数" dataIndex="ruleSettingClickUser" />
        <Table.Column width={100} title="备用模块点击次数" dataIndex="standbyClickNum" />
        <Table.Column width={100} title="备用模块点击人数" dataIndex="standbyClickUser" />
        <Table.Column width={100} title="门槛页点击次数" dataIndex="thresholdPageClickNum" />
        <Table.Column width={100} title="门槛页点击人数" dataIndex="thresholdPageClickUser" />
        <Table.Column width={100} title="主kv模块点击次数" dataIndex="mainKvClickNum" />
        <Table.Column width={100} title="主kv模块点击人数" dataIndex="mainKvClickUser" />
        <Table.Column width={100} title="用户模块点击次数" dataIndex="userModuleClickNum" />
        <Table.Column width={100} title="用户模块点击人数" dataIndex="userModuleClickUser" />
        <Table.Column width={100} title="实物奖品点击次数" dataIndex="medalGiftClickNum" />
        <Table.Column width={100} title="实物奖品点击人数" dataIndex="medalGiftClickUser" />
        <Table.Column width={100} title="健康增值权益点击次数" dataIndex="lifeRightsClickNum" />
        <Table.Column width={100} title="健康增值权益点击人数" dataIndex="lifeRightsClickUser" />
        <Table.Column width={100} title="攻略模块点击次数" dataIndex="strategyClickNum" />
        <Table.Column width={100} title="攻略模块点击人数" dataIndex="strategyClickUser" />
        <Table.Column width={100} title="锚点模块点击次数" dataIndex="anchorPointClickNum" />
        <Table.Column width={100} title="锚点模块点击人数" dataIndex="anchorPointClickUser" />
        <Table.Column width={100} title="勋章兑换展示模块点击次数" dataIndex="medalExchangeGiftClickNum" />
        <Table.Column width={100} title="勋章兑换展示模块点击人数" dataIndex="medalExchangeGiftClickUser" />
        <Table.Column width={100} title="备用模块2点击次数" dataIndex="standbyTwoClickNum" />
        <Table.Column width={100} title="备用模块2点击人数" dataIndex="standbyTwoClickUser" />
        <Table.Column width={100} title="备用模块3点击次数" dataIndex="standbyThreeClickNum" />
        <Table.Column width={100} title="备用模块3点击人数" dataIndex="standbyThreeClickUser" />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
