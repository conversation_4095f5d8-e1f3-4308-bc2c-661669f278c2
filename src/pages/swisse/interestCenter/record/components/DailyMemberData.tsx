import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { selectPageNewMember, selectPageNewMemberExport} from '@/api/swisseMemberCenterData';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';


const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const disabledDate = (date) => {
    return  date && date >= dayJs().subtract(1, 'day');
  };
  const defaultRangeVal = [
    dayJs().subtract(8, 'day').format('YYYY-MM-DD'),
    dayJs().subtract(1, 'day').format('YYYY-MM-DD'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.startTime = dayJs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endTime = dayJs(query.dateRange[1]).format('YYYY-MM-DD');
    selectPageNewMember  (query)
      .then((res: any): void => {
        console.log(res, 'res');
        for (let i = 0; i < res.records.length; i++) {
          res.records[i].num = i + 1;
        }
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    setLoading(true);
    const formValue: any = field.getValues();
    console.log(formValue, 'formValue');
    const query = {
      pageNum:pageInfo.pageNum,
      pageSize:pageInfo.pageSize,
      startTime:formValue.dateRange[0],
      endTime:formValue.dateRange[1],
    }
    selectPageNewMemberExport(query).then((data: any) => {
      downloadExcel(data, `新增会员数据（每日）`);
      setLoading(false);
    });
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
            disabledDate={disabledDate}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>
          导出
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column width={80} title="序号" dataIndex="num" />
        <Table.Column title="日期" dataIndex="dt" />
        <Table.Column title="新增会员数" dataIndex="newMemberTotal" />
        <Table.Column title="新增V1会员数" dataIndex="newMemberLevelOne" />
        <Table.Column title="新增V2会员数" dataIndex="newMemberLevelTwo" />
        <Table.Column title="新增V3会员数" dataIndex="newMemberLevelThree" />
        <Table.Column title="新增S1会员数" dataIndex="newMemberLevelFour" />
        <Table.Column title="新增S2会员数" dataIndex="newMemberLevelFive" />
        <Table.Column title="新增S3会员数" dataIndex="newMemberLevelSix" />
        {/*<Table.Column*/}
        {/*  title="新增时间"*/}
        {/*  dataIndex="winningTime"*/}
        {/*  cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.winningTime)}</div>}*/}
        {/*/>*/}
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
