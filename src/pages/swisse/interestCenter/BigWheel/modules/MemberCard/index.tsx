import React, { useState, useEffect } from 'react';
import { Card, Button, Divider, Loading, Table, Message, Dialog } from '@alifd/next';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import LzWheelAssets from '@/components/LzWheelAssets';
import LzDialog from '@/components/LzDialog';
// import EditMemberCard from '../../compoonets/EditMemberCard';
import LzColorPicker from '@/components/LzColorPicker';
import ChoosePrize from '../../../hasJoinPage/compoonets/ChoosePrize';
import { addDecorationData } from '@/api/swisseMemberCenter';
import { PrizeInfo, PRIZE_INFO, PRIZE_TYPE } from '../../utils';
import { deepCopy } from '@/utils';

function MembershipCard({ data, defaultData, dispatch, id, reload }) {
  const [loading, setLoading] = React.useState(false);
  const [bigWhellVisible, setBigWhellVisible] = useState<boolean>(false);
  const [prizeVisible, setPrizeVisible] = useState<boolean>(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  const [editOldData, setEditOldData] = React.useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const selectWheel = (wheelBg): void => {
    setBigWhellVisible(false);
    setData({ wheelBg });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (prizeData): boolean | void => {
    // 更新指定index 奖品信息
    data.prizeList[target] = {
      ...data.prizeList[target],
      ...prizeData,
      sortId: target + 1,
      moduleId: 25,
      pageType: 5,
      modulePrizeType: 3,
      prizeKey: prizeData.planId || prizeData.promoId || prizeData.prizeKey,
      prizeName: prizeData.prizeName || prizeData.planName,
    };
    // 计算总概率
    data.totalProbability = data.prizeList
      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
      .reduce((val, total) => {
        return val + Number(total.probability);
      }, 0);
    if ((+data.totalProbability * 1000) / 1000 > 99.999) {
      Message.error('总概率不能大于等于100%');
      data.prizeList.splice(target, 1, deepCopy(PRIZE_INFO));
      return false;
    }
    // 至少有一个奖品为谢谢参与
    const isHaveThanks = data.prizeList.some((item) => item.prizeName === '谢谢参与');
    if (!isHaveThanks) {
      Message.error('至少有一个奖品为谢谢参与');
      data.prizeList.splice(target, 1, deepCopy(PRIZE_INFO));
      return;
    }
    setData(data);
    setPrizeVisible(false);
  };
  const saveSetting = (): any => {
    const isHavePrize = data.prizeList.every((item) => item.prizeName === '谢谢参与');
    if (isHavePrize) {
      Message.error('请设置奖品');
      return;
    }
    setLoading(true);
    addDecorationData({
      decorationData: JSON.stringify(data),
      modulePrizeList: data.prizeList,
      moduleId: 25,
      pageType: 5,
    })
      .then((res) => {
        Message.success('大转盘模块保存成功');
        dispatch({ type: 'RESET_PUSH' });
        setLoading(false);
        reload();
      })
      .catch((e) => {
        Message.error(e.message);
        setLoading(false);
      });
  };

  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };

  const commonProps = {
    title: '大转盘',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting}>
          保存模块设置
        </Button>
      </div>
    ),
  };
  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = data.prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...data.prizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 8 - prizeListLength; i++) {
      list.push(deepCopy(PRIZE_INFO));
      list[i].sortId = prizeListLength + i + 1;
    }
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({ prizeList: list.length ? list : data.prizeList });
  }, []);
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <Loading visible={loading} style={{ width: '100%' }}>
            <div className={styles.operation}>
              <div className={styles.MemberContainer}>
                <div className="crm-label">页面背景图</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={150}
                    bgHeight={250}
                    value={data.bg}
                    onChange={(bg) => {
                      const updatedData = { ...data };
                      updatedData.bg = bg;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    <div>图片尺寸：建议宽度为750px，高度为1274px</div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">页面背景色</div>
                <div className={styles.imgUpload}>
                  <LzColorPicker
                    value={data.bgColor}
                    onChange={(bgColor) => {
                      const updatedData = { ...data };
                      updatedData.bgColor = bgColor;
                      setData(updatedData);
                    }}
                  />
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">奖盘背景图</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={150}
                    bgHeight={150}
                    value={data.turnUrl}
                    onChange={(turnUrl) => {
                      const updatedData = { ...data };
                      updatedData.turnUrl = turnUrl;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    <div>图片尺寸：建议宽度为700px，高度为700px</div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">奖盘按钮图</div>
                <div className={styles.imgUpload}>
                  <LzImageSelector
                    bgWidth={150}
                    bgHeight={150}
                    value={data.turnBtn}
                    onChange={(turnBtn) => {
                      const updatedData = { ...data };
                      updatedData.turnBtn = turnBtn;
                      setData(updatedData);
                    }}
                  />
                  <div className={styles.tip}>
                    <div>图片尺寸：建议宽度为190px，高度为225px</div>
                    <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                  </div>
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">奖盘文字颜色</div>
                <div className={styles.imgUpload}>
                  <LzColorPicker
                    value={data.wheelTextColor}
                    onChange={(wheelTextColor) => {
                      const updatedData = { ...data };
                      updatedData.wheelTextColor = wheelTextColor;
                      setData(updatedData);
                    }}
                  />
                </div>
              </div>
              <div className={styles.MemberContainer}>
                <div className="crm-label">奖品列表</div>
                <div>单次中奖总概率:{data.totalProbability.toFixed(3)}%</div>
                <div className={styles.imgUpload}>
                  <Table dataSource={data.prizeList} style={{ marginTop: '15px' }} primaryKey="sortIds">
                    <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
                    <Table.Column title="奖项名称" dataIndex="prizeName" />
                    <Table.Column
                      title="奖项类型"
                      cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                      dataIndex="prizeType"
                    />
                    <Table.Column
                      title="单位数量"
                      cell={(_, index, row) => {
                        if (row.prizeType === 1) {
                          return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                        } else {
                          return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                        }
                      }}
                    />
                    <Table.Column
                      title="发放份数"
                      cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                    />
                    <Table.Column
                      title="单份价值(元)"
                      cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : ''}</div>}
                    />
                    <Table.Column
                      title="中奖概率(%)"
                      cell={(_, index, row) => <div>{row.probability ? row.probability : ''}</div>}
                    />
                    <Table.Column
                      title="每日发放限额"
                      cell={(_, index, row) => <div>{row.dayLimitType === 2 ? row.dayLimit : '不限'}</div>}
                    />
                    <Table.Column
                      title="奖品图"
                      cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                    />
                    <Table.Column
                      title="操作"
                      width={100}
                      cell={(val, index, _) => (
                        <div>
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              let row = data.prizeList[index];
                              if (row.prizeName === '谢谢参与') {
                                row = null;
                              }
                              setEditValue(row);
                              setEditOldData(defaultData.prizeList[index]);
                              setTarget(index);
                              setPrizeVisible(true);
                            }}
                          >
                            <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                          </Button>
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              if (_.prizeType) {
                                Dialog.confirm({
                                  v2: true,
                                  title: '提示',
                                  centered: true,
                                  content: '确认清空该奖品？',
                                  onOk: () => {
                                    data.prizeList[index] = deepCopy(PRIZE_INFO);
                                    data.prizeList[index].sortId = index + 1;
                                    console.log(data.prizeList);

                                    data.totalProbability = data.prizeList
                                      .filter((e: Pick<PrizeInfo, 'prizeName'>): boolean => e.prizeName !== '谢谢参与')
                                      .reduce((v, total) => {
                                        return v + Number(total.probability);
                                      }, 0);
                                    setData(data);
                                  },
                                  onCancel: () => console.log('cancel'),
                                } as any);
                              }
                            }}
                          >
                            <i className={`iconfont icon-shanchu`} />
                          </Button>
                        </div>
                      )}
                    />
                  </Table>
                </div>
              </div>
            </div>
            <LzDialog
              title="奖盘样式"
              className="lz-dialog-larger"
              visible={bigWhellVisible}
              footer={false}
              onCancel={() => setBigWhellVisible(false)}
              onClose={() => setBigWhellVisible(false)}
            >
              <LzWheelAssets handleSelect={selectWheel} />
            </LzDialog>
            <LzDialog
              title={false}
              visible={prizeVisible}
              footer={false}
              onClose={() => setPrizeVisible(false)}
              style={{ width: '670px' }}
            >
              <ChoosePrize
                choosePrizeList={[]}
                isEdit={!!editValue}
                editValue={editValue}
                editDefaultValue={editOldData}
                hasLimit={false}
                onChange={onPrizeChange}
                initTarget={2}
                typeList={[1, 2, 3, 7, 8]}
                onCancel={() => {
                  setPrizeVisible(false);
                }}
              />
            </LzDialog>
          </Loading>
        </Card.Content>
      </Card>
    </div>
  );
}

export default MembershipCard;
