.preview {
  position: relative;
  .bg {
    display: block;
    width: 100%;
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .container {
    padding: 15px;
    background: #F4F6F9;
    border-radius: 5px;
  }


  .MemberContainer {
    margin-bottom: 20px;
    @extend .container;
    .imgUpload {
      display: flex;
      align-items: center;
      gap: 10px;

      .tip {
        margin-top: 10px;
      }

      .removeHotZone {
        margin-top: 4px;
        i {
          margin-left: 5px;
          font-size: 12px;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }
        }
      }
    }
  }
  .EquityContainer {
    @extend .container;

    .imgUpload {
      gap: 10px;
      display: flex;
      align-items: center;
    }
  }
  .ProcessContainer {
    @extend .container;

    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
}
