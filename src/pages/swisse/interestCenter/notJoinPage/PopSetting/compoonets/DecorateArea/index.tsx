import React from 'react';
import styles from './index.module.scss';

const DecorateComponent = ({ state, dispatch, moduleName }) => {
  const Components = state.previews[moduleName].decorateComponent;
  return <Components data={state.modules[moduleName]} id={state.id} dispatch={dispatch} />;
};

function DecorateComponentArea({ selectedModule, state, dispatch }) {

  return (
    <div className={styles.decorateComponent}>
      <DecorateComponent state={state} dispatch={dispatch} moduleName={selectedModule} />
    </div>
  );
}

export default DecorateComponentArea;
