import React from 'react';
// import { Tab } from '@alifd/next';
import PageSetting from '@/pages/swisse/interestCenter/notJoinPage/PageSetting';
// import PopSetting from '@/pages/swisse/interestCenter/notJoinPage/PopSetting';
export default () => {
  return (
    <div>
      <PageSetting />
      {/*<Tab>*/}
      {/*  <Tab.Item key="1" title="页面配置">*/}
      {/*    <PageSetting />*/}
      {/*  </Tab.Item>*/}
      {/*  <Tab.Item key="2" title="弹窗配置">*/}
      {/*    <PopSetting />*/}
      {/*  </Tab.Item>*/}
      {/*</Tab>*/}
    </div>
  );
};
