$px-scale: 20;
.preview {
  width: 100%;
  position: relative;
  border-radius: 8.5px;
  overflow: hidden;
  .member-content {
    position: relative;
    width: 100%;
    height: 34.5px * $px-scale;
    background-repeat: no-repeat;
    background-position: center top;
    background-size: 100% auto;
    box-sizing: border-box;
    padding-top: 4.65px * $px-scale;
  }
  .time-count {
    width: 100%;
    height: 3.25px * $px-scale;
    margin-bottom: 0.625px * $px-scale;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    font-size: 0.875px * $px-scale;
    .time-item {
      width: 1px * $px-scale;
      height: 2.1px * $px-scale;
      background-image: url(https://img10.360buyimg.com/imgzone/jfs/t1/235507/25/22509/2572/66a32181Fc1a08bc9/36234b6ab367344f.png);
      background-size: 100%;
      font-size: 1.25px * $px-scale;
      color: #d72b29;
      font-weight: 400;
      text-align: center;
      line-height: 2.1px * $px-scale;
      margin-right: 0.125px * $px-scale;
    }
  }
  .swiper-box {
    margin-top: 2px * $px-scale;
    width: 100%;
    height: 12.5px * $px-scale;
    margin-bottom: 2px * $px-scale;
    display: flex;
    justify-content: space-around;
    align-items: center;

    img {
      width: 7.8px*$px-scale;
    }
  }
  .link-img {
    width: 100%;
  }
}
.operation {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;

  .kvContainer {
    padding: 15px;
    background: #f4f6f9;
    border-radius: 5px;
    .imgUpload {
      display: flex;
      align-items: center;
      .tip {
        margin-left: 10px;
      }
    }
    .colorPicker {
      display: flex;
      margin-top: 10px;
      gap: 20px;

      span {
        font-weight: bold;
        margin-right: 10px;
      }
    }
  }
}
