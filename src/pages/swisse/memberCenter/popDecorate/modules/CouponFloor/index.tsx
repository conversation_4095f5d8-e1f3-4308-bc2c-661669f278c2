import { memberSave<PERSON>age<PERSON>son, memberSavePageJsonDraft } from '@/api/swissePop';
import LzImageSelector from '@/components/LzImageSelector';
import { Button, Card, Divider, Message, Radio, Dialog, Form, Input } from "@alifd/next";
import React from 'react';
import styles from './index.module.scss';
import LzDialog from '@/components/LzDialog';
import Plan from '../../compoonets/Plan';
import { checkUrl } from "@/pages/mengniu/utils";
import { log } from "video.js";
import { deepCopy } from "@/utils";
import { getShop } from "@/utils/shopUtil";

const RadioGroup = Radio.Group;

function CouponFloor({ data, dispatch, allJson, defaultData, levelInfo, defaultCouponList }) {
  const pageinfo = defaultData.filter((item) => item.pageType == data.pageType);
  const [visible, setVisible] = React.useState(false);
  const [editValue, setEditValue] = React.useState();
  const [getLevel, setGetLevel] = React.useState('new');
  const [selectCouponList, setSelectCouponList] = React.useState(
    data.couponAct.couponList.filter((item) => item.getLevel == 'new'),
  );
  const setData = (value) => {
    setSelectCouponList(value.couponAct.couponList.filter((item) => item.getLevel == getLevel));
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  const [pageLoading, setPageLoading] = React.useState(false);
  React.useEffect(() => {}, []);

  const addItem = () => {
    const obj = {
      activityId: `2407${getShop().venderId}88`,
      couponUrl: '',
      couponId: '',
      getLevel,
      giftId: '',
      id: '',
      imgUrl: '',
      showLevel: getLevel == 'new' ? '[1]' : '[2]',
      sort: 1,
      type: '1',
      venderId: getShop().venderId,
    };
    data.couponAct.couponList.push(obj);
    defaultCouponList.couponList.push(JSON.parse(JSON.stringify(obj)));
    setData(data);
  };
  const validateNavUrl = (rule, value, callback) => {
    if (!value) {
      callback();
    } else {
      try {
        if (checkUrl(value)) {
          callback(`链接必须以 jd.com 或 isvjcloud.com 结尾`);
        } else {
          callback();
        }
      } catch (error) {
        callback(`请输入有效的 URL`);
      }
    }
  };

  const isValidCoupon = () => {
    let err = false;
    let numbe1 = 0;
    let numbe2 = 0;
    let numbe3 = 0;
    let numbe4 = 0;
    let numbe5 = 0;

    data.couponAct.couponList.forEach((e) => {
      console.log(e);
      if (!e.imgUrl) {
        console.log(1);
        err = true;
      }
      if (e.type == '2' && !e.couponUrl) {
        console.log(2);
        err = true;
      }
      if (e.type == '1' && !e.couponId) {
        console.log(3);
        err = true;
      }
      if (!JSON.parse(e.showLevel).length) {
        console.log(4);
        err = true;
      }
      if (JSON.parse(e.showLevel).includes(1)) numbe1 += 1;
      if (JSON.parse(e.showLevel).includes(2)) numbe2 += 1;
      if (JSON.parse(e.showLevel).includes(3)) numbe3 += 1;
      if (JSON.parse(e.showLevel).includes(4)) numbe4 += 1;
      if (JSON.parse(e.showLevel).includes(5)) numbe5 += 1;
    });
    const level1 = levelInfo.filter((ite) => ite.level == 1);
    const level2 = levelInfo.filter((ite) => ite.level == 2);
    const level3 = levelInfo.filter((ite) => ite.level == 3);
    const level4 = levelInfo.filter((ite) => ite.level == 4);
    const level5 = levelInfo.filter((ite) => ite.level == 5);
    if (numbe1 == 0) Message.error(`会员：${level1[0].levelName} 未配置优惠券`);
    if (numbe2 == 0) Message.error(`会员：${level2[0].levelName} 未配置优惠券`);
    if (numbe3 == 0) Message.error(`会员：${level3[0].levelName} 未配置优惠券`);
    if (numbe4 == 0) Message.error(`会员：${level4[0].levelName} 未配置优惠券`);
    if (numbe5 == 0) Message.error(`会员：${level5[0].levelName} 未配置优惠券`);
    if (err) {
      return false;
    }
    return true;
  };
  const publish = (): any => {
    if (!data.content.background.imgUrl) {
      Message.error('请上传图片');
      return false;
    }
    if (!data.couponAct.couponList.length) {
      Message.error('请配置优惠券');
      return false;
    }
    if (!isValidCoupon()) {
      Message.error('请正确配置优惠券');
      return false;
    }
    const update = deepCopy(data);
    delete update.couponAct;
    const updateJson = allJson.filter((item) => item.pageType != data.pageType);
    updateJson.push(update);
    memberSavePageJson({ pageInfo: JSON.stringify(updateJson), couponList: data.couponAct.couponList })
      .then((res) => {
        Message.success('发布成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const saveSetting = (): any => {
    if (!data.content.background.imgUrl) {
      Message.error('请上传图片');
      return false;
    }
    const updateJson = allJson.filter((item) => item.pageType != data.pageType);
    updateJson.push(data);
    console.log('updateJson', updateJson);
    setPageLoading(true);
    memberSavePageJsonDraft({ pageInfo: JSON.stringify(updateJson) })
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };

  const commonProps = {
    title: data.pageName,
    extra: (
      <div>
        <RadioGroup
          value={data.showType}
          onChange={(showType) => {
            data.showType = showType;
            dispatch({ type: 'UPDATE_MODULE', payload: data.showType });
          }}
        >
          <Radio id="0" value={0}>
            全显示
          </Radio>
          <Radio id="1" value={1}>
            限新客
          </Radio>
          <Radio id="2" value={2}>
            限老客
          </Radio>
          <Radio id="3" value={3}>
            不显示
          </Radio>
        </RadioGroup>
        <Button type="primary" onClick={publish} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">背景图</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={750}
                  height={456}
                  value={data.content.background.imgUrl}
                  onChange={(img) => {
                    data.content.background.imgUrl = img;
                    setData(data);
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：仅支持宽度为750px，高度为456px</div>
                  <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                </div>
              </div>
              <div className={styles.colorPicker}>
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    data.content.background.imgUrl = pageinfo[0].content.background.imgUrl;
                    setData(data);
                  }}
                >
                  重置
                </Button>
              </div>
            </div>
          </div>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">优惠券</div>
              <div className={styles.colorPicker}>
                <div style={{ marginBottom: '10px' }}>
                  <Button
                    type={getLevel == 'new' ? 'primary' : 'secondary'}
                    style={{ marginRight: '10px' }}
                    onClick={() => {
                      setGetLevel('new');
                      setSelectCouponList(data.couponAct.couponList.filter((item) => item.getLevel == 'new'));
                    }}
                  >
                    会员新客
                  </Button>
                  <Button
                    type={getLevel == 'new' ? 'secondary' : 'primary'}
                    onClick={() => {
                      setGetLevel('old');
                      setSelectCouponList(data.couponAct.couponList.filter((item) => item.getLevel == 'old'));
                    }}
                  >
                    会员老客
                  </Button>
                </div>
              </div>
              <div className={styles.colorPicker}>
                <div style={{ marginBottom: '10px' }}>
                  <Button type={'primary'} onClick={() => addItem()} disabled={selectCouponList.length >= 10}>
                    添加（{selectCouponList.length} / 10）
                  </Button>
                  <span className={'tip'} style={{ marginLeft: 5 }}>
                    注：最多可添加10张优惠券
                  </span>
                </div>
              </div>
              <div>
                {data.couponAct.couponList.map((item, index) => {
                  return (
                    <div key={index} style={{ borderBottom: `1px solid #c7c6c7` }} hidden={item.getLevel != getLevel}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
                        <span className={'tip'} style={{ marginRight: '5px' }}>
                          类型选择
                        </span>
                        <RadioGroup
                          value={item.type}
                          disabled={item.id != ''}
                          onChange={(type) => {
                            item.type = type;
                            dispatch({ type: 'UPDATE_MODULE', payload: data });
                          }}
                        >
                          <Radio id="1" value={'1'}>
                            直接领取
                          </Radio>
                          <Radio id="2" value={'2'}>
                            积分兑换
                          </Radio>
                        </RadioGroup>
                      </div>
                      <div className={styles.imgUpload} style={{ marginBottom: '10px' }}>
                        <span className={'tip'} style={{ marginRight: '5px' }}>
                          优惠券图片
                        </span>
                        <div className={styles.imgUpload}>
                          <LzImageSelector
                            width={215}
                            height={249}
                            value={item.imgUrl}
                            onChange={(imgSrc) => {
                              item.imgUrl = imgSrc;
                              setData(data);
                            }}
                          />
                          <div style={{ marginLeft: '5px' }}>
                            <div className={styles.tip}>
                              <div>图片尺寸：仅支持宽度为215px，高度为249px</div>
                              <div>图片格式：大小不超过1M图片类型为jpg、png</div>
                            </div>
                            {item.type === '1' && (
                              <Button
                                type={'primary'}
                                disabled={item.id != ''}
                                onClick={() => {
                                  setEditValue(item);
                                  setVisible(true);
                                }}
                              >
                                选择优惠券
                              </Button>
                            )}{' '}
                            {item.type === '2' && (
                              <Form.Item
                                name="adLink"
                                label="跳转链接"
                                validator={(rule, value, callback) => validateNavUrl(rule, value, callback)}
                              >
                                <Input
                                  value={item.couponUrl}
                                  trim
                                  placeholder="请输入跳转链接"
                                  onChange={(value) => {
                                    item.couponUrl = value;
                                    setData(data);
                                  }}
                                />
                              </Form.Item>
                            )}
                            <div className={styles.buttonDiv}>
                              <Button
                                style={{ marginLeft: '10px', marginRight: '10px' }}
                                type={'primary'}
                                text
                                onClick={() => {
                                  const resetData = JSON.parse(JSON.stringify(defaultCouponList.couponList[index]));
                                  item.imgUrl = resetData.imgUrl;
                                  item.showLevel = resetData.showLevel;
                                  item.getLevel = resetData.getLevel;
                                  item.type = resetData.type;
                                  item.couponUrl = resetData.couponUrl;
                                  item.couponId = resetData.couponId;
                                  setData(data);
                                }}
                              >
                                重置
                              </Button>
                              {/* 删除 */}
                              <Button type={'primary'} text>
                                <i
                                  className="iconfont icon-icon-07 btn-del"
                                  onClick={() => {
                                    data.couponAct.couponList.splice(index, 1);
                                    defaultCouponList.couponList.splice(index, 1);
                                    setData(data);
                                  }}
                                />
                              </Button>
                            </div>
                          </div>

                        </div>
                      </div>
                      <div style={{ display: 'flex', alignItems: 'center' }}>
                        <span className={'tip'} style={{ marginRight: '5px', marginBottom: '16px' }}>
                          可领取等级设置
                        </span>
                        <Form.Item name="showlevel" label="" style={{ marginRight: '5px' }}>
                          {levelInfo.map((option) => (
                            <label key={option.level} style={{ marginRight: '5px' }}>
                              <input
                                type="checkbox"
                                value={option.level}
                                disabled={
                                  (getLevel == 'new' && option.level != 1) || (getLevel == 'old' && option.level == 1)
                                }
                                checked={JSON.parse(item.showLevel).includes(option.level)}
                                onChange={(e) => {
                                  const { value } = e.target;
                                  const select = Number(value);
                                  console.log('123');
                                  if (JSON.parse(item.showLevel).includes(select)) {
                                    const showLevel = JSON.parse(item.showLevel).filter((va) => va !== select);
                                    item.showLevel = JSON.stringify(showLevel);
                                    setData(data);
                                  } else {
                                    const showLevel = [...JSON.parse(item.showLevel), select];
                                    item.showLevel = JSON.stringify(showLevel);
                                    setData(data);
                                  }
                                }}
                              />
                              {option.levelName}
                            </label>
                          ))}
                        </Form.Item>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          <Dialog
            title="选择优惠券"
            footer={false}
            shouldUpdatePosition
            visible={visible}
            onClose={() => setVisible(false)}
          >
            <Plan
              onSubmit={(val) => {
                console.log(val);
                editValue.couponId = val.planId;
                setData(data);
                setVisible(false);
              }}
            />
          </Dialog>
        </Card.Content>
      </Card>
    </div>
  );
}

export default CouponFloor;
