import React from 'react';
import styles from './index.module.scss';

export default ({ data, allJson, levelInfo }) => {
  const currentLevel = 2;
  const [isMan, setIsMan] = React.useState(false);
  const arr = levelInfo.filter((item) => item.level === currentLevel);
  const arr1 = levelInfo.filter((item) => item.level === currentLevel + 1);
  // setMyCard(arr[0].myCardImageUrl)
  // setMmeberRule(arr[0].memberRuleImageUrl)
  // setContentBgColor(arr[0].linearGradient)
  // setMemberApng(arr[0].bannerImageUrl)
  // setNickNameColor(arr[0].nickNameColor)
  // setMembe2rApng(arr[0].banner2ImageUrl)
  // setGenderBox(arr[0].genderBox)
  // setChooseBg(arr[0].chooseBg)
  return (
    <div className={styles.preview}>
      <div className={styles['member-info']} style={{ backgroundImage: `url(${data.content.background.imgUrl})` }}>
        <div className={styles['top-box']}>
          <div>
            <img className={styles['my-nick']} src={data.content.myNick.imgUrl} />
            <div className={styles.name} style={{ color: `${arr[0].nickNameColor}` }}>
              用户昵称
            </div>
          </div>
          <div>
            <img className={styles['my-nick']} src={data.content.myLevel.imgUrl} />
            <div className={styles.name} style={{ color: `${arr[0].nickNameColor}` }}>
              {arr[0].levelName}
            </div>
          </div>
        </div>
        <img
          src={data.content.memberRule.imgUrl}
          className={styles['member-rule']}
        />
      </div>

      <div className={styles.content} style={{ backgroundImage: `url(${data.content.contentBg.imgUrl})` }}>
        {/* 基本信息 */}
        <div className={styles['base-info']}>
          {/* 我的积分 */}
          <div style={{ height: '100%' }}>
            <img
              className={styles['my-point']}
              src={
                'https://img10.360buyimg.com/imgzone/jfs/t1/238661/25/15766/3977/67064706F25f3ce95/1545cee066666fef.png'
              }
            />
            <div>100</div>
          </div>
          {/* 我的卡包 */}
          <div style={{ height: '100%' }}>
            <img
              className={styles['my-card']}
              src={
                'https://img10.360buyimg.com/imgzone/jfs/t1/193683/20/47919/3989/67064706F29af897d/ae74fe453db3102d.png'
              }
            />
          </div>
        </div>
        <div className={styles['progress-box-div']}>
          <div className={styles['progress-info']}>
            {currentLevel < 5 ? <div>再消费150元可升级为{arr1[0].levelName}</div> : <div>您已升至最高等级</div>}
          </div>
          {/* 进度条背景 */}
          <div className={styles['member-level-progress-bg']}>
            <div className={styles['member-level-line']} />
            <div className={styles['member-level-line2-box']}>
              <div className={styles['member-level-line1']} />
              <div
                className={styles['member-level-line2']}
                style={{
                  backgroundImage: `linear-gradient(to right, ${arr[0].linearGradient})`,
                  width: `${(0.2 * 100).toFixed(2)}%`,
                }}
              />
              <div className={styles['member-level-line3']} />
              <div className={styles['member-level-line1']} />
            </div>
            <div className={styles['member-level-line2-box']} style={{ display: 'flex' }}>
              {levelInfo.map((levelItem, levelIndex) => (
                <div key={levelIndex} className={styles['member-level-info']}>
                  <div hidden={currentLevel <= levelItem.level} className={styles['member-level-icon']} />
                  <div hidden={currentLevel >= levelItem.level} className={styles['member-level-icon-max']} />
                  <div hidden={currentLevel != levelItem.level} className={styles['member-level-icon-active']} />
                  <div hidden={currentLevel == levelItem.level} className={styles['member-level-name']}>
                    {levelItem.levelName}
                  </div>
                  <div hidden={currentLevel != levelItem.level} className={styles['member-level-name-active']}>
                    {levelItem.levelName}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* 权益 */}
        <div className={styles.rights}>
          {data.iconInfo.map((item, index) => (
            <div
              key={index}
              hidden={!item.showLevel.includes(currentLevel)}
              style={{ backgroundImage: `url(${item.imgSrc})` }}
            />
          ))}
          {data.iconInfo.map((item, index) => (
            <div
              key={index}
              hidden={item.showLevel.includes(currentLevel)}
              style={{ backgroundImage: `url(${item.clockImg})` }}
            />
          ))}
          {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((div, index) => (
            <div key={index} hidden={div <= data.iconInfo.length} />
          ))}
        </div>
      </div>
    </div>
  );
};
