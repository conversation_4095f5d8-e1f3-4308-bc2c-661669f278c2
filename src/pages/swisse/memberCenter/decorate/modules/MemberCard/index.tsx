import { popJsonSaveZphy<PERSON>son, popJsonSaveZphyMemberLevel } from '@/api/zphy';
import LzImageSelector from '@/components/LzImageSelector';
import LzTipPanel from '@/components/LzTipPanel';
import { checkUrl } from '@/pages/mengniu/utils';
import {
  Box,
  Button,
  Card,
  Checkbox,
  Divider,
  Field,
  Form,
  Input,
  Message,
  NumberPicker,
  Switch,
  Table
} from "@alifd/next";
import React, { useState } from "react";
import styles from './index.module.scss';
import { savePageJson } from "@/api/swisse";

const formItemLayout = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
  labelAlign: 'left',
  colon: true,
};

const validateNavUrl = (rule, value, callback) => {
  if (!value) {
    callback();
  } else {
    try {
      if (checkUrl(value)) {
        callback(`链接必须以 jd.com 或 isvjcloud.com 结尾`);
      } else {
        callback();
      }
    } catch (error) {
      callback(`请输入有效的 URL`);
    }
  }
};

function MemberCard({ data, dispatch, levelInfo, defaultIconInfo }) {
  // console.log('MemberCard', data);
  const [pageLoading, setPageLoading] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MEMBER_CARD', payload: value });
  };

  React.useEffect(() => {}, []);
  const field = Field.useField();

  const addItem = () => {
    const obj = {
      imgSrc: '',
      clockImg: '',
      name: '',
      url: '',
      showLevel: [],
    };
    data.iconInfo.push(obj);
    defaultIconInfo.push(JSON.parse(JSON.stringify(obj)));
    setData(data.iconInfo);
  };

  const isValidSwiper = () => {
    let err = false;

    data.iconInfo.forEach((e) => {
      if (!e.imgSrc) {
        err = true;
      }
      if (!e.url) {
        err = true;
      }
      if (!e.clockImg) {
        err = true;
      }
      if (e.showLevel.length == 0) {
        err = true;
      }
      const patt = /^ [\s]*$/;
      if (patt.test(e.name)) {
        err = true;
      }
    });
    if (err) {
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!data.iconInfo.length) {
      Message.error('请至少添加一张图片');
      return false;
    }
    if (!isValidSwiper()) {
      Message.error('请完善icon配置');
      return false;
    }
    setPageLoading(true);
    savePageJson({ iconInfo: JSON.stringify(data.iconInfo) })
      .then((res) => {
        Message.success('保存成功');
        setPageLoading(false);
        dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(false);
      });
  };
  const commonProps = {
    title: '会员卡面',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <div className={styles.kvContainer}>
              <div className="crm-label">icon配置</div>
              <div style={{ marginBottom: '10px' }}>
                <div>图片尺寸：为120*153px,支持jpg、jpeg、png格式，大小不超过1M</div>
              </div>
              <div className={styles.imgContainer}>
                {data.iconInfo.map((item, index) => {
                  return (
                    <div key={index}>
                      <div className={styles.imgUpload}>
                        <div className={styles.divitem}>
                          <LzImageSelector
                            width={120}
                            height={153}
                            value={item.imgSrc}
                            onChange={(imgSrc) => {
                              item.imgSrc = imgSrc;
                              setData(data.iconInfo);
                            }}
                          />
                        </div>
                        <div className={styles.divitem}>
                          <LzImageSelector
                            width={120}
                            height={153}
                            value={item.clockImg}
                            onChange={(imgSrc) => {
                              item.clockImg = imgSrc;
                              setData(data.iconInfo);
                            }}
                          />
                        </div>
                        <div className={styles.divitem}>
                          <Form style={{ width: '100px', marginTop: '10px' }} field={field}>
                            <Form.Item required name={`name${index}`} requiredMessage={'请输入名字'}>
                              <Input
                                style={{ width: '100px' }}
                                name={`name${index}`}
                                value={item.name}
                                placeholder={'请输入名字'}
                                maxLength={7}
                                onFocus={() => {}}
                                onChange={(val) => {
                                  item.name = val;
                                  setData(data.iconInfo);
                                }}
                              />
                            </Form.Item>
                          </Form>
                          <Form.Item name="showlevel" label="可解锁的等级设置：（可多选）">
                            {levelInfo.map((option) => (
                              <label key={option.level}>
                                <input
                                  type="checkbox"
                                  value={option.level}
                                  checked={item.showLevel.includes(option.level)}
                                  onChange={(e) => {
                                    const { value } = e.target;
                                    const select = Number(value);
                                    console.log('123');
                                    if (item.showLevel.includes(select)) {
                                      item.showLevel = item.showLevel.filter((va) => va !== select);
                                      setData(data.iconInfo);
                                    } else {
                                      item.showLevel = [...item.showLevel, select];
                                      setData(data.iconInfo);
                                    }
                                  }}
                                />
                                {option.levelName}
                              </label>
                            ))}
                          </Form.Item>
                          <Form.Item
                            name="adLink"
                            label="跳转链接"
                            validator={(rule, value, callback) => validateNavUrl(rule, value, callback)}
                          >
                            <Input
                              value={item.url}
                              trim
                              placeholder="请输入跳转链接"
                              onChange={(value) => {
                                item.url = value;
                                setData(data.iconInfo);
                              }}
                            />
                          </Form.Item>
                        </div>
                      </div>
                      <div className={styles.buttonDiv}>
                        <Button
                          style={{ marginLeft: '10px', marginRight: '10px' }}
                          type={'primary'}
                          text
                          onClick={() => {
                            const resetData = JSON.parse(JSON.stringify(defaultIconInfo[index]));
                            item.imgSrc = resetData.imgSrc;
                            item.name = resetData.name;
                            item.showLevel = resetData.showLevel;
                            item.url = resetData.url;
                            setData(data.iconInfo);
                          }}
                        >
                          重置
                        </Button>
                        {/* 删除 */}
                        <Button type={'primary'} disabled={data.iconInfo.length <= 1} text>
                          <i
                            className="iconfont icon-icon-07 btn-del"
                            onClick={() => {
                              data.iconInfo.splice(index, 1);
                              defaultIconInfo.splice(index, 1);
                              setData(data.iconInfo);
                            }}
                          />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
              <div style={{ marginTop: '10px' }}>
                <Button type={'primary'} onClick={() => addItem()} disabled={data.iconInfo.length >= 10}>
                  添加（{data.iconInfo.length} / 10）
                </Button>
                <span className={'tip'} style={{ marginLeft: 5 }}>
                  注：最多可添加10个icon
                </span>
              </div>
            </div>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}

export default MemberCard;
