import React, { useState } from 'react';
import { <PERSON>oon, Button, Card } from '@alifd/next';
import styles from './style.module.scss';
import CardTags from '../CardTags';
import { withRouter } from 'react-router-dom';
import LzEmpty from '@/components/LzEmpty';
import Introduce from '../Introduce';
import { appHistory } from '@ice/stark-app';
import Badge from './components/badge';
import { deepCopy } from '@/utils';
import { initData } from '@/models/customizedPager';

const { Tooltip } = Balloon;

const ResponsiveList = (props) => {
  const { isLoading, list, history } = props;
  const [showImgInfo, setShowImgInfo] = useState(false);
  const [imgInfo, setImgInfo] = useState({
    cover: '',
    title: '',
  });
  // 活动管理页面
  const handleActivityManage = (act: any): void => {
    if (act.managePage && act.managePage.indexOf('http') === 0) {
      window.open(act.managePage, act.managePage);
      return;
    }
    if (act.id === 10058) {
      appHistory.push('/interact2/giftBag?a=132&activityType=10058');
    } else {
      const params = deepCopy(initData);
      params.activityType = [act.id] as any;
      history.push(`/activity?params=${encodeURIComponent(JSON.stringify(params))}`);
    }
  };
  // 活动创建页面
  const handleActivityCreate = (act: any): void => {
    if (act.createPage && act.createPage.indexOf('http') === 0) {
      window.open(act.createPage, act.createPage);
      return;
    }
    switch (act.version) {
      case 1:
        act.createPage && appHistory.push(act.createPage);
        break;
      case 2:
        appHistory.push(`/interact2/activity/select?activityType=${act.id}`);
        break;
      case 3:
        history.push(`/select?activityType=${act.id}`);
        break;
      default:
        break;
    }
    // if (act.version === 1) {
    //   act.createPage && appHistory.push(act.createPage);
    // } else  {
    //   history.push(`/select?activityType=${act.id}`);
    // }
  };
  const closeImgInfo = () => {
    setShowImgInfo(!showImgInfo);
    setImgInfo({ cover: '', title: '' });
  };
  return (
    <div className={styles.SquarePage}>
      <div className={['lz-flex-box', styles.ActivityTypeRows].join(' ')}>
        {list &&
          list.map((item) => (
            <Card key={item.id} free hasBorder className={styles.ActivityTypeCard}>
              {item.badge && <Badge badge={item.badge} />}
              <Card.Media>
                <img
                  loading={'lazy'}
                  src={item.cover}
                  className={styles.ActivityTypeCardCover}
                  alt={item.title}
                  onClick={() => {
                    setShowImgInfo(!showImgInfo);
                    setImgInfo({ cover: item.cover, title: item.title });
                  }}
                />
              </Card.Media>
              <Card.Header
                title={
                  <div className={styles.ActivityTypeCardTitle}>
                    <div className={styles.ActivityTypeCardTitleText}>{item.title}</div>
                    <Introduce activityType={item.id} />
                  </div>
                }
              />
              <Card.Content>
                <Tooltip
                  v2
                  trigger={<div className={styles.ActivityTypeCardIntro}>{item.intro}</div>}
                  align="b"
                  arrowPointToCenter
                >
                  {item.intro}
                </Tooltip>

                <div className={styles.ActivityTypeCardTags}>
                  <CardTags tags={item.tags?.split(',')} />
                </div>
              </Card.Content>
              <Card.Actions style={{ whiteSpace: 'nowrap', display: 'flex', justifyContent: 'space-around' }}>
                <Button
                  type="primary"
                  key="action1"
                  disabled={!item.enable}
                  title={!item.enable ? '活动已禁用,请联系管理' : '点击创建活动'}
                  onClick={() => handleActivityCreate(item)}
                  style={{ width: '50%' }}
                >
                  创建活动
                </Button>
                <Button type="normal" key="action2" onClick={() => handleActivityManage(item)} style={{ width: '50%' }}>
                  管理活动
                </Button>
              </Card.Actions>
            </Card>
          ))}
      </div>
      {(!list || (list.length === 0 && !isLoading)) && <LzEmpty />}
      {showImgInfo && (
        <div className={styles.ImgInfo} onClick={closeImgInfo}>
          <img loading={'lazy'} src={imgInfo.cover} alt={imgInfo.title} className={styles.ShowImgInfo} />
          <div className={`iconfont icon-icon4-29 ${styles.closeBtn}`} onClick={closeImgInfo} />
        </div>
      )}
    </div>
  );
};

export default withRouter(ResponsiveList);
