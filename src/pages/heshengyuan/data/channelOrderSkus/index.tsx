import React, { useState, useEffect } from 'react';
import { Field, Form, Table, Input, Grid, Button, Dialog, DatePicker2, Select } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import {
  purchaseDataGetChannelOrderStat,
  purchaseDataGetChannelOrderStatExport,
  purchaseDataGetChannelOrderSkus,
} from '@/api/biostime';
import { BiostimePersonnelGetInfoVO } from '@/api/types';
import LzPanel from '@/components/LzPanel';
import LzPagination from '@/components/LzPagination';
import dayjs from 'dayjs';
import { downloadExcel } from '../../../../utils';

const FormItem = Form.Item;
interface Pager {
  pageNum: number;
  pageSize: number;
  total?: number;
}

const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<BiostimePersonnelGetInfoVO[]>([]);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  const [orderStartTime, setOrderStartTime] = useState(dayjs().subtract(30, 'day').format('YYYY-MM-DD'));
  const [orderEndTime, setOrderEndTime] = useState(dayjs().format('YYYY-MM-DD'));
  const [category, setCategory] = useState<string[]>([]);
  const [series, setSeries] = useState<string[]>([]);
  const [segments, setSegments] = useState<string[]>([]);
  const [specification, setSpecification] = useState<string[]>([]);
  const [decorations, setDecorations] = useState<string[]>([]);
  const [FormValue, setFormValue] = useState({
    phone: '',
    type: 1,
    category: '',
    series: '',
    segments: '',
    specification: '',
    decorations: '',
  });
  const fieldList = ['category', 'series', 'segments', 'specification', 'decorations'];

  // 在页面加载完成后查询数据列表
  const getCateGory = async (
    type: number = 1,
    category?: string,
    series?: string,
    segments?: string,
    specification?: string,
  ) => {
    const formValue: any = field.getValues();
    setFormValue({ ...formValue });
    field.reset(fieldList.slice(type - 1));
    const result = await purchaseDataGetChannelOrderSkus({
      type, //1-品类 2-系列 3-段数 4-规格 5-包装
      category,
      series,
      segments,
      specification,
    });
    console.log('🚀 ~ result:', result);
    switch (type) {
      case 1:
        setCategory(result || []);
        break;
      case 2:
        setSeries(result || []);
        break;
      case 3:
        setSegments(result || []);
        break;
      case 4:
        setSpecification(result || []);
        break;
      case 5:
        setDecorations(result || []);
      default:
        break;
    }
  };
  useEffect(() => {
    getCateGory(1);
  }, []);

  const handleSubmit = (v, e) => {
    const formValue: any = field.getValues();

    if (!e) {
      getChannelTurn(formValue, { ...pageInfo, pageNum: 1, total: 0 });
    }
  };
  // 获取数据列表
  const getChannelTurn = async (query, page: Pager) => {
    const postData = {
      ...query,
      ...page,
      orderStartTime,
      orderEndTime,
      activityId: '2405100000267230',
    };
    try {
      setLoading(true);
      const result = await purchaseDataGetChannelOrderStat(postData);
      setList(result.list || []);
      setPageInfo({
        total: Number(result.count),
        pageNum: page.pageNum,
        pageSize: page.pageSize,
      });
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const exportData = async (query, page: Pager) => {
    const postData = {
      ...query,
      ...page,
      orderStartTime,
      orderEndTime,
      activityId: '2405100000267230',
    };
    try {
      setLoading(true);
      const result: any = await purchaseDataGetChannelOrderStatExport(postData);
      downloadExcel(result, '顾问成交统计表');
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handlePage = ({ pageSize, pageNum }) => {
    const formValue: any = field.getValues();
    getChannelTurn(formValue, { pageNum, pageSize });
  };
  const handleOrderDateChange = (dates) => {
    setOrderStartTime(dates[0].format('YYYY-MM-DD'));
    setOrderEndTime(dates[1].format('YYYY-MM-DD'));
  };
  // 活动主页
  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" useLabelForErrorMessage field={field} colon labelAlign={'top'}>
          <FormItem name="phone" label="所属顾问手机号">
            <Input placeholder="请输入顾问手机号" />
          </FormItem>
          <FormItem name="type" label="查询方式" required>
            <Select style={{ width: '100%' }} defaultValue={1}>
              <Select.Option value={1}>按天查询</Select.Option>
              <Select.Option value={0}>总计</Select.Option>
            </Select>
          </FormItem>
          <FormItem label="下单时间" required>
            <DatePicker2.RangePicker
              hasClear={false}
              style={{ width: '100%' }}
              onChange={handleOrderDateChange}
              defaultValue={[dayjs(orderStartTime, 'YYYY-MM-DD'), dayjs(orderEndTime, 'YYYY-MM-DD')]}
            />
          </FormItem>
          <FormItem name="category" label="品线">
            <Select
              style={{ width: '100%' }}
              placeholder="请选择品线"
              onChange={(value: string) => getCateGory(2, value)}
            >
              {category.map((item) => (
                <Select.Option value={item} key={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="series" label="系列">
            <Select
              style={{ width: '100%' }}
              placeholder="请选择系列"
              onChange={(value: string) => getCateGory(3, FormValue.category, value)}
            >
              {series.map((item) => (
                <Select.Option value={item} key={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="segments" label="段数">
            <Select
              placeholder="请选择段数"
              onChange={(value: string) => getCateGory(4, FormValue.category, FormValue.series, value)}
            >
              {segments.map((item) => (
                <Select.Option value={item} key={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="specification" label="规格">
            <Select
              placeholder="请选择规格"
              onChange={(value: string) =>
                getCateGory(5, FormValue.category, FormValue.series, FormValue.segments, value)
              }
            >
              {specification.map((item) => (
                <Select.Option value={item} key={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
          <FormItem name="decorations" label="包装">
            <Select style={{ width: '100%' }} placeholder="请选择包装">
              {decorations.map((item) => (
                <Select.Option value={item} key={item}>
                  {item}
                </Select.Option>
              ))}
            </Select>
          </FormItem>
          <FormItem colon={false}>
            <Form.Reset loading={loading} style={{ marginRight: '10px' }}>
              重置
            </Form.Reset>
            <Form.Submit
              validate
              type="primary"
              loading={loading}
              style={{ marginRight: '10px' }}
              onClick={handleSubmit}
            >
              查询
            </Form.Submit>
            <Form.Submit loading={loading} type="primary" onClick={exportData}>
              导出
            </Form.Submit>
          </FormItem>
        </Form>
      </LzPanel>
      <LzPanel>
        <Table dataSource={list} loading={loading} tableLayout="fixed">
          <Table.Column width={300} align="center" title="时间" dataIndex="opDate" />
          <Table.Column width={100} align="center" title="顾问名" dataIndex="name" />
          <Table.Column width={120} align="center" title="顾问手机号" dataIndex="phone" />
          <Table.Column width={100} align="center" title="总订单数" dataIndex="orderCnt" />
          <Table.Column width={100} align="center" title="总下单人数" dataIndex="pinCnt" />
          <Table.Column width={100} align="center" title="新客订单数" dataIndex="newOrderCnt" />
          <Table.Column width={100} align="center" title="下单新客人数" dataIndex="newPinCnt" />
          <Table.Column width={100} align="center" title="品线名称" dataIndex="category" />
          <Table.Column width={100} align="center" title="系列名称" dataIndex="series" />
          <Table.Column width={100} align="center" title="段数名称" dataIndex="segments" />
          <Table.Column width={100} align="center" title="规格名称" dataIndex="specification" />
          <Table.Column width={100} align="center" title="包装名称" dataIndex="decorations" />
        </Table>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={handlePage}
        />
      </LzPanel>
    </div>
  );
};
