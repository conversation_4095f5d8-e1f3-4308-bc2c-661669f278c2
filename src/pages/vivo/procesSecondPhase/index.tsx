import LzPanel from "@/components/LzPanel";
import {Box, Button, Dialog, Select, Table} from "@alifd/next";
import LzPagination, {Pager} from "@/components/LzPagination";
import React, {useEffect, useReducer, useState} from "react";
import styles from './style.module.scss';
import {
  examineAddPublish,
  examineDelete,
  examinePublish,
  examineRankWorksPage,
  examineVerifyRank,
  examineWorksPage
} from "@/api/vivoNewArrival";
import LzMsg from "@/components/LzMsg";

const initPager: Pager = {
  pageNo: 1,
  pageSize: 10,
  total: 0,
};

export default () => {
  // pageType:  1:审核页 2：发布页
  const [pageType, setPageType] = useState(1);
  const [loading, setLoading] = useState(false);
  const [rankList, setRankList] = useState([]);
  const [works, setWorks] = useState([]);
  const [worksPublish, setWorksPublish] = useState([]);
  const [params, setParams] = useReducer((prevState, currState) => ({...prevState, ...currState}), {
    ...initPager,
  });
  const [rankLotteryDraw, setRankLotteryDraw] = useState(false);
  const [showImgDialog, setShowImgDialog] = useState(false);
  const [imgDialogUrl, setImgDialogUrl] = useState('');
  const tableHeader = [
    {
      value: '作品id',
      key: 'id',
    },
    {
      value: '作品标题',
      key: 'workTitle',
    },
    {
      value: '人气值',
      key: 'popularity',
    },
    {
      value: '作者昵称',
      key: 'nickName',
    },
    {
      value: '图片',
      key: 'workImageUrl',
    },
    {
      value: '操作',
      key: 'edit',
    },
  ];
  const ranktableHeader = [
    {
      value: '作品id',
      key: 'id',
    },
    {
      value: '作品标题',
      key: 'workTitle',
    },
    {
      value: '人气值',
      key: 'popularity',
    },
    {
      value: '作者昵称',
      key: 'nickName',
    },
    {
      value: '图片',
      key: 'workImageUrl',
    },
    {
      value: '排名',
      key: 'rankLimit',
    },
    {
      value: '操作',
      key: 'edit',
    },
  ];

  useEffect(() => {
    getRankList();
  }, [worksPublish])

  const getWorksPage = async (query) => {
    try {
      setLoading(true);
      let result = [];
      // @ts-ignore
      result = await examineWorksPage({...query});
      const info = {
        total: result.total,
        pageNo: result.current,
        pageSize: result.size,
      };
      setParams({...params, ...info});
      setWorks(result.records);
      console.log(result);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const getRankWorksPage = async () => {
    try {
      setLoading(true);
      let result = [];
      // @ts-ignore
      result = await examineRankWorksPage();
      setWorksPublish(result);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const publish = async () => {
    try {
      setLoading(true);
      let result = [];
      // @ts-ignore
      result = await examinePublish({rankLotteryDraw});
      console.log(result);
      await getWorksPage(params);
      await getRankWorksPage();
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const getRankList = () => {
    const list = [] as Number[];
    for (let i = 0; i < 50; i += 1) {
      const isFind = worksPublish.findIndex((ix: any) => ix.rankLimit === i + 1) === -1;
      if (isFind) {
        list.push(i + 1)
      }
    }
    setRankList(list)
  };


  const verifyRank = async (id, rankLimit) => {
    try {
      setLoading(true);
      let result = [];
      // @ts-ignore
      result = await examineVerifyRank({id, rankLimit});
      console.log(result);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const deleteWorks = async (id) => {
    try {
      setLoading(true);
      let result = [];
      // @ts-ignore
      result = await examineDelete({id});
      console.log(result);
      await getWorksPage(params);
      await getRankWorksPage();
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const addPublish = async (id) => {
    try {
      setLoading(true);
      let result = [];
      // @ts-ignore
      result = await examineAddPublish({id});
      console.log(result);
      await getWorksPage(params);
      await getRankWorksPage();
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getWorksPage(params).then(r => {
    });
    getRankWorksPage().then(r => {
    });
  }, []);
  return (
    <div>
      {/* eslint-disable-next-line react/jsx-no-undef */}
      <LzPanel>
        <Box direction="row" spacing={20}>
          <Button
            type={pageType === 1 ? 'primary' : 'secondary'}
            onClick={() => {
              setPageType(1);
            }}
          >
            审核页
          </Button>
          <Button
            type={pageType === 1 ? 'secondary' : 'primary'}
            onClick={() => {
              setPageType(2);
              getRankList();
            }}
          >
            发布页
          </Button>
        </Box>
      </LzPanel>
      <div hidden={pageType == 1}>
        <LzPanel>
          {/*<div className={styles.subTitle}>{pageType == 1 ? '审核页' : '发布页'}</div>*/}
          <div className={styles.publishView} style={{display: pageType == 1 ? 'none' : ''}}>
            <Button
              type="primary"
              onClick={() => {
                console.log(rankLotteryDraw);
                publish();
              }}
            >
              点击发布
            </Button>
            <div>
              是否开奖{' '}
              <Select
                cacheValue={false}
                defaultValue={rankLotteryDraw}
                onChange={(val) => setRankLotteryDraw(val)}
                dataSource={[
                  {value: false, label: '否'},
                  {value: true, label: '是'},
                ]}
                style={{width: 100, marginRight: 8}}
              />
            </div>
          </div>
        </LzPanel>
      </div>

      <div hidden={pageType == 2}>
        <LzPanel>
          <Table.StickyLock dataSource={works} loading={loading}>
            {tableHeader.map((head, index) => {
              if (head.key === 'workImageUrl') {
                return (
                  <Table.Column
                    key={index}
                    width={150}
                    title={head.value}
                    cell={(val, ind: number, _) => (
                      <div style={{display: "flex", overflowX: 'auto', maxHeight: '120px'}}>
                        {works[ind].workImageUrl.map((item, index) => (
                          <img
                            style={{height: 'auto', width: '100%', aspectRatio: 'auto', objectFit: 'contain'}}
                            src={item}
                            onClick={() => {
                              setImgDialogUrl(item);
                              setShowImgDialog(true);
                            }}
                          />
                        ))}

                      </div>
                    )}
                  />
                );
              } else if (head.key === 'edit') {
                return (
                  <Table.Column
                    key={index}
                    width={120}
                    align={'center'}
                    title={head.value}
                    cell={(val, ind: number, _) => (
                      <Button
                        type={!works[ind].addRank ? 'primary' : 'secondary'}
                        disabled={works[ind].addRank}
                        onClick={() => {
                          if (!works[ind].addRank) {
                            addPublish(works[ind].id).then();
                          }
                        }}
                      >
                        {!works[ind].addRank ? '添加到待发布' : '已添加到发布页'}
                      </Button>
                    )}
                  />
                );
              } else {
                return <Table.Column key={index} width={150} title={head.value} dataIndex={head.key}/>;
              }
            })}
          </Table.StickyLock>
          <LzPagination
            total={params.total}
            pageNum={params.pageNo}
            pageSize={params.pageSize}
            onChange={(e) => {
              console.log(e);
              const info = {
                pageNo: e.pageNum,
                pageSize: e.pageSize,
              };
              setParams({...params, ...info});
              getWorksPage(
                {
                  ...{
                    pageNo: e.pageNum,
                    pageSize: e.pageSize,
                  },
                },
              ).then();
            }}
          />
        </LzPanel>
      </div>
      <div hidden={pageType == 1}>
        <LzPanel>
          <Table.StickyLock dataSource={worksPublish} loading={loading}>
            {ranktableHeader.map((head, index) => {
              if (head.key === 'workImageUrl') {
                return (
                  <Table.Column
                    key={index}
                    width={150}
                    title={head.value}
                    cell={(val, ind: number, _) => (
                      <div style={{display: "flex", overflowX: 'auto', maxHeight: '120px'}}>
                        {worksPublish[ind].workImageUrl.map((item, index) => (
                          <img
                            style={{height: 'auto', width: '100%', aspectRatio: 'auto', objectFit: 'contain'}}
                            src={item}
                            onClick={() => {
                              setImgDialogUrl(item);
                              setShowImgDialog(true);
                            }}
                          />
                        ))}
                      </div>
                    )}
                  />
                );
              } else if (head.key === 'rankLimit') {
                return (
                  <Table.Column
                    key={index}
                    width={150}
                    title={head.value}
                    cell={(val, ind: number, _) => (
                      <Select
                        cacheValue={false}
                        defaultValue={worksPublish[ind].rankLimit}
                        onChange={(value) => {
                          worksPublish[ind].rankLimit = value;
                          verifyRank(worksPublish[ind].id, worksPublish[ind].rankLimit);
                          getRankList();
                        }}
                        dataSource={rankList}
                        style={{width: 100, marginRight: 8}}
                      />
                    )}
                  />
                );
              } else if (head.key === 'edit') {
                return (
                  <Table.Column
                    key={index}
                    width={100}
                    title={head.value}
                    align={'center'}
                    cell={(val, ind: number, _) => (
                      <Button
                        type="primary"
                        onClick={() => {
                          if (!worksPublish[ind].addRank) {
                            deleteWorks(worksPublish[ind].id).then();
                          }
                        }}
                      >
                        移除
                      </Button>
                    )}
                  />
                );
              } else {
                return <Table.Column key={index} width={150} title={head.value} dataIndex={head.key}/>;
              }
            })}
          </Table.StickyLock>
        </LzPanel>
        <Dialog footer={false} visible={showImgDialog} onClose={() => setShowImgDialog(false)}>
          <div className={styles.imgDialog} style={{backgroundImage: `url(${imgDialogUrl})`}}/>
        </Dialog>
      </div>
    </div>
  );
};
