import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import format from '@/utils/format';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface SKU_INFO {
  prizeName: string;
  prizeImg: string;
  unitCount: number;
  unitPrice: string;
}
export interface CustomValue {
  addBtnColor: string;
  addBtnBg: string;
  addCar: string;
  joinCar: string;
  titleBg: string;
  pageBg: string;
  pageColor: string;
}
export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  tabsList: any[];
  limitTotal: string;
  discount: string;
}
interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};
// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 页面设置', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  addBtnColor: '',
  addBtnBg: '',
  pageColor: '',
  addCar: '',
  joinCar: '',
  titleBg: '',
  pageBg: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `购物车计算器-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [dayjs().add(0, 'day'), dayjs().add(30, 'day')],
    // 活动开始时间
    startTime: format.formatDateTimeDayjs(dayjs().add(0, 'day')),
    // 活动结束时间
    endTime: format.formatDateTimeDayjs(dayjs().add(30, 'day')),
    tabsList: [],
    limitTotal: '',
    discount: '',
  };
};

export const skuInfo: SKU_INFO = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: '',
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 校验开始时间是否符合规则
// const isStartTimeValid = (startTime: string): boolean => {
//   const isLessThanTenMinutes: boolean = dayjs(startTime).isAfter(dayjs().subtract(10, 'minute'));
//   if (!isLessThanTenMinutes) {
//     Message.error('活动开始时间应大于当前时间');
//     return false;
//   }
//   return true;
// };
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 检查活动数据是否合法
const checkActivityStatus = (formData: PageData): boolean => {
  if (formData.tabsList.length === 0) {
    Message.error('请添加商品');
    return false;
  }
  const isTabsListValid: boolean = formData.tabsList.every((item, tabIndex) => {
    if (!item.tabTile) {
      Message.error(`Tab ${tabIndex + 1}: 缺少未选中按钮背景`);
      return false;
    }
    if (!item.tabActiveTile) {
      Message.error(`Tab ${tabIndex + 1}: 缺少已选中按钮背景`);
      return false;
    }
    const isSkuListValid = item.skuList.every((prize, skuIndex) => {
      if (!prize.prizeName) {
        Message.error(`请完善Tab ${tabIndex + 1}的第${skuIndex + 1}个商品信息`);
        return false;
      }
      return true;
    });
    return isSkuListValid;
  });

  if (!isTabsListValid) {
    return false;
  }

  return true;
};

export const checkActivityData = (formData: PageData): boolean => {
  // 开始时间异常
  // if (!isStartTimeValid(formData.startTime)) {
  //   return false;
  // }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 活动数据异常
  if (!checkActivityStatus(formData)) {
    return false;
  }
  return true;
};
