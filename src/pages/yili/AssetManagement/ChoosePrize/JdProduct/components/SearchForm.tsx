import { useState } from 'react';
import * as React from 'react';
import { Button, Form, Input } from '@alifd/next';

const initBeanSearchData = {
  skuName: '',
  skuOrWmsCode: '',
};

export default ({ onSearch }: any) => {
  const [beanSearchData, setBeanSearchData] = useState(initBeanSearchData);

  /**
   * 京豆筛选框赋值
   * @param {*} value 筛选值
   */
  const beanSelectData = (value: any) => {
    setBeanSearchData({
      ...beanSearchData,
      ...value,
    });
  };

  const onSearchClick = () => {
    onSearch(beanSearchData);
  };

  const onResetClick = () => {
    setBeanSearchData(initBeanSearchData);
    onSearch(initBeanSearchData);
  };

  return (
    <div>
      <Form inline>
        <Form.Item className="item" label="商品名称：">
          <Input
            value={beanSearchData.skuName}
            className="dialog-search-ctrl"
            placeholder="请输入商品名称"
            onChange={(skuName) => beanSelectData({ skuName })}
          />
        </Form.Item>
        <Form.Item className="item" label="商品编号：">
          <Input
            value={beanSearchData.skuOrWmsCode}
            className="dialog-search-ctrl"
            style={{ width: '200px' }}
            placeholder="请输入商品编号"
            onChange={(skuOrWmsCode) => beanSelectData({ skuOrWmsCode })}
          />
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSearchClick}>
            查询
          </Button>
          <Button style={{ marginLeft: 10 }} type="normal" onClick={onResetClick}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
