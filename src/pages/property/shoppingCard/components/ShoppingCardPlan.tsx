import React, { useEffect, useState } from 'react';
import { Form, Input, DatePicker2, Field, Table, Button, Message, Radio } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import Utils, { deepCopy, getParams } from '@/utils';
import dayJs from 'dayjs';
import format from '@/utils/format';
import { getResPrizeShoppingCardListByPage } from '@/api/sendShoppingCard';
import { any } from 'video.js/dist/types/utils/events';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const defaultPage = {
  pageNo: 1,
  pageSize: 10,
  total: 0,
};

export default ({ onChange, cancel }) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  // 选中的key列表
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const loadData = (query: any): void => {
    setLoading(true);
    query.activeType = 1;
    getResPrizeShoppingCardListByPage(query)
      .then((res: any): void => {
        res.data.forEach((item: any) => {
          item.id = item.planId;
        })
        setTableData(res.data as any[]);
        pageInfo.total = +res.page.total!;
        pageInfo.pageSize = +res.page.size!;
        pageInfo.pageNo = +res.page.total!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const handlePage = ({ pageSize, pageNo }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNo,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNo });
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);
  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]): void => {
    setSelectedRowKeys(selectKey);
    // console.log(selectKey, 'records========');
    const result = tableData
      .filter((item) => selectKey.includes(item.id));
    // console.log(result, 'result====');
    onChange(result[0]);
  };
  const rowSelection: {
    mode: 'single';
    selectedRowKeys: string[];
    onChange: (selectKey: string[]) => void;
  } = {
    mode: 'single',
    selectedRowKeys: selectedRowKeys,
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  };

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="planName" label="购物卡计划名称">
          <Input maxLength={20} placeholder="请输入购物卡计划名称" />
        </Form.Item>
        <Form.Item name="planId" label="计划ID">
          <Input placeholder="请输入计划ID" />
        </Form.Item>
        {/* <FormItem name="dateRange" label="任务时间">
          <RangePicker
            showTime
            hasClear={false}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem> */}
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div><Button text type='primary' onClick={() => {
        location.href = process.env.APP_MODE === 'prod' ? 'https://crmbsaas.dianpusoft.cn/property/shoppingCard' : 'https://crmbsaas-test.dianpusoft.cn/property/shoppingCard';
      }}>新建或查看全部购物卡计划&gt</Button> </div>
      <Table.StickyLock loading={loading} dataSource={tableData} rowSelection={rowSelection}>
        <Table.Column
          width={180}
          title="计划名称"
          lock="left"
          dataIndex="planName"
        />
        <Table.Column title="计划Id" width={180} dataIndex="id" cell={(_, index, row) => (
          <div>
            {
              <div className="lz-copy-text">
                {row.id ? Utils.mask(row.id) : '-'}
                {row.id && (
                  <span
                    className={'iconfont icon-fuzhi'}
                    style={{ marginLeft: 5 }}
                    onClick={() => {
                      Utils.copyText(row.id).then(() => {
                        Message.success('计划ID已复制到剪切板');
                      });
                    }}
                  />
                )}
              </div>
            }
          </div>
        )} />
        <Table.Column width={100} title="购物卡ID" dataIndex="shoppingCardId" cell={(_, index, row) => (
          <div>
            {
              <div className="lz-copy-text">
                {row.shoppingCardId ? Utils.mask(row.id) : '-'}
                {row.shoppingCardId && (
                  <span
                    className={'iconfont icon-fuzhi'}
                    style={{ marginLeft: 5 }}
                    onClick={() => {
                      Utils.copyText(row.shoppingCardId).then(() => {
                        Message.success('购物卡ID已复制到剪切板');
                      });
                    }}
                  />
                )}
              </div>
            }
          </div>
        )} />

        <Table.Column
          width={180}
          title="创建时间"
          cell={(value, index, row) => {
            return <div>{dayJs(row.createTime).format('YYYY-MM-DD HH:mm:ss')}</div>;
          }}
        />
        <Table.Column
          width={200}
          title="购物卡有效期"
          cell={(value, index, activity) => (
            <div>
              <p>起：{dayJs(activity.startDate).format('YYYY-MM-DD HH:mm:ss')}</p>
              <p>止：{dayJs(activity.endDate).format('YYYY-MM-DD HH:mm:ss')}</p>
            </div>
          )}
        />
        <Table.Column width={100} title="本金(元)" dataIndex="principal" cell={(data, index, row) => {
          return <div>{(parseFloat(row.principal) * 100 / 10000).toFixed(2)}</div>
        }} />
        <Table.Column width={100} title="面值(元)" dataIndex="amount" cell={(data, index, row) => {
          return <div>{(parseFloat(row.amount) * 100 / 10000).toFixed(2)}</div>
        }} />
        <Table.Column
          width={125}
          title='计划发放数量(张)'
          cell={(value, index, record) => {
            return (
              <div style={{ textAlign: 'center' }}>
                <div>{record.quantityTotal}</div>
              </div>
            );
          }}
        />
        <Table.Column
          width={105}
          title='已发数量(张)'
          cell={(value, index, record) => {
            return (
              <div style={{ textAlign: 'center' }}>
                <div>{record.quantityUsed}</div>
              </div>
            );
          }}
        />
        <Table.Column
          width={105}
          title="已冻结数量(张)"
          cell={(value, index, record) => {
            return (
              <div style={{ textAlign: 'center' }}>
                <div>{record.quantityFreeze}</div>
              </div>
            );
          }}
        />
        <Table.Column
          width={105}
          title='可用数量(张)'
          cell={(value, index, record) => {
            return (
              <div style={{ textAlign: 'center' }}>
                <div>{record.quantityRemain}</div>
              </div>
            );
          }}
        />
      </Table.StickyLock>
      <LzPagination
        pageNum={pageInfo.pageNo}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
    </div>
  );
};
