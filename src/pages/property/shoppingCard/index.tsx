/**
 * 预售有礼数据报表
 */
import React, { useRef, useState } from 'react';
import { Button, Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import SendRecord from './components/SendRecord';
import styles1 from './style.module.scss';
import BatchSendDialog from './components/BatchSendDialog';
import LzDialog from '@/components/LzDialog';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  const [showBatchDialog,setShowBatchDialog] = useState(false);
  const currentRef = useRef<{ refresh: () => void } | null>(null); // 明确类型

  // 批量发放
  const batchSend = () => {
    setShowBatchDialog(true);
  };
  const batchSendOnChange = () => {
    setShowBatchDialog(false);
    currentRef.current?.refresh();
  };
  return (
    <div className="crm-container">
      <LzPanel title="手动发放权益">
        <div className={styles1.pageDivAll}>
          <div className={styles1.tipDiv}>创建发放权益任务进行权益发送</div>
          <div className={styles1.sendCardDiv}>
            <img src="//img10.360buyimg.com/imgzone/jfs/t1/223669/22/20378/5687/643cbc1fF3fa4e814/01cf6b772023e17f.png" alt="" />
            <div className={styles1.contentDiv}>
              <div>发购物卡</div>
              <div>通过用户PIN进行购物卡发放</div>
              <div className={styles1.btnDiv}>
                <Button type="primary" onClick={() => batchSend()}>批量发放</Button>
              </div>
            </div>
          </div>
        </div>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="任务记录" key="1">
            <SendRecord cRef={currentRef} />
          </Tab.Item>
        </Tab>
      </LzPanel>

      <LzDialog
        title="购物卡批量发放"
        className="lz-dialog-large"
        visible={showBatchDialog}
        footer={false}
        onCancel={() => setShowBatchDialog(false)}
        onClose={() => setShowBatchDialog(false)}
      >
        <BatchSendDialog
          cRef={currentRef}
          onChange={batchSendOnChange}
          cancel={() => setShowBatchDialog(false)}
        />
      </LzDialog>
    </div>
  );
};
