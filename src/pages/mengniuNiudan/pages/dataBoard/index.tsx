import React, { useEffect, useReducer, useState } from 'react';
import {
  Balloon,
  Button,
  Field,
  Form,
  Input,
  Message,
  Table,
  Typography,
  Dialog,
  DatePicker2,
  Select,
} from '@alifd/next';
import LzMsg from '@/components/LzMsg';
// import { goodDramasKeepGoSelectData, goodDramasKeepGoExport, chunZhenGiftSelectData, chunZhenGiftExport } from '@/api/chunzhenDataBoard';
import { crm90031DataGetRecordListExport, crm90031DataGetRecordPage } from '@/api/mengniu';
import LzDialog from '@/components/LzDialog';
import LzPanel from '@/components/LzPanel';
import styles from './style.module.scss';
import { downloadExcel, getParams } from '@/utils';
import format from '@/utils/format';
import dayjs from 'dayjs';
import constants from '@/utils/constant';
import LzPagination, { Pager } from '@/components/LzPagination';
import { FormLayout } from '@/pages/activity/10021/1001/util';
const Option = Select.Option;
const FormItem = Form.Item;
const dateFormat: string = constants.DATE_FORMAT_TEMPLATE;
const { RangePicker } = DatePicker2;

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(true); // true展示好剧不断  false会员礼遇
  const [list, setList] = useState<any>([]);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  // 请求数据
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    startTime: '',
    endTime: '',
    ...initPager,
  });
  // 活动
  const [activity, setActivity] = useState<any>('');

  const rangePreset = {
    上个月: [dayjs().add(-1, 'month').startOf('month'), dayjs().add(-1, 'month').endOf('month')],
    本月: [dayjs().startOf('month'), dayjs().endOf('month')],
    下个月: [dayjs().add(+1, 'month').startOf('month'), dayjs().add(+1, 'month').endOf('month')],
    昨天: [dayjs().add(-1, 'day'), dayjs()],
    今天: [dayjs(), dayjs().add(1, 'day')],
  };
  // 日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setParams({
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
      activityId: getParams('id'),
    });
  };

  const handleSubmit = () => {
    // const formValue = field.getValues() as any;
    // // formValue中undefined的值转成空字符串
    // if (formValue) {
    //   Object.keys(formValue).forEach((key) => {
    //     formValue[key] = formValue[key] || '';
    //   });
    // }
    setParams({ ...params, pageNum: 1, pageSize: params.pageSize, activityId: getParams('id') });
    setPageInfo({ ...pageInfo, pageNum: 1 });
    getList({ ...params }).then();
  };
  const handleReset = () => {
    setParams({ startTime: '', endTime: '', pageNum: 1, pageSize: params.pageSize, activityId: getParams('id') });
    setActivity('');
    // getList({ startTime: '', endTime: '', pageNum: 1, pageSize: params.pageSize }).then();
  };
  const exportData = () => {
    crm90031DataGetRecordListExport(params).then((data: any) => downloadExcel(data, ``));
  };
  const getList = async (query) => {
    const postData = {
      ...params,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      ...query,
    };
    console.log('*************************', postData);
    try {
      setLoading(true);
      let result = {};
      result = await crm90031DataGetRecordPage(postData);
      setList((result.records as any) || []);
      setPageInfo({
        total: result.total,
        pageNum: result.current,
        pageSize: result.size,
      } as any);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPageInfo({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    getList({ ...formValue, pageSize, pageNum }).then();
  };
  useEffect(() => {
    setList([]);
    // getList({ ...pageInfo, pageNum: 1 }).then();
  }, []);

  const currentDate = dayjs('2024-07-01');

  const disabledDate = function (date, mode) {
    switch (mode) {
      case "date":
        return date.valueOf() <= currentDate.valueOf();
      case "year":
        return date.year() < currentDate.year();
      case "month":
        return (
          date.year() * 100 + date.month() <
          currentDate.year() * 100 + currentDate.month()
        );
      default:
        return false;
    }
  };

  return (
    <div>
      <LzPanel>
        <Form className="lz-query-criteria" field={field} {...formItemLayout}>
          <FormItem name="dateRange" label="选择时间">
            <RangePicker
              format="YYYY-MM-DD"
              // preset={rangePreset}
              hasClear={false}
              disabledDate={disabledDate}
              onChange={(value) => {
                onDataRangeChange(value);
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" onClick={handleSubmit}>
              查询
            </Form.Submit>
            <Form.Reset
              onClick={() => {
                handleReset();
              }}
            >
              重置
            </Form.Reset>
            <Button type="primary" onClick={exportData}>
              导出
            </Button>
          </FormItem>
        </Form>
      </LzPanel>
      <LzPanel>
          <Table.StickyLock dataSource={list} loading={loading}>
            <Table.Column width={150} title="日期" lock dataIndex="dataDate" />
            <Table.Column width={150} title="PV" dataIndex="pv" />
            <Table.Column width={150} title="UV" dataIndex="uv" />
            <Table.Column width={150} title="抽奖次数" dataIndex="drawNum" />
            <Table.Column width={150} title="浏览商品次数" dataIndex="browseSkuNum" />
            <Table.Column width={150} title="加购商品次数" dataIndex="addSkuNum" />
            <Table.Column width={150} title="分享助力次数" dataIndex="inviteHelpNum" />
            <Table.Column width={150} title="签到次数" dataIndex="checkInNum" />
            <Table.Column width={150} title="蒙牛京东自营旗舰店开卡人数" dataIndex="mengniuMemberNum" />
            <Table.Column width={150} title="蒙牛京东自营旗舰店下单人数" dataIndex="mengniuOrderNum" />
            <Table.Column width={150} title="蒙牛京东自营旗舰店下单金额" dataIndex="mengniuGmv" />
            <Table.Column width={150} title="蒙牛牛奶旗舰店开卡人数" dataIndex="mengniuMilkMemberNum" />
            <Table.Column width={150} title="蒙牛牛奶旗舰店下单人数" dataIndex="mengniuMilkOrderNum" />
            <Table.Column width={150} title="蒙牛牛奶旗舰店下单金额" dataIndex="mengniuMilkGmv" />
            <Table.Column width={150} title="蒙牛官方旗舰店开卡人数" dataIndex="mengniuPopMemberNum" />
            <Table.Column width={150} title="蒙牛官方旗舰店下单人数" dataIndex="mengniuPopOrderNum" />
            <Table.Column width={150} title="蒙牛官方旗舰店下单金额" dataIndex="mengniuPopGmv" />
            <Table.Column width={150} title="特仑苏官方旗舰店开卡人数" dataIndex="terlunsuMemberNum" />
            <Table.Column width={150} title="特仑苏官方旗舰店下单人数" dataIndex="terlunsuOrderNum" />
            <Table.Column width={150} title="特仑苏官方旗舰店下单金额" dataIndex="terlunsuGmv" />
            <Table.Column width={150} title="纯甄旗舰店开卡人数" dataIndex="chunzhenMemberNum" />
            <Table.Column width={150} title="纯甄旗舰店下单人数" dataIndex="chunzhenOrderNum" />
            <Table.Column width={150} title="纯甄旗舰店下单金额" dataIndex="chunzhenGmv" />
          </Table.StickyLock>
        <LzPagination
          total={pageInfo.total}
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          onChange={handlePage}
        />
      </LzPanel>
    </div>
  );
};
