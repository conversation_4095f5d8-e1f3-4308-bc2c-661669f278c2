import React, { useReducer } from 'react';
import { Message, Loading, Tab } from '@alifd/next';

import PreviewArea from './PreviewArea';
import DecorateComponentArea from './DecorateComponentArea';
import { decorateReducer, initialState } from '@/pages/feihe/memberCenter/isvSmartCard/decorateReducer';
import { getZphySmartCardJson } from '@/api/zphy';
import { deepCopy } from '@/utils';

import styles from './index.module.scss';

const tabList = [
  { key: 'NotMemberNew', title: '非会员' },
  { key: 'ZROldMember', title: '卓睿会员' },
  { key: 'OldMember', title: '非卓睿会员' },
];

export default () => {
  const [state, dispatch] = useReducer(decorateReducer, initialState());
  const [init, setInit] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [activeKey, setActiveKey] = React.useState('NotMemberNew');
  function convertAtoB(a: any) {
    const b: any = {};
    b.NotMemberNew = {
      bgImg: a.bgImg.notMember,
      tabs: [a.NotMemberNew, a.NotMemberOldMember, a.NotMemberZROldMember],
    };
    b.ZROldMember = {
      bgImg: a.bgImg.zr,
      tabs: [a.NewMember, a.ZROldMember_4, a.ZROldMember_2, a.ZROldMember_3, a.ZROldMember_5],
    };
    b.OldMember = {
      bgImg: a.bgImg.normal,
      tabs: [a.OldMember_4, a.OldMember_2, a.OldMember_3, a.OldMember_5],
    };
    return b;
  }

  const getInitData = async () => {
    setLoading(true);
    try {
      const res: any = await getZphySmartCardJson({
        type: 1,
      });
      const result = convertAtoB(JSON.parse(res.json).SmartCard);
      dispatch({
        type: 'INIT_MODULE',
        payload: { SmartCard: deepCopy(result) },
      });
      dispatch({
        type: 'INIT_DEFAULT',
        payload: deepCopy(result),
      });
      dispatch({
        type: 'INIT_DEFAULT_DATA',
        payload: res.version,
      });
      setInit(true);
      setLoading(false);
    } catch (e) {
      setLoading(false);
      Message.error(e.message);
    }
  };
  const popupShow = (key) => {
    // Dialog.confirm({
    //   v2: true,
    //   title: '确认',
    //   content: '切换将失去未保存的修改',
    //   onOk: () => {
    //     changeActiveKey(key);
    //   },
    //   onCancel: () => console.log('cancel'),
    // });
    changeActiveKey(key);
  };
  const changeActiveKey = (key) => {
    setActiveKey(key);
    dispatch({ type: 'INIT_KEY', payload: '0' });
  };
  React.useEffect(() => {
    getInitData().then();
  }, []);
  return (
    <div>
      <div id="main">
        <Loading visible={loading} style={{ width: '100%', minHeight: '80vh' }}>
          <Tab activeKey={activeKey} onChange={(key) => popupShow(key)}>
            {tabList.map((item) => {
              return <Tab.Item title={item.title} key={item.key} />;
            })}
          </Tab>
          {init && (
            <div className={styles.mainContainer}>
              <PreviewArea activeKey={activeKey} state={state} dispatch={dispatch} />
              <DecorateComponentArea
                activeKey={activeKey}
                selectedModule={state.selectedModule}
                state={state}
                dispatch={dispatch}
              />
            </div>
          )}
        </Loading>
      </div>
    </div>
  );
};
