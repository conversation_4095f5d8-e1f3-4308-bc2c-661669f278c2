import { getF<PERSON><PERSON>ist, getZphyGiftInfoList, saveZphyGiftInfoList, save<PERSON><PERSON><PERSON><PERSON> } from '@/api/zphy';
import LzDialog from '@/components/LzDialog';
import LzImageSelector from '@/components/LzImageSelector';
import { urlRegularCheck } from '@/pages/feihe/memberCenter/decorate/utils';
import { deepCopy, numRegularCheckInt } from '@/utils';
import {
  Button,
  Card,
  Checkbox,
  Divider,
  Field,
  Form,
  Input,
  Message,
  NumberPicker,
  Pagination,
  Radio,
  Switch,
  Table,
} from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

function ClassInterest({ data, allJson, dispatch, defaultData }) {
  console.log('ClassInterest', data);

  const formItemLayout = {
    labelCol: {
      span: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };
  const field = Field.useField({});
  const setData = (value) => {
    dispatch({ type: 'UPDATE_MODULE', payload: value });
  };
  const levelNameList = ['LV3', 'LV4', 'LV5', 'LV6'];
  const levelList = ['3', '4', '5', '6'];
  const [visible, setVisible] = React.useState(false);
  const [pageLoading, setPageLoading] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [activeIndex, setActiveIndex] = React.useState(0);
  const [prizeList, setPrizeList] = React.useState<any>([]);
  const [pagePrizeList, setPagePrizeList] = React.useState<any>([]);
  const [total, setTotal] = React.useState<any>(0);
  const getPrizeList = async (page) => {
    if (page) {
      setPagePrizeList(prizeList.slice((page - 1) * 10, page * 10));
    } else {
      setLoading(true);
      const res = await getFuluList({ source: 'fh' });
      setPrizeList(res);
      setTotal(res.length);
      setPagePrizeList(res.slice(0, 10));
      setLoading(false);
    }
  };
  const addItem = () => {
    const obj = {
      memo: allJson.showZhuoRui ? `卓睿V3` : `非卓睿V3`,
      showareacode: '2',
      name: '-',
      showimage: '',
      multiproperty: '3',
      priceinfo: '6',
      totalnum: 0,
      type: 7,
      producturl: '',
      value: '',
    };
    data.levelGiftList.push(obj);
    setData({ levelGiftList: data.levelGiftList });
  };
  const isValidBiActivity = (activityList) => {
    for (const element of activityList) {
      if (element.name == '-' || element.name == '') {
        Message.error('请填写奖品名称');
        return false;
      }
      if (!element.showimage) {
        Message.error('请上传奖品图片');
        return false;
      }
      if (!element.totalnum && element.type !== 5) {
        Message.error('请填写奖品库存');
        return false;
      }
      if (element.type == 6 && !element.value) {
        Message.error('直充卡券请选择奖品');
        return false;
      }
      if (element.type == 5 && !element.producturl) {
        Message.error('跳转链接请配置链接');
        return false;
      }
      if (!element.multiproperty) {
        return Message.error('请选择可领取的等级');
      }
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!data.levelBenefitBgImg) {
      Message.error('请上传奖品底图');
      return false;
    }
    if (data.levelGiftList.length == 0) {
      Message.error('请添加奖品');
      return false;
    }
    if (!isValidBiActivity(data.levelGiftList)) {
      return false;
    }

    let updateJson = {};
    if (allJson.showZhuoRui) {
      updateJson = {
        ...allJson,
        ZRNewMember: {
          ...allJson.ZRNewMember,
          MemberCard: {
            ...allJson.ZRNewMember.MemberCard,
            levelBenefitBgImg: data.levelBenefitBgImg,
          },
        },
        ZROldMember: {
          ...allJson.ZROldMember,
          MemberCard: {
            ...allJson.ZROldMember.MemberCard,
            levelBenefitBgImg: data.levelBenefitBgImg,
          },
        },
      };
    } else {
      updateJson = {
        ...allJson,
        NewMember: {
          ...allJson.NewMember,
          MemberCard: {
            ...allJson.NewMember.MemberCard,
            levelBenefitBgImg: data.levelBenefitBgImg,
          },
        },
        OldMember: {
          ...allJson.OldMember,
          MemberCard: {
            ...allJson.OldMember.MemberCard,
            levelBenefitBgImg: data.levelBenefitBgImg,
          },
        },
      };
    }
    updateJson.ZRNewMember.MemberCard.exchangeLevelBenefitShow = data.exchangeLevelBenefitShow;
    updateJson.ZROldMember.MemberCard.exchangeLevelBenefitShow = data.exchangeLevelBenefitShow;
    updateJson.NewMember.MemberCard.exchangeLevelBenefitShow = data.exchangeLevelBenefitShow;
    updateJson.OldMember.MemberCard.exchangeLevelBenefitShow = data.exchangeLevelBenefitShow;
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    saveZphyJson(params)
      .then((res) => {
        // Message.success('保存成功');
        let updateList = deepCopy(defaultData.giftInfoRes);
        if (allJson.showZhuoRui) {
          updateList = updateList.filter((e) => e.showareacode !== '2' || e.memo.indexOf('非') !== -1);
        } else {
          updateList = updateList.filter((e) => e.showareacode !== '2' || e.memo.indexOf('非') == -1);
        }
        data.levelGiftList.forEach((item, index) => {
          data.levelGiftList[index].orderval = index + 1;
        });

        updateList = updateList.concat(data.levelGiftList);
        const params1 = {
          showAreaCode: 2,
          zphyGiftInfoList: updateList,
        };
        console.log(updateList);
        saveZphyGiftInfoList(params1).then((res) => {
          Message.success('保存成功');
          setPageLoading(false);
          dispatch({ type: 'UPDATE_REFRESHPAGE', payload: true });
        });
      })
      .catch((e) => {
        Message.error(e.message);
        setPageLoading(true);
      });
  };
  const getZphyGiftInfoListFn = async () => {
    const giftInfoRes = await getZphyGiftInfoList();
    dispatch({
      type: 'INIT_DEFAULT',
      payload: {
        ...defaultData,
        giftInfoRes: deepCopy(giftInfoRes),
      },
    });
  };
  const commonProps = {
    title: '等级权益',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };

  const formItemLayout2 = {
    labelCol: {
      span: 2,
    },
    wrapperCol: {
      span: 22,
    },
    labelAlign: 'left',
    colon: true,
  };

  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <div className={styles.operation}>
            <Form field={field} {...formItemLayout2}>
              <Form.Item label="是否展示">
                <Switch
                  checked={data.exchangeLevelBenefitShow}
                  onChange={(value) => {
                    setData({
                      exchangeLevelBenefitShow: value,
                    });
                  }}
                />
              </Form.Item>
            </Form>
            <div className={styles.kvContainer}>
              <div className="crm-label">奖品底图上传:</div>
              <div className={styles.imgUpload}>
                <LzImageSelector
                  width={713}
                  height={455}
                  value={data.levelBenefitBgImg}
                  onChange={(levelBenefitBgImg) => {
                    setData({ levelBenefitBgImg });
                  }}
                />
                <div className={styles.tip}>
                  <div>图片尺寸：为713*455px,支持jpg、jpeg、png格式，大小不超过1M</div>
                </div>
              </div>
              <div className={styles.colorPicker}>
                <Button
                  style={{ marginLeft: '35px' }}
                  type={'primary'}
                  text
                  onClick={() => {
                    setData({ levelBenefitBgImg: defaultData.MemberCard.levelBenefitBgImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </div>
            <div className={styles.kvContainer}>
              <div className="crm-label">奖品配置:</div>
              <div style={{ marginTop: '10px' }}>
                <Button type={'primary'} onClick={() => addItem()} disabled={data.levelGiftList.length >= 20}>
                  添加（{data.levelGiftList.length} / 20）
                </Button>
                <span className={'tip'} style={{ marginLeft: 5 }}>
                  注：最多可添加20个奖品，至少保留一个奖品
                </span>
              </div>
              {data.levelGiftList.map((item, index) => {
                return (
                  <div key={index} className={styles['list-box']}>
                    <div className={styles['prize-title']}>
                      <div style={{ marginRight: '51px' }}>奖品{index + 1}配置</div>
                      <Radio.Group
                        value={item.type}
                        defaultValue={item.type}
                        onChange={(type: number) => {
                          data.levelGiftList[index].type = type;
                          setData({ levelGiftList: data.levelGiftList });
                        }}
                      >
                        <Radio id="1" value={6}>
                          直充卡券
                        </Radio>
                        <Radio id="2" value={7}>
                          实物
                        </Radio>
                        <Radio id="3" value={5}>
                          跳转链接
                        </Radio>
                      </Radio.Group>
                      {/* 删除 */}
                      <Button
                        style={{ marginLeft: '20px' }}
                        type={'primary'}
                        text
                        disabled={data.levelGiftList.length <= 1}
                      >
                        <i
                          className="iconfont icon-icon-07 btn-del"
                          onClick={() => {
                            data.levelGiftList.splice(index, 1);
                            setData({ levelGiftList: data.levelGiftList });
                          }}
                        />
                      </Button>
                    </div>
                    <div className={styles['prize-content']}>
                      <div className={styles.imgUpload} style={{ flexDirection: 'column' }}>
                        <LzImageSelector
                          width={250}
                          height={162}
                          value={item.showimage}
                          onChange={(showimage) => {
                            item.showimage = showimage;
                            setData({ levelGiftList: data.levelGiftList });
                          }}
                        />
                        <Button
                          style={{ marginTop: '10px' }}
                          type={'primary'}
                          text
                          onClick={() => {
                            data.levelGiftList[index].showimage = defaultData.giftInfoRes.filter(
                              (e) => e.id == item.id,
                            )[0].showimage;
                            setData({ levelGiftList: data.levelGiftList });
                          }}
                        >
                          重置
                        </Button>
                      </div>
                      <div style={{ width: '100%' }}>
                        {item.type == 6 && (
                          // 直充卡券
                          <div>
                            <Button
                              type={'primary'}
                              text
                              style={{ marginLeft: '10px', marginRight: '10px' }}
                              onClick={() => {
                                setActiveIndex(index);
                                setVisible(true);
                                getPrizeList();
                              }}
                            >
                              选择奖品
                            </Button>
                            {item.fuluName}
                          </div>
                        )}
                        {item.type == 5 && (
                          // 跳转链接
                          <Form style={{ width: '100%', marginLeft: '10px' }} field={field} inline>
                            <Form.Item
                              label="配置链接"
                              required
                              name={`producturl${index}`}
                              requiredMessage={'请输入配置链接'}
                              validator={urlRegularCheck}
                            >
                              <Input
                                style={{ width: '300px' }}
                                name={`producturl${index}`}
                                value={item.producturl}
                                placeholder={'请输入配置链接'}
                                onFocus={() => {}}
                                onChange={(val) => {
                                  item.producturl = val;
                                  setData({ levelGiftList: data.levelGiftList });
                                }}
                              />
                            </Form.Item>
                          </Form>
                        )}
                        <div className={styles['prize-info']}>
                          <div style={{ display: 'flex', width: '100%' }}>
                            <div className={styles['prize-left']}>
                              <div className={styles['prize-name']}>
                                当前奖品名称：
                                <span style={{ color: '#1677ff' }}>{item.name}</span>
                              </div>
                              <div>
                                当前奖品库存：
                                <span style={{ color: '#1677ff' }}>
                                  {Number(item.totalnum) - Number(item.hassendnum || 0)}
                                </span>
                              </div>
                            </div>
                            <Form style={{ width: '50%' }} {...formItemLayout} field={field}>
                              <Form.Item
                                label="修改为："
                                // required
                                requiredMessage="请输入奖品名称（对用户展示）"
                              >
                                <Input
                                  trim
                                  style={{ width: '200px' }}
                                  className="input-ctrl"
                                  placeholder="请输入奖品名称（对用户展示）"
                                  name={`name${index}`}
                                  onChange={(v) => {
                                    item.name = v;
                                    setData({ levelGiftList: data.levelGiftList });
                                  }}
                                />
                              </Form.Item>
                              <Form.Item
                                label="增加库存："
                                // required
                                requiredMessage="请输入>=0的正整数"
                                validator={numRegularCheckInt}
                              >
                                <NumberPicker
                                  style={{ width: '200px' }}
                                  name={`restNum${index}`}
                                  defaultValue={0}
                                  onChange={(v) => {
                                    if (v !== undefined) {
                                      if (defaultData.giftInfoRes.filter((e) => e.id == item.id).length > 0) {
                                        item.totalnum =
                                          Number(defaultData.giftInfoRes.filter((e) => e.id == item.id)[0].totalnum) +
                                          Number(v);
                                      } else {
                                        item.totalnum = Number(v);
                                      }
                                      setData({ levelGiftList: data.levelGiftList });
                                    }
                                  }}
                                  type="inline"
                                  min={0}
                                  max={9999999}
                                />
                              </Form.Item>
                            </Form>
                          </div>
                          <div className={styles['level-box']}>
                            可领取的等级设置：
                            <Checkbox.Group
                              onChange={(v) => {
                                if (v.length > 0) {
                                  const newList = v.sort((a, b) => {
                                    return parseInt(a) - parseInt(b);
                                  });
                                  item.multiproperty = newList.join(',');
                                  item.memo = allJson.showZhuoRui ? `卓睿V${v[0]}` : `非卓睿V${v[0]}`;
                                  setData({ levelGiftList: data.levelGiftList });
                                }
                              }}
                              value={item.multiproperty.split(',')}
                            >
                              {levelList.map((option, i) => (
                                <Checkbox className={styles.item} key={option} value={option}>
                                  {levelNameList[i]}
                                </Checkbox>
                              ))}
                            </Checkbox.Group>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>图片尺寸：为250*162px,支持jpg、jpeg、png格式，大小不超过1M</div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card.Content>
      </Card>
      <LzDialog
        title={'选择奖品'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '660px' }}
      >
        <div>
          <Table dataSource={pagePrizeList} style={{ marginTop: '15px' }} loading={loading}>
            <Table.Column title="序号" align={'center'} cell={(_, index, row) => <span>{index + 1}</span>} />
            <Table.Column title="奖品" align={'center'} dataIndex="productName" />
            <Table.Column
              title="操作"
              align={'center'}
              cell={(_, index, row) => (
                <Button
                  type={'primary'}
                  text
                  style={{ marginLeft: '10px' }}
                  onClick={() => {
                    console.log(activeIndex, row.productId);
                    data.levelGiftList[activeIndex].value = row.productId;
                    data.levelGiftList[activeIndex].fuluName = row.productName;
                    setVisible(false);
                  }}
                >
                  选择
                </Button>
              )}
            />
          </Table>
          <Pagination
            total={total}
            onChange={(v) => {
              getPrizeList(v);
            }}
            className="page-demo"
          />
        </div>
      </LzDialog>
    </div>
  );
}
export default ClassInterest;
