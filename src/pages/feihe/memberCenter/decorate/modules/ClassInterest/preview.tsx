import { Slider } from '@alifd/next';
import React from 'react';
import styles from './index.module.scss';

export default ({ data, dispatch }) => {
  const [activeTab, setActiveTab] = React.useState<Number>(3);
  const canReceiveButtonImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/246487/7/802/10753/658a6936F8de0ac04/a5d379aef2c04ccb.png';
  const receivedButtonImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/243333/4/752/8395/658a6937F45017a6a/095624e8d89f98c0.png';
  const lockButtonImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/238455/32/1312/8574/658a6936F9eb1ddcb/c1da57652b8cc581.png';

  const buttonMap = {
    0: canReceiveButtonImg,
    1: receivedButtonImg,
    2: lockButtonImg,
  };
  const LevelImageMap = {
    0: 'https://img10.360buyimg.com/imgzone/jfs/t1/227929/20/11033/5621/658d24a8F428a64a9/7a98f86b99a2ca71.png',
    1: 'https://img10.360buyimg.com/imgzone/jfs/t1/240026/8/1378/3338/658d24a8F9a610cf5/a27d6e6595557009.png',
    2: 'https://img10.360buyimg.com/imgzone/jfs/t1/243193/32/1153/3399/658d24a8F297f961b/09268c567b2b8996.png',
    3: 'https://img10.360buyimg.com/imgzone/jfs/t1/248678/14/989/5587/658d24a8F32c7988b/1761706231d24a02.png',
    4: 'https://img10.360buyimg.com/imgzone/jfs/t1/94972/8/32134/3192/658d24a9F6237ea87/7651ea7b32c7ec02.png',
    5: 'https://img10.360buyimg.com/imgzone/jfs/t1/245741/28/1117/5561/658d24a9F0fe4a85b/adcd0bca86bc451b.png',
    6: 'https://img10.360buyimg.com/imgzone/jfs/t1/245949/5/1115/5800/658d24a9F18a0145e/daa03bf8671f9169.png',
  };
  const tabs = [3, 4, 5, 6];
  return (
    <div className={`${styles.preview} ${data.exchangeLevelBenefitShow ? `` : `${styles.hide}`}`}>
      <div
        className={styles.levelBgImg}
        style={{
          backgroundImage: `url(${data.levelBenefitBgImg})`,
        }}
      >
        <div className={styles.tabs}>
          {tabs.map((item, index) => (
            <div
              key={index}
              className={`${styles['tab-item']} ${item === activeTab ? `${styles.active}` : `${styles['not-active']}`}`}
              onClick={() => {
                setActiveTab(item);
              }}
            >
              <img className={styles['tab-title']} src={LevelImageMap[item]} />
            </div>
          ))}
        </div>
        <div className={styles['level-exchange-list-container']}>
          {data.levelGiftList.filter((v) => v.multiproperty.indexOf(activeTab) !== -1).length > 2 ? (
            <Slider
              slidesToShow={2.12}
              arrowPosition="inner"
              dots={false}
              arrows={false}
              autoplay
              infinite={false}
              lazyLoad
            >
              {data.levelGiftList
                .filter((v) => v.multiproperty.indexOf(activeTab) !== -1)
                ?.map((item, index) => (
                  <div className={`${styles['level-benefits-exchange-item-wrapper']}`} key={index}>
                    <div className={styles['level-benefits-exchange-item']}>
                      <div className={styles['surplus-count']}>
                        <span className={styles.span}>剩余</span>
                        <span className={styles.num}>{Number(item.totalnum) - Number(item.hassendnum || 0)}</span>
                        <span className={styles.span}>份</span>
                      </div>
                      <div
                        className={styles['level-benefits-exchange-item-img']}
                        style={{ backgroundImage: `url(${item.showimage})` }}
                      />
                      <span className={styles['level-benefits-exchange-item-name']}>{item.name}</span>
                      <div className={styles['exchange-button']}>
                        <div
                          className={styles['exchange-button-img']}
                          style={{ backgroundImage: `url(${buttonMap[2]})` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
            </Slider>
          ) : (
            <>
              {data.levelGiftList
                .filter((v) => v.multiproperty.indexOf(activeTab) !== -1)
                ?.map((item, index) => (
                  <div className={`${styles['level-benefits-exchange-item-wrapper']}`} key={index}>
                    <div className={styles['level-benefits-exchange-item']}>
                      <div className={styles['surplus-count']}>
                        <span className={styles.span}>剩余</span>
                        <span className={styles.num}>{Number(item.totalnum) - Number(item.hassendnum || 0)}</span>
                        <span className={styles.span}>份</span>
                      </div>
                      <div
                        className={styles['level-benefits-exchange-item-img']}
                        style={{ backgroundImage: `url(${item.showimage})` }}
                      />
                      <span className={styles['level-benefits-exchange-item-name']}>{item.name}</span>
                      <div className={styles['exchange-button']}>
                        <div
                          className={styles['exchange-button-img']}
                          style={{ backgroundImage: `url(${buttonMap[2]})` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
