import FIcon from '@/pages/feihe/components/FIcon';
import React from 'react';
import style from './CompareItem.module.scss';

export default (props) => {
  const { data } = props;

  let iconType;
  let className;

  if (data === '/') {
    iconType = null;
    className = style.neutral;
  } else {
    const value = parseFloat(data);
    if (isNaN(value)) {
      iconType = null;
      className = style.neutral;
    } else if (value > 0) {
      iconType = 'zengchangjiantou';
      className = style.positive;
    } else if (value < 0) {
      iconType = 'xiajiangjiantou';
      className = style.negative;
    } else {
      iconType = 'compare-not-change';
      className = style['no-change'];
    }
  }

  return (
    <div className={`${style['compare-item']} ${className}`}>
      <div className={style.icon}>{iconType && <FIcon className={style.icon} type={iconType} size="xs" />}</div>
      <span>{iconType ? `${Math.abs(parseFloat(data)).toFixed(2)}%` : data}</span>
    </div>
  );
};
