import React from 'react';
import styles from './index.module.scss';

export default ({ data }) => {
  return (
    <div className={styles.preview}>
      {(!data || !data.filter((e) => e.bg).length) && <div className={styles.empty} />}
      {data.map((item, dataIndex) => (
        <div key={dataIndex} className={styles.previewItem}>
          {item.bg && <img src={item.bg} alt="" />}
          {item.hotZoneList.map((hotZone, index) => (
            <div
              key={index}
              className={styles.hotZone}
              style={{
                left: `${hotZone.left}px`,
                top: `${hotZone.top}px`,
                width: `${hotZone.width}px`,
                height: `${hotZone.height}px`,
              }}
              onClick={() => {
                console.log(hotZone.url);
              }}
            >
              热区0{index + 1}
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};
