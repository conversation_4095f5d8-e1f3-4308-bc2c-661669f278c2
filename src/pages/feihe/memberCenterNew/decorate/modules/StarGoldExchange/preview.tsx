import React from 'react';
import styles from './index.module.scss';

export default ({ data, dispatch }) => {
  const lockImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/230768/18/10042/5683/658a6937Fb2e24512/62f4935bbf890495.png';
  const unLockImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/242396/18/701/5560/658a6937Fa7dd3a10/36d359a1c6647e52.png';
  const canReceiveButtonImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/246487/7/802/10753/658a6936F8de0ac04/a5d379aef2c04ccb.png';
  const receivedButtonImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/243333/4/752/8395/658a6937F45017a6a/095624e8d89f98c0.png';
  const lockButtonImg =
    'https://img10.360buyimg.com/imgzone/jfs/t1/238455/32/1312/8574/658a6936F9eb1ddcb/c1da57652b8cc581.png';

  const buttonMap = {
    0: canReceiveButtonImg,
    1: receivedButtonImg,
    2: lockButtonImg,
  };
  return (
    <div className={`${styles.preview} ${data.exchangeBenefitShow ? `` : `${styles.hide}`}`}>
      <div className={styles['scroll-box']}>
        {data.starGoldExchangeGiftList.map((item, index) => {
          return (
            <div
              key={index}
              className={`${styles['exchange-item-wrapper']} ${styles[`${item.restNum === 0 ? `no-stock` : ``}`]}`}
              style={{ backgroundImage: `url(${data.exchangeBenefitBgImg})` }}
            >
              <div
                className={`${styles['exchange-item']}
        ${styles[`exchange-item-status-2`]}`}
              >
                <div className={styles['surplus-count']}>
                  <span className={styles.span}>剩</span>
                  <span className={styles.num}>{Number(item.totalnum) - Number(item.hassendnum || 0)}</span>
                  <span className={styles.span}>份</span>
                </div>
                <div className={styles['need-count']}>
                  <span className={styles.num}>{item.priceinfo}</span>
                  <span className={styles.span}>星币</span>
                </div>
                <img className={styles['lock-icon']} src={lockImg} />
                <div className={styles['exchange-info']}>
                  <img className={styles['exchange-img']} src={item.showimage} />
                  <span className={styles['exchange-name']}>{item.name}</span>
                </div>
                <div className={`${styles['exchange-button']}`}>
                  <img className={styles['exchange-button-img']} src={buttonMap[2]} />
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <img
        className={styles.tips}
        src="https://img10.360buyimg.com/imgzone/jfs/t1/156041/1/40243/1022/65ae743bF5d618180/a038798e89d7df02.png"
      />
    </div>
  );
};
