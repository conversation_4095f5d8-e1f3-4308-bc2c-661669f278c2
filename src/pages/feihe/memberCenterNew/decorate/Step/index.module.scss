.steps {
  display: flex;
  justify-content: space-between;
  background: #fff;
  align-items: center;
  padding: 15px;
  margin-bottom: 20px;


  .step {
    background: #f4f6f9;
    width: 31%;
    padding: 15px;
    border-radius: 10px;
    position: relative;

    &:not(:last-child) {
      &:before {
        content: '1';
        color: #fff;

        position: absolute;
        width: 6.5%;
        height: 1px;
        border-bottom: 1px solid #d3d3d3;
        right: -9%;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    .index {
      display: inline-block;
      width: 20px;
      height: 20px;
      border-radius: 10px;
      background: #1677ff;
      color: white;
      font-weight: bold;
      text-align: center;
      line-height: 20px;
      margin-right: 8px;

    }


    .title {
      font-weight: bold;
      font-size: 14px;
    }
    .desc {
      color: gray;
      font-size: 12px;
      margin-left: 28px;
      margin-top: 8px;
      margin-bottom: 8px;
    }
    .info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .infoIcon {
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 4px solid transparent;
        border-bottom: 4px solid transparent;
        border-left: 4px solid #1677ff;
        margin-right: 5px;
      }

      i::before {
        font-size: 16px;
      }
    }
  }
}
