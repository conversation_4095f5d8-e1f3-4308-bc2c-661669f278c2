.previewContainer {
  border-radius: 5px;
  height: calc(100vh - 155px);
  overflow-y: scroll;
  flex-shrink: 0;
  padding: 0 16px 0 77px;
  position: sticky;
  top: 20px;
}

.previewContainer::-webkit-scrollbar {
  width: 0;
}
.previewName {
  background: #fff;
  position: relative;
  width: 375px;
  border: 2px solid var(--bg-color);
  padding: 6px;
  .moduleName {
    position: absolute;
    left: -80px;
    width: 70px;
    height: 18px;
    background: var(--bg-color);
    color: var(--font-color);
    font-size: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 5px;
    cursor: pointer;
  }
  &::after {
    content: ' ';
    position: absolute;
    top: 9.5px;
    left: -10px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent transparent var(--bg-color);
    z-index: 1;
  }
}
