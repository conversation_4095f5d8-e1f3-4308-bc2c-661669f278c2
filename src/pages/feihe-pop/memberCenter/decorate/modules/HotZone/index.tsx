import { popJsonSaveZphyJson } from '@/api/zphy';
import LzDialog from '@/components/LzDialog';
import LzImageSelector from '@/components/LzImageSelector';
import LzTipPanel from '@/components/LzTipPanel';
import EditHotZone from '@/pages/decorate/compoonets/EditHotZone';
import SettingPeople from '@/pages/decorate/compoonets/SettingPeople';
import { Button, Card, Divider, Message } from '@alifd/next';
import React, { useEffect } from 'react';
import styles from './index.module.scss';

const TIP = (
  <div>
    <div style={{ fontWeight: 'bold', fontSize: 14, marginBottom: 5 }}>说明: </div>
    <div>1.仅支持宽度为739px，高度为273px</div>
    <div>2.大小不超过1M图片类型为jpg、png</div>
  </div>
);

export default ({ data: dataList, dispatch, allJson }) => {
  const [pageLoading, setPageLoading] = React.useState(false);
  const [visible, setVisible] = React.useState(false);
  const [peopleVisible, setPeopleVisible] = React.useState(false);
  const [editData, setEditData] = React.useState({});
  const [editDataIndex, setEditDataIndex] = React.useState(0);

  const deleteHotZoneUrl = (dataIndex, hotZoneIndex) => {
    dataList[dataIndex].hotZoneList.splice(hotZoneIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: [...dataList] });
  };

  const deleteHotZone = (dataIndex) => {
    dataList.splice(dataIndex, 1);
    dispatch({ type: 'UPDATE_MODULE', payload: [...dataList] });
  };

  const addHotZone = (dataIndex) => {
    const data = dataList[dataIndex];
    if (!data.bg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditData(data);
    setEditDataIndex(dataIndex);
    setVisible(true);
  };

  const handleAddImage = () => {
    const updatedDataList = [...dataList];
    updatedDataList.push({ bg: '', hotZoneList: [] });
    dispatch({ type: 'UPDATE_MODULE', payload: updatedDataList });
  };
  const isValidSwiper = () => {
    let err = false;

    dataList.forEach((e) => {
      if (!e.bg) {
        err = true;
      }
      if (!e.hotZoneList.length) {
        err = true;
      }
    });
    if (err) {
      return false;
    }
    return true;
  };
  const saveSetting = (): any => {
    if (!dataList.length) {
      Message.error('请至少添加一张图片');
      return false;
    }
    if (!isValidSwiper()) {
      Message.error('请完善热区信息');
      return false;
    }
    let updateJson = {};
    if (allJson.showZhuoRui) {
      updateJson = {
        ...allJson,
        ZRNewMember: {
          ...allJson.ZRNewMember,
          hotZone: [...dataList],
        },
        ZROldMember: {
          ...allJson.ZROldMember,
          hotZone: [...dataList],
        },
      };
    } else {
      updateJson = {
        ...allJson,
        NewMember: {
          ...allJson.NewMember,
          hotZone: [...dataList],
        },
        OldMember: {
          ...allJson.OldMember,
          hotZone: [...dataList],
        },
      };
    }
    console.log('updateJson', updateJson);
    const params = {
      json: JSON.stringify(updateJson),
      type: 1,
      version: allJson.version,
    };
    setPageLoading(true);
    popJsonSaveZphyJson(params).then((res) => {
      Message.success('保存成功');
      setPageLoading(false);
    });
  };

  const commonProps = {
    title: '备用互动热区',
    extra: (
      <div>
        <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
          更新至当前设置
        </Button>
      </div>
    ),
  };
  useEffect(() => {
    // dataList = [
    //   {
    //     bg: 'https://img10.360buyimg.com/imgzone/jfs/t1/135277/39/40897/38789/65b05fefF9680011f/9766cf7f21cf2bee.png',
    //     hotZoneList: [
    //       {
    //         left: 2,
    //         top: 30.99999999999997,
    //         width: 178.76428411382474,
    //         height: 106.17321588617523,
    //         url: 'https://lives.jd.com/#/author?authorId=91714',
    //         rectId: '1706757890106',
    //         containerWidth: 375,
    //         containerHeight: 139,
    //       },
    //       {
    //         left: 187,
    //         top: 29,
    //         width: 187,
    //         height: 108.9375,
    //         url: 'http://lzdz1-isv.isvjcloud.com/m/1000003568/dz350deb3f2077400db9e6ad750b89/',
    //         rectId: '1706757920555',
    //         containerWidth: 375,
    //         containerHeight: 139,
    //       },
    //     ],
    //   },
    // ];
    // dispatch({ type: 'UPDATE_MODULE', payload: [...dataList] });
  }, []);
  return (
    <div>
      <Card free>
        <Card.Header {...commonProps} />
        <Divider />
        <Card.Content>
          <LzTipPanel message={TIP} />
          <Button className={styles.addBtn} onClick={handleAddImage} disabled={dataList.length >= 1}>
            添加图片（{dataList.length} / 1）
          </Button>

          <div className={styles.operation}>
            {dataList.map((data, dataIndex) => (
              <div className={styles.hotContainer} key={dataIndex}>
                <div className={styles.imgUpload}>
                  <div>
                    <LzImageSelector
                      width={750}
                      bgWidth={158}
                      value={data.bg}
                      onChange={(bg) => {
                        const updatedDataList = [...dataList];
                        updatedDataList[dataIndex].bg = bg;
                        updatedDataList[dataIndex].hotZoneList = [];
                        dispatch({ type: 'UPDATE_MODULE', payload: updatedDataList });
                      }}
                    />
                  </div>
                  <div className={styles.setting}>
                    <Button className={styles.btn} onClick={() => addHotZone(dataIndex)}>
                      添加热区
                    </Button>
                    {data.hotZoneList.map((item, hotZoneIndex) => (
                      <div key={hotZoneIndex} className={styles.urlContainer}>
                        <div className={styles.url}>{`热区0${hotZoneIndex + 1}：${item.url}`}</div>
                        <i
                          className="iconfont icon-icon-07 btn-del"
                          onClick={() => deleteHotZoneUrl(dataIndex, hotZoneIndex)}
                        />
                      </div>
                    ))}
                  </div>
                  <div className={styles.removeHotZone}>
                    <i className="iconfont icon-icon-07 btn-del" onClick={() => deleteHotZone(dataIndex)} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card.Content>
      </Card>
      <LzDialog
        title={'编辑热区'}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '750px' }}
      >
        <EditHotZone
          data={editData}
          dataList={dataList}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setVisible(false)}
        />
      </LzDialog>
      <LzDialog
        title={'人群限制'}
        visible={peopleVisible}
        footer={false}
        onClose={() => setPeopleVisible(false)}
        style={{ width: '500px' }}
      >
        <SettingPeople
          dataList={dataList}
          dataIndex={editDataIndex}
          dispatch={dispatch}
          onClose={() => setPeopleVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
