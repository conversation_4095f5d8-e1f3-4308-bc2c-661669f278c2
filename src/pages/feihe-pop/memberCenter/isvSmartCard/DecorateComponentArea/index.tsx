import React from 'react';
import styles from './index.module.scss';

const DecorateComponent = ({ state, dispatch, moduleName, activeKey }) => {
  const Components = state.previews[moduleName].decorateComponent;
  return (
    <Components
      activeKey={activeKey}
      data={state.modules[moduleName]}
      operationKey={state.key}
      dispatch={dispatch}
      defaultData={state.defaultValue}
      defaultValueData={state.defaultValueData}
    />
  );
};

function DecorateComponentArea({ selectedModule, state, dispatch, activeKey }) {
  return (
    <div className={styles.decorateComponent}>
      <DecorateComponent state={state} dispatch={dispatch} activeKey={activeKey} moduleName={selectedModule} />
    </div>
  );
}

export default DecorateComponentArea;
