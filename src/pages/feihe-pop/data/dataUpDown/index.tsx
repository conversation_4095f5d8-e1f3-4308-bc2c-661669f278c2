import React from 'react';
import LzPanel from '@/components/LzPanel';
import { Form, DatePicker2, Table, Button, Radio } from '@alifd/next';
import { memberPopDataGetMemberGradeList, memberPopDataMemberGradeListExport } from '@/api/firmus';
import { downloadExcel } from '@/utils';
import LzMsg from '@/components/LzMsg';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;

export default () => {
  const [upData, setUpData] = React.useState<any[]>([]);
  const [downData, setDownData] = React.useState<any[]>([]);
  const [rangeDate, setRangeDate] = React.useState<any[]>([dayjs().subtract(30, 'day'), dayjs().add(0, 'day')]);
  const [loading, setLoading] = React.useState<boolean>(false);
  const [type, setType] = React.useState<number>(1);

  const fetchData = async (value) => {
    setLoading(true);
    const [startDate, endDate] = rangeDate;
    try {
      const { downGrade, upGrade } = await memberPopDataGetMemberGradeList({
        type: value,
        startDate,
        endDate,
      });
      if (value === 1) {
        setUpData(upGrade!);
      } else {
        setDownData(downGrade!);
      }
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const init = () => {
    fetchData(type).then();
  };
  const download = (value) => {
    const name = value === 1 ? '升级数据' : '降级数据';
    const [startDate, endDate] = rangeDate;
    memberPopDataMemberGradeListExport({
      startDate,
      endDate,
      type: value,
    }).then((data: any) => downloadExcel(data, name));
  };

  const handleRadioChange = (val) => {
    setType(val);
    fetchData(val).then();
  };

  React.useEffect(() => {
    init();
  }, []);

  return (
    <div>
      <LzPanel title={'卓睿/非卓睿升降级数据'}>
        <Form colon inline>
          <Form.Item label={'选择时间'}>
            <RangePicker
              value={rangeDate}
              onChange={(val) => {
                const format = 'YYYY-MM-DD';
                const formatVal = val.map((item) => item.format(format));
                setRangeDate(formatVal);
              }}
            />
          </Form.Item>
          <Form.Item label={'升/降级'}>
            <Radio.Group value={type} onChange={(val) => handleRadioChange(val)}>
              <Radio value={1}>升级</Radio>
              <Radio value={2}>降级</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item>
            <Button type={'primary'} onClick={init}>
              查询
            </Button>
            <Button type={'primary'} onClick={() => download(type)}>
              导出
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
      {type === 1 && (
        <LzPanel>
          <Table dataSource={upData} loading={loading}>
            <Table.Column dataIndex={'type'} />
            <Table.Column title={'时间段前365天仅购买非卓睿的用户数'} dataIndex={'userCountHis'} />
            <Table.Column title={'时间段前365天仅购买非卓睿系列且时间段购买卓睿的用户数'} dataIndex={'userCount'} />
            <Table.Column title={'升级率'} dataIndex={'rate'} />
          </Table>
        </LzPanel>
      )}
      {type === 2 && (
        <LzPanel>
          <Table dataSource={downData} loading={loading}>
            <Table.Column dataIndex={'type'} />
            <Table.Column title={'时间段前365天仅购买卓睿的用户数'} dataIndex={'userCountHis'} />
            <Table.Column title={'时间段前365天仅购买卓睿系列且时间段购买非卓睿的用户数'} dataIndex={'userCount'} />
            <Table.Column title={'降级率'} dataIndex={'rate'} />
          </Table>
        </LzPanel>
      )}
    </div>
  );
};
