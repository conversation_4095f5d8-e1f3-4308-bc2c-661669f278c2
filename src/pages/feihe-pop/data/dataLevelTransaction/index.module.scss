.row {
  display: flex;
  align-content: center;
}

.cell {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  width: 100%;
  padding: 5px 10px;

  &:nth-child(1) {
    flex: 0.05;
  }

  &:nth-child(2) {
    flex: 0.475;
    border-left: 1px solid #000;
    border-right: 1px solid #000;
  }

  &:nth-child(3) {
    flex: 0.475;
  }
}

.textCenter {
  justify-content: center;
  font-size: 24px;
  color: rgba(236, 128, 141, 0.7);
}

.label {
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
