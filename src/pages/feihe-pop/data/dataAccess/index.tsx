import { memberPopDataGetMemberAccessGmvList, memberPopDataMemberAccessGmvListExport } from '@/api/firmus';
import { Pager } from '@/components/LzPagination';
import LzPanel from '@/components/LzPanel';
import { FormLayout } from '@/pages/activity/10021/1001/util';
import CompareItem from '@/pages/feihe/components/CompareItem/CompareItem';
import { typeGroup } from '@/pages/feihe/data/dataTransaction';
import { downloadExcel } from '@/utils';
import format from '@/utils/format';
import { Button, DatePicker2, Field, Form, Radio, Select, Table } from '@alifd/next';
import dayjs from 'dayjs';
import React, { useEffect, useReducer, useState } from 'react';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default function DataTransaction() {
  const field = Field.useField();

  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    type: 1, // 1：环比；2：同比
    priceType: 1,
    startDate: dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD'),
  });
  const [compareColumnTitle, setCompareColumnTitle] = useState('较前一期');
  const [tableData, setTableData] = useState<any[]>([]);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  const [loading, setLoading] = useState(false);
  const onDataRangeChange = (rangeDate): void => {
    setParams({
      startDate: format.formatDateTimeDayjs(rangeDate[0]),
      endDate: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };

  const getList = async ({ pageSize, pageNum }) => {
    try {
      setLoading(true);
      const res: any = await memberPopDataGetMemberAccessGmvList({
        ...params,
        pageSize,
        pageNum,
      });
      for (let i = 0; i < res.length; i++) {
        res[i].num = i + 1;
      }
      setTableData(res as any[]);
      // pageInfo.total = +res.total!;
      // pageInfo.pageSize = +res.size!;
      // pageInfo.pageNum = +res.current!;
      setPageInfo(pageInfo);
      setLoading(false);
    } catch (error) {
      console.error(error);
    }
  };

  const handleSubmit = () => {
    getList({ pageNum: 1, pageSize: pageInfo.pageSize });
    setCompareColumnTitle(typeGroup.find((item) => item.value === params.type)?.title || '');
  };

  const exportData = () => {
    memberPopDataMemberAccessGmvListExport(params).then((data: any) => downloadExcel(data, `会员中心访问成交数据`));
  };

  useEffect(() => {
    getList({ pageNum: 1, pageSize: pageInfo.pageSize });
  }, []);

  return (
    <div>
      <LzPanel>
        <h1>会员中心访问成交数据</h1>
        <Form className="lz-query-criteria" field={field} {...formItemLayout} style={{ marginBottom: 20 }}>
          <FormItem name="rights" label="价格口径">
            <Select
              value={params.priceType}
              hasClear
              followTrigger
              onChange={(priceType) => {
                setParams({ priceType });
              }}
            >
              <Select.Option value={1}>京东价</Select.Option>
              <Select.Option value={2}>实付价</Select.Option>
            </Select>
          </FormItem>
          <FormItem name="dateRange" label="选择时间">
            <RangePicker
              value={[params.startDate, params.endDate]}
              format="YYYY-MM-DD"
              hasClear={false}
              onChange={(value) => {
                onDataRangeChange(value);
              }}
            />
          </FormItem>
          <FormItem name="type">
            <Radio.Group
              dataSource={typeGroup}
              shape="button"
              value={params.type}
              onChange={(type) => {
                setParams({ type });
              }}
            />
          </FormItem>
          <FormItem colon={false}>
            <Button type="primary" onClick={handleSubmit}>
              查询
            </Button>
            <Button type="primary" onClick={exportData}>
              导出
            </Button>
          </FormItem>
        </Form>
        <Table dataSource={tableData} loading={loading}>
          <Table.Column width={80} title="序号" dataIndex="num" />
          <Table.Column title="数据维度" dataIndex="name" />
          <Table.Column title="整体" dataIndex="all" />
          <Table.Column
            title={compareColumnTitle}
            dataIndex="allCompare"
            cell={(value, index, row) => {
              return <CompareItem data={value} />;
            }}
          />
          <Table.Column title="新客" dataIndex="newCustomer" />
          <Table.Column
            title={compareColumnTitle}
            dataIndex="newCustomerCompare"
            cell={(value, index, row) => {
              return <CompareItem data={value} />;
            }}
          />
          <Table.Column title="老客" dataIndex="oldCustomer" />
          <Table.Column
            title={compareColumnTitle}
            dataIndex="oldCustomerCompare"
            cell={(value, index, row) => {
              return <CompareItem data={value} />;
            }}
          />
        </Table>
        {/* <LzPagination */}
        {/*  pageNum={pageInfo.pageNum} */}
        {/*  pageSize={pageInfo.pageSize} */}
        {/*  total={pageInfo.total} */}
        {/*  onChange={handlePage} */}
        {/* /> */}
      </LzPanel>
    </div>
  );
}
