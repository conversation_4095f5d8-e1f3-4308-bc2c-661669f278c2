import React from 'react';
import styles from './index.module.scss';

export default function RulePopup({ state, CloseButton }) {
  const {
    selectedModule,
    popupData,
    modules,
    modules: {
      KvSwiper: {
        jsonData: { rulePopupBgImg, rulePopupTextColor },
      },
    },
  } = state;

  const rule = popupData?.rule || modules[selectedModule].rule || '这里是活动规则文本';

  return (
    <div className={`${styles['popup-container']}`} style={{ backgroundImage: `url(${rulePopupBgImg})` }}>
      <div className={`${styles['popup-content']} no-scrollbar`}>
        <div className={`${styles['rule-text']} no-scrollbar`} style={{ color: `${rulePopupTextColor}` }}>
          {rule}
        </div>
      </div>
      <CloseButton />
    </div>
  );
}
