.video-swiper {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 0.3em;
}

.video-swiper-title {
  width: 100%;
  height: 0.86em;
  background-repeat: no-repeat;
  background-position: center;
  background-size: auto 100%;
}

.video-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 7.36em;
  height: 4.22em;
  padding-bottom: 0.1em;
  background-repeat: no-repeat;
  background-position: center;
  background-size: auto 100%;
}

.video-item {
  display: flex;
  justify-content: center;
}

.video-player {
  width: 6.5em;
  height: 3.6em;
  background-color: transparent;
  border-radius: 0.1em;
  .video-poster {
    width: 100%;
    height: 100%;
  }
}
