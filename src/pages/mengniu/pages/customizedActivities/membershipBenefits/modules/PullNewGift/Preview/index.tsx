import classNames from 'classnames';
import groupBy from 'lodash/groupBy';
import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import styles from './index.module.scss';

export default function PullNewGiftPreview({ data, state, dispatch }) {
  if (!data) return null;

  const { memberLevel } = state;

  const {
    jsonData,
    activityData: { prizeGroupList },
  } = data;
  const { CommonSetting } = state.modules;

  const { prizeList = [] } = prizeGroupList[0] || {};

  const prizeGroupListByIndex = groupBy(prizeList, 'index');
  const prizeGroupList_format = Object.values(prizeGroupListByIndex);

  const bgHeight = (len: any) => {
    let newheight = '';
    if (len === 2) {
      newheight = '11.3295em'; // 10.79*1.05
    } else if (len === 3) {
      newheight = '15.519em'; // 14.78*1.05
    } else {
      newheight = '6.552em'; // 6.24*1.05
    }
    return newheight;
  };

  return (
    <div
      className={styles['pullnew-gift']}
      style={{
        backgroundImage: `url(${
          jsonData[`pullNewbgImg${prizeGroupList_format.length === 0 ? 1 : prizeGroupList_format.length}`]
        })`,
        height: bgHeight(prizeGroupList_format.length),
      }}
    >
      {/* 活动规则 */}
      <img
        className={styles['pullnew-rule-button']}
        src={CommonSetting.jsonData.ruleButtonImg}
        onClick={(e) => {
          if (state.selectedModule != 'PullNewGift') return;
          dispatch({
            type: 'SHOW_POPUP',
            payload: {
              popupName: 'RulePopup',
              popupData: {},
            },
          });
        }}
      />
      {/* 邀新纪录 */}
      <img
        className={styles['pullnew-share-button']}
        src={CommonSetting.jsonData.shareButtonImg}
        onClick={(e) => {
          if (state.selectedModule != 'PullNewGift') return;
          dispatch({
            type: 'SHOW_POPUP',
            payload: {
              popupName: 'MySharePopup',
              popupData: {},
            },
          });
        }}
      />
      {/* 领奖纪录 */}
      <img
        className={styles['pullnew-record-button']}
        src={CommonSetting.jsonData.myPrizeButtonImg}
        onClick={(e) => {
          if (state.selectedModule != 'PullNewGift') return;
          dispatch({
            type: 'SHOW_POPUP',
            payload: {
              popupName: 'MyPrizePopup',
              popupData: {},
            },
          });
        }}
      />
      {/* 奖品列表 */}
      {prizeGroupList_format.map((item, index) => (
        <Swiper
          key={index}
          loop
          slidesPerView={3}
          autoplay={{ delay: 2500, disableOnInteraction: false }}
          className={`${styles['prize-list']} ${styles[`prize-list-${index + 1}`]}`}
        >
          {item.map((item2, index2) => (
            <SwiperSlide key={index2} className={`${styles['prize-slide']}`}>
              <div className={`${styles['prize-bg']}`} style={{ backgroundImage: `url(${jsonData.prizeBgImg}})` }}>
                <div className={`${styles['prize-info']}`}>
                  <div className={`${styles['level']}`} style={{ color: `${jsonData.memberLevelTextColor}` }}>
                    <span className="lz-multi-ellipsis--l1">
                      {memberLevel.find((item3) => item3.levelId == item2.vipLevel).levelName}
                    </span>
                  </div>
                  <div className={`${styles['prize-name']}`} style={{ color: `${jsonData.prizeNameTextColor}` }}>
                    <span className="lz-multi-ellipsis--l1">{item2.prizeName}</span>
                  </div>
                  {item2.prizeImg ? (
                    <img className={styles['prize-img']} src={item2.prizeImg} />
                  ) : (
                    <div className={styles['prize-img']}>{/*  */}</div>
                  )}
                  <div
                    className={`${styles['get-button']}`}
                    style={{ backgroundImage: `url(${jsonData.receiveButtonImg})` }}
                    onClick={(e) => {
                      if (state.selectedModule != 'PullNewGift') return;
                      dispatch({
                        type: 'SHOW_POPUP',
                        payload: {
                          popupName: 'DrawResPopup',
                          popupData: {},
                        },
                      });
                    }}
                  />
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      ))}
      {/* 立即邀请 */}
      <img
        className={classNames(styles['share-button'], {
          [styles['oneBottom']]: prizeGroupList_format.length === 1,
        })}
        src={jsonData.shareButtonImg}
      />
    </div>
  );
}
