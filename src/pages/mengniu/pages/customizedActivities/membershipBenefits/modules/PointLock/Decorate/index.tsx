import { saveInfo } from '@/api/chunzhengift';
import RuleSetting from '@/pages/mengniu/components/RuleSetting';
import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import { showValidateMessage } from '@/pages/mengniu/utils';
import { delay } from '@/utils';
import { Button, Card, Dialog, Divider, Field, Loading, Message } from '@alifd/next';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import cloneDeep from 'lodash/cloneDeep';
import React, { useState } from 'react';
import BaseInfo from './components/BaseInfo';
import DecorationSetting from './components/DecorationSetting';
import JoinRule from './components/JoinRule';

dayjs.extend(isBetween);

export default function PointLockDecorate({ state, data, resetData, dispatch, moduleName, getModuleData }) {
  if (!data) return null;

  const field = Field.useField();

  const updateCurrentModuleData = (value) => {
    dispatch({
      type: 'UPDATE_MODULE',
      payload: {
        [moduleName]: {
          ...data,
          ...value,
        },
      },
    });
  };

  const [pageLoading, setPageLoading] = useState(false);

  const saveSetting = () => {
    field.validate((errors, values) => {
      if (errors) {
        return showValidateMessage(errors);
      }

      const submitData = cloneDeep(data);

      submitData.activityId = state.activityId;
      submitData.type = state.previews[moduleName].type;
      submitData.jsonData = JSON.stringify(submitData.jsonData);
      submitData.activityData.rangeDate = [submitData.activityStartTime, submitData.activityEndTime];
      submitData.activityData.activityName = '纯甄会员礼遇积分锁权';
      submitData.activityData.startTime = data.activityStartTime;
      submitData.activityData.endTime = data.activityEndTime;
      const groupId = submitData.activityData.prizeGroupList.find((item) => item.groupId != '')?.groupId;

      try {
        submitData.activityData.prizeGroupList.forEach((activity) => {
          activity.prizeList.forEach((prize, prizeIndex, prizeList) => {
            prize.unitPrice *= 100;
            prize.prizeInfoJson = JSON.stringify(prize.prizeInfoJson);
            prize.prizeGroupId = groupId;
          });
        });
      } catch (error) {
        console.error('ERROR PointLock saveSetting', error);
      }

      if (submitData.drawId === 0) {
        submitData.drawId = null;
      }
      if (submitData.decoData === '') {
        submitData.decoData = '{"background-color": "#F00"}';
      }
      if (submitData.activityMainId === 0) {
        submitData.drawId = null;
      }
      if (!submitData.prizeInfoJsonList) {
        submitData.prizeInfoJsonList = '{}';
      }

      console.log(`${state.previews[moduleName].name} - 保存数据`, submitData);

      const isWithinActivityTime = dayjs().isBetween(
        dayjs(submitData.showStartTime),
        dayjs(submitData.showEndTime),
        null,
        '[]',
      );

      Dialog.confirm({
        title: '提示',
        content: isWithinActivityTime
          ? '当前活动进行中，修改活动可能导致正参与的用户因机制改变而失去参与资格，有客诉风险，确认继续保存修改吗？'
          : `确认保存吗？`,
        onOk: async () => {
          setPageLoading(true);
          try {
            await saveInfo(submitData);
            Message.success('保存成功');
            // 重新拉取模块数据
            await delay(1000);
            getModuleData.get_PointLock();
          } catch (e) {
            Message.error(e.message);
          } finally {
            setPageLoading(false);
          }
        },
      });
    });
  };

  const settingProps = { data, resetData, updateCurrentModuleData, field, state, dispatch };

  return (
    <Loading tip="加载中..." fullScreen visible={pageLoading}>
      <Card free className={decorateStyles['decorate-container']}>
        <Card.Header
          title={<span className="crm-label">积分锁权</span>}
          extra={
            <Button type="primary" onClick={saveSetting} disabled={pageLoading}>
              保存并发布
            </Button>
          }
        />
        <Divider />
        <Card.Content>
          {/* 活动基本信息 */}
          <BaseInfo {...settingProps} />
          {/* 参与规则 */}
          <JoinRule {...settingProps} />
          {/* 装修设置 */}
          <DecorationSetting {...settingProps} />
          {/* 规则设置 */}
          <RuleSetting {...settingProps} />
        </Card.Content>
      </Card>
    </Loading>
  );
}
