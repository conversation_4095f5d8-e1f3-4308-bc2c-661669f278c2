import decorateStyles from '@/pages/mengniu/public/styles/decorate.module.scss';
import { Box, Button, Input } from '@alifd/next';
import React from 'react';

export default function TextSetting({ value, maxLength, onChange, onReset }) {
  return (
    <Box direction="row" align="center">
      <Input className={decorateStyles['text-input']} value={value} trim onChange={onChange} maxLength={maxLength} />
      <Button text type="primary" className={decorateStyles['reset-button']} onClick={onReset}>
        重置
      </Button>
    </Box>
  );
}
