import React, { useEffect, useReducer, useState } from 'react';
import { <PERSON><PERSON>, Dialog, Loading } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import { FormLayout } from '@/pages/activity/10021/1001/util';
import ChooseGoods from '@/components/ChooseGoods';

import { memberGetSkuList, memberSaveSkuList } from '@/api/babycare';
import styles from '../style.module.scss';
import { deepCopy } from '@/utils';
import { POPUP_OBJECT } from '../../util';

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ closeDialog, showDialog }) => {
  const [loading, setLoading] = useState(false);
  const [dataList, setDataList] = useState([]);
  const [skuList, setSkuList] = useState([]);
  const [currentTabKey, setCurrentTabKey] = useState(5);

  const getMemberGetSkuData = async () => {
    try {
      setLoading(true);
      const data = await memberGetSkuList();
      setDataList(data ?? []);
      const sku = data.find((i: any) => i?.monthOldType === currentTabKey) ?? [];
      setSkuList(deepCopy(sku?.skuDtos));
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const saveSkuList = async () => {
    try {
      setLoading(true);
      dataList.forEach((i: any) => {
        i.skuDtos = i.skuDtos.map((x: any) => x.skuId).join(',');
      });
      setDataList(deepCopy(dataList));
      console.log(dataList);
      await memberSaveSkuList({ skuDtoList: dataList });
      LzMsg.success('保存成功');
      closeDialog(false);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (showDialog) {
      getMemberGetSkuData().then();
    }
  }, [showDialog]);

  useEffect(() => {
    const data = dataList.find((i: any) => i?.monthOldType === currentTabKey);
    setSkuList(deepCopy(data?.skuDtos));
  }, [currentTabKey]);

  // 活动主页
  return (
    <>
      <Dialog
        v2
        centered
        width={1000}
        height={600}
        title="编辑-推荐商品"
        footer={false}
        visible={showDialog}
        onClose={() => closeDialog(false)}
      >
        <Loading visible={loading} style={{ display: 'block' }}>
          <div className={styles.dialogContent}>
            <div className={styles.headerView}>
              <span className={styles.tip}>注：最多添加50个推荐商品</span>
              <ChooseGoods
                max={50}
                value={skuList}
                onChange={(data) => {
                  setSkuList(deepCopy(data));
                  const currentIndex = dataList.findIndex((i: any) => i.monthOldType === currentTabKey);
                  dataList[currentIndex].skuDtos = data;
                  setDataList(deepCopy(dataList));
                }}
              />
            </div>

            <div className={styles.tabView}>
              {POPUP_OBJECT &&
                POPUP_OBJECT.map((item: any) => (
                  <div
                    className={currentTabKey === Number(item.value) ? styles.tabItemActive : styles.tabItem}
                    onClick={() => setCurrentTabKey(Number(item.value))}
                  >
                    {item.label}
                  </div>
                ))}
            </div>

            <div className={styles.skuView}>
              {skuList &&
                skuList.map((item: any) => (
                  <div className={styles.skuItem}>
                    <img src={item.skuImg ?? item.skuMainPicture} style={{ width: '132px' }} alt="" />
                  </div>
                ))}
            </div>

            <div className={styles.footer}>
              <Button type={'primary'} onClick={() => saveSkuList()}>
                保存
              </Button>
              <Button onClick={() => closeDialog(false)}>取消</Button>
            </div>
          </div>
        </Loading>
      </Dialog>
    </>
  );
};
