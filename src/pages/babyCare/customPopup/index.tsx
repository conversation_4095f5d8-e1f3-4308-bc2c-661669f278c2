import React, { useEffect, useReducer, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Select, Table, Loading } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import LzTag from '@/components/LzTag';
import { memberGetCustomPopPage, memberStopCustomPop } from '@/api/babycare';
import { IPageCustomizedActivityPageResponse } from '@/api/types';
import LzPanel from '@/components/LzPanel';
// import styles from './style.module.scss';
import dayjs from 'dayjs';
import constants from '@/utils/constant';
import LzPagination, { Pager } from '@/components/LzPagination';
import { FormLayout } from '@/pages/activity/10021/1001/util';
import { Link } from 'ice';
import { history } from 'create-app-shared';

const FormItem = Form.Item;

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

interface List {
  popName: string;
  monthOldType: string;
  popId: number;
  createTime: string;
  popEndTime: string;
  popStatrTime: string;
  popTimes: number;
  status: number;
}

export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<List[]>([]);
  // 分页数据
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);

  // 请求数据
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    popName: '',
    status: 0,
    ...initPager,
  });

  const handleSubmit = () => {
    setParams({ ...params, pageNum: 1, pageSize: params.pageSize });
    setPageInfo({ ...pageInfo, pageNum: 1 });
    getList({ ...params }).then();
  };

  const handleReset = () => {
    setParams({ popName: '', status: 0, startTime: '', endTime: '', pageNum: 1, pageSize: params.pageSize });
    getList({ popName: '', status: 0, startTime: '', endTime: '', pageNum: 1, pageSize: params.pageSize }).then();
  };

  const getList = async (query) => {
    const postData = {
      ...params,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
      ...query,
    };
    try {
      setLoading(true);
      const result: IPageCustomizedActivityPageResponse = await memberGetCustomPopPage(postData);
      setList((result.records as any) || []);
      setPageInfo({
        total: result.total,
        pageNum: result.current,
        pageSize: result.size,
      } as any);
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const forwardFinish = async (record: any) => {
    try {
      setLoading(true);
      await memberStopCustomPop({ popId: record.popId });
      getList({ ...params, ...pageInfo }).then();
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handlePage = ({ pageSize, pageNum }) => {
    setPageInfo({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    getList({ ...formValue, pageSize, pageNum }).then();
  };

  useEffect(() => {
    setList([]);
    getList({ ...pageInfo, pageNum: 1 }).then();
  }, []);

  const handleDateChange = (dates) => {
    setParams({
      startTime: dates[0].format('YYYY-MM-DD'),
      endTime: dates[1].format('YYYY-MM-DD'),
    });
  };

  const activityTimeCell = (value, index, record) => {
    if (record.popStatrTime && record.popEndTime) {
      return (
        <div>
          <div>起：{dayjs(record.popStatrTime).format(constants.DATE_FORMAT_TEMPLATE)}</div>
          <div>止：{dayjs(record.popEndTime).format(constants.DATE_FORMAT_TEMPLATE)}</div>
        </div>
      );
    } else {
      return <div>-</div>;
    }
  };
  const createTimeCell = (value) => {
    return <div>{dayjs(value).format(constants.DATE_FORMAT_TEMPLATE)}</div>;
  };

  // 跳转页面按钮点击事件
  // const onJumpButtonClick = (link, searchObj: Record<string, any>) => {
  //   const query = QS.stringify(searchObj);
  //   if (link.includes('?')) {
  //     appHistory.push(`${link}&${query}`);
  //   } else {
  //     appHistory.push(`${link}?${query}`);
  //   }
  // };

  // 跳转新建 | 编辑页面
  const jumpCreatePage = (record) => {
    console.log(record);
    history?.push(`/babyCare/pages/creatCustomPopup?popId=${record.popId}&status=${record.status}`);
  };

  const operateCell = (value, index, record) => {
    return (
      <div style={{ display: 'flex', columnGap: 10 }}>
        <div>
          {(record.status === 1 || record.status === 2 || record.status === 3) && (
            <Button type="primary" text onClick={() => jumpCreatePage(record)}>
              查看
            </Button>
          )}
        </div>
        <div>
          {record.status === 1 && (
            <Button type="primary" text onClick={() => forwardFinish(record)}>
              提前结束
            </Button>
          )}
        </div>
      </div>
    );
  };

  return (
    <div>
      <Loading visible={loading} style={{ display: 'block' }}>
        <LzPanel>
          <h2>自定义弹窗</h2>
          <Form className="lz-query-criteria" field={field} labelAlign={'top'}>
            <FormItem label="弹窗名称" labelAlign="left">
              <Input
                value={params.popName}
                trim
                hasClear
                maxLength={50}
                placeholder="请输入弹窗名称"
                onChange={(popName) => {
                  setParams({ popName });
                }}
              />
            </FormItem>
            <FormItem labelAlign="left" name="status" label="弹窗状态">
              <Select
                hasClear
                followTrigger
                value={params.status}
                defaultValue={0}
                onChange={(status: number) => {
                  setParams({ status });
                }}
              >
                <Select.Option value={0}>全部</Select.Option>
                <Select.Option value={1}>进行中</Select.Option>
                <Select.Option value={2}>未开始</Select.Option>
                <Select.Option value={3}>已结束</Select.Option>
              </Select>
            </FormItem>
            <Form.Item name="creatTime" labelAlign="left" label="创建时间">
              <DatePicker2.RangePicker hasClear={false} onChange={handleDateChange} />
            </Form.Item>
            <FormItem colon={false}>
              <Form.Submit type="primary" onClick={handleSubmit}>
                查询
              </Form.Submit>
              <Form.Reset
                onClick={() => {
                  handleReset();
                }}
              >
                重置
              </Form.Reset>
              <Button type="primary">
                <Link to="/babyCare/pages/creatCustomPopup" style={{ color: '#FFFFFF' }}>
                  新建
                </Link>
              </Button>
            </FormItem>
          </Form>
        </LzPanel>
        <LzPanel>
          <Table dataSource={list} loading={loading}>
            <Table.Column title="弹窗名称" dataIndex="popName" />
            <Table.Column title="创建时间" dataIndex="createTime" cell={createTimeCell} />
            <Table.Column title="弹窗对象" dataIndex="monthOldType" />
            <Table.Column
              title="弹窗状态"
              cell={(value, index, data) => (
                <span>
                  {
                    [
                      '',
                      <LzTag.Success>进行中</LzTag.Success>,
                      <LzTag.Warning>未开始</LzTag.Warning>,
                      <LzTag.Error>已结束</LzTag.Error>,
                    ][data.status]
                  }
                </span>
              )}
            />
            <Table.Column title="弹出时间" dataIndex="popStatrTime" cell={activityTimeCell} />
            <Table.Column title="弹窗次数" dataIndex="popTimes" />

            <Table.Column title="操作" cell={operateCell} />
          </Table>
          <LzPagination
            total={pageInfo.total}
            pageNum={pageInfo.pageNum}
            pageSize={pageInfo.pageSize}
            onChange={handlePage}
          />
        </LzPanel>
      </Loading>
    </div>
  );
};
