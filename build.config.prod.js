module.exports = {
  define: {
    env: process.env.NODE_ENV,
  },
  vite: false,
  hash: true,
  minify: true,
  publicPath: '/apps/custom/',
  outputDir: 'dist',
  eslint: false,
  dropLogLevel: 'log',
  outputAssetsPath: {
    js: 'js',
    css: 'css',
  },
  externals: {
    react: 'React',
    'react-dom': 'ReactDOM',
    '@alifd/next': 'Next',
    moment: 'moment',
  },
  plugins: [
    [
      'build-plugin-icestark',
      {
        umd: true,
        type: 'child',
        uniqueName: 'custom',
      },
    ],
    './build.router.plugin.js',
    './build.plugin.js',
  ],
};
